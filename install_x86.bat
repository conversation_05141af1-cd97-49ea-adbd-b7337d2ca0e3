@echo off
chcp 65001 >nul
echo.
echo ========================================================
echo   Subtitle Reader - Automatic<PERSON><PERSON> in<PERSON> pre x86
echo ========================================================
echo.

:: Kontrola architektúry
echo 🔍 Kontrolujem architektúru systému...
if "%PROCESSOR_ARCHITECTURE%"=="AMD64" (
    echo ✅ Detekovaná x86_64 architektúra
) else if "%PROCESSOR_ARCHITECTURE%"=="x86" (
    echo ✅ Detekovaná x86 architektúra
) else (
    echo ❌ Nepodporovaná architektúra: %PROCESSOR_ARCHITECTURE%
    echo    Tento inštalátor je určený pre x86/x64 systémy
    pause
    exit /b 1
)

:: Kontrola Windows verzie
echo 🔍 Kontrolujem Windows verziu...
ver | findstr /i "10\|11" >nul
if errorlevel 1 (
    echo ⚠️ Varovanie: Aplikácia je testovaná na Windows 10/11
    echo    Pokračujem v inštalácii...
) else (
    echo ✅ Windows 10/11 detekovaný
)

:: Kontrola Python
echo.
echo 🐍 Kontrolujem Python inštaláciu...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python nie je nainštalovaný alebo nie je v PATH
    echo.
    echo 📥 Sťahujem a inštalujem Python 3.11...
    echo    Prosím čakajte, môže to trvať niekoľko minút...
    
    :: Stiahni Python installer
    powershell -Command "Invoke-WebRequest -Uri 'https://www.python.org/ftp/python/3.11.9/python-3.11.9-amd64.exe' -OutFile 'python_installer.exe'"
    
    if exist python_installer.exe (
        echo 🔧 Spúšťam Python inštalátor...
        python_installer.exe /quiet InstallAllUsers=1 PrependPath=1 Include_test=0
        
        :: Čakaj na dokončenie inštalácie
        timeout /t 30 /nobreak >nul
        
        :: Vymaž inštalátor
        del python_installer.exe
        
        :: Refresh PATH
        call refreshenv.cmd 2>nul || echo Prosím reštartujte Command Prompt a spustite script znovu
        
        echo ✅ Python nainštalovaný
    ) else (
        echo ❌ Nepodarilo sa stiahnuť Python inštalátor
        echo    Prosím nainštalujte Python manuálne z https://python.org
        pause
        exit /b 1
    )
) else (
    python --version
    echo ✅ Python je dostupný
)

:: Kontrola pip
echo.
echo 📦 Kontrolujem pip...
python -m pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ pip nie je dostupný
    echo 🔧 Inštalujem pip...
    python -m ensurepip --upgrade
) else (
    echo ✅ pip je dostupný
)

:: Vytvorenie virtual environment
echo.
echo 🔧 Vytváram virtuálne prostredie...
if exist .venv (
    echo ⚠️ Virtuálne prostredie už existuje, odstraňujem...
    rmdir /s /q .venv
)

python -m venv .venv
if errorlevel 1 (
    echo ❌ Nepodarilo sa vytvoriť virtuálne prostredie
    pause
    exit /b 1
)

echo ✅ Virtuálne prostredie vytvorené

:: Aktivácia virtual environment
echo.
echo 🔧 Aktivujem virtuálne prostredie...
call .venv\Scripts\activate.bat
if errorlevel 1 (
    echo ❌ Nepodarilo sa aktivovať virtuálne prostredie
    pause
    exit /b 1
)

:: Upgrade pip
echo.
echo 📦 Aktualizujem pip...
python -m pip install --upgrade pip

:: Inštalácia základných závislostí
echo.
echo 📦 Inštalujem základné závislosti...
python -m pip install wheel setuptools

:: Inštalácia hlavných závislostí
echo.
echo 📦 Inštalujem hlavné závislosti (môže trvať niekoľko minút)...

:: GUI framework
echo   🖥️ PyQt6...
python -m pip install PyQt6 PyQt6-tools

:: OCR dependencies
echo   🔍 OCR závislosti...
python -m pip install pytesseract Pillow

:: TTS dependencies
echo   🔊 TTS závislosti...
python -m pip install pyttsx3

:: Windows-specific
echo   🪟 Windows závislosti...
python -m pip install pywin32 WMI

:: Utility libraries
echo   🛠️ Utility knižnice...
python -m pip install pynput mss packaging six

:: Text processing
echo   📝 Text processing...
python -m pip install RapidFuzz Levenshtein pyperclip

:: Web components
echo   🌐 Web komponenty...
python -m pip install pywebview requests

:: Windows Runtime (OneCore TTS)
echo   🎤 Windows Runtime (OneCore TTS)...
python -m pip install winrt-Windows.Foundation winrt-Windows.Media.SpeechSynthesis winrt-Windows.Storage.Streams

echo ✅ Všetky Python závislosti nainštalované

:: Kontrola Tesseract
echo.
echo 🔍 Kontrolujem Tesseract OCR...
tesseract --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Tesseract OCR nie je nainštalovaný
    echo.
    echo 📥 Sťahujem Tesseract OCR...
    
    :: Stiahni Tesseract installer
    powershell -Command "Invoke-WebRequest -Uri 'https://github.com/UB-Mannheim/tesseract/releases/download/v5.3.3.20231005/tesseract-ocr-w64-setup-5.3.3.20231005.exe' -OutFile 'tesseract_installer.exe'"
    
    if exist tesseract_installer.exe (
        echo 🔧 Spúšťam Tesseract inštalátor...
        echo    Prosím postupujte podľa inštalačného sprievodcu
        tesseract_installer.exe
        
        echo ⏳ Čakám na dokončenie inštalácie Tesseract...
        echo    Stlačte Enter po dokončení inštalácie...
        pause >nul
        
        :: Vymaž inštalátor
        del tesseract_installer.exe
        
        :: Nastav environment variable
        setx TESSERACT_CMD "C:\Program Files\Tesseract-OCR\tesseract.exe"
        set TESSERACT_CMD=C:\Program Files\Tesseract-OCR\tesseract.exe
        
        echo ✅ Tesseract OCR nainštalovaný
    ) else (
        echo ❌ Nepodarilo sa stiahnuť Tesseract inštalátor
        echo    Prosím nainštalujte Tesseract manuálne
    )
) else (
    echo ✅ Tesseract OCR je dostupný
    tesseract --version
)

:: Inštalácia českého jazyka pre Tesseract
echo.
echo 🇨🇿 Inštalujem český jazyk pre Tesseract...
if exist "C:\Program Files\Tesseract-OCR\tessdata\ces.traineddata" (
    echo ✅ Český jazyk už je nainštalovaný
) else (
    echo 📥 Sťahujem ces.traineddata...
    powershell -Command "Invoke-WebRequest -Uri 'https://github.com/tesseract-ocr/tessdata/raw/main/ces.traineddata' -OutFile 'ces.traineddata'"
    
    if exist ces.traineddata (
        echo 📂 Kopírujem do Tesseract adresára...
        copy ces.traineddata "C:\Program Files\Tesseract-OCR\tessdata\ces.traineddata" >nul
        del ces.traineddata
        echo ✅ Český jazyk nainštalovaný
    ) else (
        echo ⚠️ Nepodarilo sa stiahnuť český jazyk
    )
)

:: Test inštalácie
echo.
echo 🧪 Testujem inštaláciu...

echo   🐍 Python test...
python -c "print('✅ Python funguje')" || echo "❌ Python test zlyhal"

echo   🖥️ PyQt6 test...
python -c "from PyQt6 import QtWidgets; print('✅ PyQt6 funguje')" || echo "❌ PyQt6 test zlyhal"

echo   🔍 Tesseract test...
python -c "import pytesseract; print('✅ pytesseract funguje')" || echo "❌ pytesseract test zlyhal"

echo   🔊 TTS test...
python -c "import pyttsx3; print('✅ pyttsx3 funguje')" || echo "❌ pyttsx3 test zlyhal"

echo.
echo ========================================================
echo   🎉 INŠTALÁCIA DOKONČENÁ!
echo ========================================================
echo.
echo 🚀 Spúšťam Subtitle Reader aplikáciu...
echo.

:: Spustenie aplikácie
set TESSERACT_CMD=C:\Program Files\Tesseract-OCR\tesseract.exe
python main_qt.py

echo.
echo 👋 Aplikácia ukončená
echo.
echo 💡 Pre opätovné spustenie použite: run_app.bat
echo.
pause
