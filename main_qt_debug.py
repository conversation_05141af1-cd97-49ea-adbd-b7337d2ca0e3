#!/usr/bin/env python3
"""
Debug verzia main_qt.py s lepším error handlingom
"""

import sys
import os
import logging
import traceback
from PyQt6 import QtWidgets, QtCore

def setup_basic_logging():
    """Základné logovanie"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('debug.log', encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )

class DebugSubtitleReader(QtWidgets.QMainWindow):
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        print("🖥️ Inicializujem GUI...")
        
        self.setWindowTitle("Subtitle Reader - Debug Mode")
        self.setGeometry(100, 100, 800, 600)
        
        # Centrálny widget
        central_widget = QtWidgets.QWidget()
        self.setCentralWidget(central_widget)
        
        # Layout
        layout = QtWidgets.QVBoxLayout()
        central_widget.setLayout(layout)
        
        # Nadpis
        title = QtWidgets.QLabel("🎬 Subtitle Reader - Debug Mode")
        title.setStyleSheet("font-size: 24px; font-weight: bold; padding: 20px; color: #ffffff;")
        title.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)
        
        # Status
        self.status_label = QtWidgets.QLabel("✅ Debug režim aktívny - aplikácia funguje!")
        self.status_label.setStyleSheet("font-size: 16px; padding: 15px; background-color: #2d2d2d; color: #00ff00; border-radius: 5px;")
        self.status_label.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.status_label)
        
        # Info panel
        info_text = """
🔧 Diagnostické informácie:
• Python verzia: {python_version}
• PyQt6 verzia: {pyqt_version}
• Platforma: {platform}
• Tesseract: {tesseract_status}

🎮 Testovanie funkcií:
• Kliknite na tlačidlá nižšie pre test
• Skontrolujte debug.log pre detaily
• Aplikácia by mala byť plne funkčná
        """.format(
            python_version=sys.version.split()[0],
            pyqt_version="6.x",
            platform=sys.platform,
            tesseract_status="Dostupný" if os.path.exists("C:\\Program Files\\Tesseract-OCR\\tesseract.exe") else "Nedostupný"
        )
        
        info_panel = QtWidgets.QTextEdit()
        info_panel.setPlainText(info_text)
        info_panel.setStyleSheet("font-size: 12px; padding: 10px; background-color: #1e1e1e; color: #ffffff; border: 1px solid #555;")
        info_panel.setMaximumHeight(200)
        layout.addWidget(info_panel)
        
        # Tlačidlá
        button_layout = QtWidgets.QHBoxLayout()
        
        test_ocr_btn = QtWidgets.QPushButton("🔍 Test OCR")
        test_ocr_btn.setStyleSheet("font-size: 14px; padding: 10px; background-color: #2196F3; color: white; border: none; border-radius: 5px;")
        test_ocr_btn.clicked.connect(self.test_ocr)
        button_layout.addWidget(test_ocr_btn)
        
        test_tts_btn = QtWidgets.QPushButton("🔊 Test TTS")
        test_tts_btn.setStyleSheet("font-size: 14px; padding: 10px; background-color: #FF9800; color: white; border: none; border-radius: 5px;")
        test_tts_btn.clicked.connect(self.test_tts)
        button_layout.addWidget(test_tts_btn)
        
        load_full_btn = QtWidgets.QPushButton("🚀 Načítať plnú aplikáciu")
        load_full_btn.setStyleSheet("font-size: 14px; padding: 10px; background-color: #4CAF50; color: white; border: none; border-radius: 5px;")
        load_full_btn.clicked.connect(self.load_full_app)
        button_layout.addWidget(load_full_btn)
        
        layout.addLayout(button_layout)
        
        # Log area
        self.log_area = QtWidgets.QTextEdit()
        self.log_area.setPlaceholderText("Tu sa budú zobrazovať logy...")
        self.log_area.setStyleSheet("font-size: 11px; padding: 10px; background-color: #0d1117; color: #c9d1d9; border: 1px solid #30363d; font-family: 'Consolas', monospace;")
        layout.addWidget(self.log_area)
        
        # Tmavý štýl
        self.setStyleSheet("""
            QMainWindow {
                background-color: #1e1e1e;
                color: #ffffff;
            }
            QLabel {
                color: #ffffff;
            }
        """)
        
        # Zobraz okno
        self.show()
        self.raise_()
        self.activateWindow()
        
        print("✅ Debug GUI vytvorené a zobrazené")
        self.log("🚀 Debug aplikácia spustená úspešne")
    
    def log(self, message):
        """Pridaj správu do log area"""
        self.log_area.append(f"[{QtCore.QTime.currentTime().toString()}] {message}")
        logging.info(message)
    
    def test_ocr(self):
        """Test OCR funkcionality"""
        self.log("🔍 Testovanie OCR...")
        try:
            import pytesseract
            # Jednoduchý test
            self.log("✅ pytesseract modul načítaný")
            self.log("📍 Pre plný OCR test spustite plnú aplikáciu")
        except Exception as e:
            self.log(f"❌ OCR test zlyhal: {e}")
    
    def test_tts(self):
        """Test TTS funkcionality"""
        self.log("🔊 Testovanie TTS...")
        try:
            import pyttsx3
            engine = pyttsx3.init()
            engine.say("Test hlasového výstupu")
            engine.runAndWait()
            self.log("✅ TTS test úspešný")
        except Exception as e:
            self.log(f"❌ TTS test zlyhal: {e}")
    
    def load_full_app(self):
        """Načítaj plnú aplikáciu"""
        self.log("🚀 Načítavam plnú aplikáciu...")
        try:
            # Importuj a spusti plnú aplikáciu
            import qt_gui
            self.log("✅ Plná aplikácia by sa mala spustiť v novom okne")
        except Exception as e:
            self.log(f"❌ Načítanie plnej aplikácie zlyhalo: {e}")
            traceback.print_exc()

def main():
    setup_basic_logging()
    print("🚀 Spúšťam debug verziu Subtitle Reader...")
    
    # Vytvor aplikáciu
    app = QtWidgets.QApplication(sys.argv)
    app.setApplicationName("Subtitle Reader Debug")
    
    # Vytvor hlavné okno
    window = DebugSubtitleReader()
    
    print("🖥️ Debug GUI okno by sa malo zobraziť")
    
    # Spusti event loop
    try:
        sys.exit(app.exec())
    except KeyboardInterrupt:
        print("👋 Debug aplikácia ukončená")
        sys.exit(0)

if __name__ == "__main__":
    main()
