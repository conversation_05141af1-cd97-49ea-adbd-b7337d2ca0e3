{"app_title": "Lecteur de sous-titres", "main_tab": "Contrôle principal", "debug_tab": "Paramètres de débogage", "detection_tab": "Détection des sous-titres", "start_reading": "Commencer la <PERSON>", "stop_reading": "<PERSON><PERSON><PERSON><PERSON>", "hotkey_hint": "ou appuyez sur Cmd+Alt+Ctrl+V", "mode_group": "Mode de lecture", "mode_full_auto": "Entièrement automatique", "mode_static": "Statique", "mode_dynamic": "Dynamique", "ocr_group": "OCR", "ocr_threshold": "Seuil OCR:", "language_group": "<PERSON><PERSON>", "app_language": "Langue de l'application:", "reading_language": "Langue de lecture des sous-titres:", "performance_mode": "Mode de performance:", "custom_settings": "Paramètres", "clear_cache": "Vider le cache", "optimize_memory": "Optimiser la mémoire", "speed_test": "⚡ Test de vitesse", "tts_group": "Parole (TTS)", "speech_rate": "Vitesse de parole:", "speech_volume": "Volume de la parole:", "allow_uppercase": "Autoriser la lecture des majuscules", "perf_speed": "Vitesse (beam=1)", "perf_balanced": "<PERSON><PERSON><PERSON><PERSON><PERSON> (beam=4)", "perf_quality": "Qualité (beam=5)", "perf_custom": "<PERSON><PERSON><PERSON><PERSON>", "tooltip_custom_params": "Configurer les paramètres de traduction personnalisés", "tooltip_clear_cache": "Vider le cache de traduction pour économiser la mémoire", "tooltip_optimize_memory": "Forcer l'optimisation de la mémoire", "tooltip_speed_test": "Exécuter le test de vitesse de traduction", "tts_detecting_subtitles": "Détection du type de sous-titres.", "tts_starting_continuous": "Démarrage de la détection continue des sous-titres.", "tts_starting_reading": "Je commence à vous lire les sous-titres.", "tts_ending_reading": "<PERSON> lecture.", "tts_detected_dynamic": "Sous-titres dynamiques détectés.", "tts_detected_static": "Sous-titres statiques détectés.", "tts_switched_dynamic": "Basculé vers le traitement dynamique.", "tts_switched_static": "Basculé vers le traitement statique.", "tts_switched_to_static": "Basculé vers les sous-titres statiques.", "demo_expired_message": "Limite de temps de démonstration de 20 minutes expirée pour aujourd'hui. Achetez la version complète ou attendez demain.", "demo_time_remaining": "🔒 Version démo, il vous reste {time} pour aujourd'hui", "purchase_button": "Acheter pour 1 an", "full_version_status": "🔓 Version complète, valide jusqu'au {expires_at} ({days_remaining} jours)", "demo_expired_title": "Temps de démo expiré", "demo_expired_dialog": "Votre temps de démo quotidien a expiré.\n\nPour continuer à utiliser l'application, veuillez acheter la version complète pour un an.\n\nLe temps de démo sera renouvelé demain.", "demo_test_button": "Version démo", "full_test_button": "Version complète", "tts_test_button": "🔊 Test TTS", "toolbar_actions": "Actions", "reset_completed_title": "Réinitialisation terminée", "reset_completed_message": "Tous les paramètres ont été restaurés aux valeurs par défaut selon la langue du système.", "start_reading_section": "COMMENCER LA LECTURE", "translator_stats": "Statistiques", "translator_stats_legacy": "Statistiques: Système hérité", "translator_stats_unavailable": "Statistiques: Multi-traducteur indisponible", "translator_stats_error": "Statistiques: Erreur d'initialisation", "reading_allowed": "AUTORISÉ", "reading_forbidden": "INTERDIT", "uppercase_reading_status": "La lecture des majuscules est", "keyboard_image_not_found": "Image du clavier introuvable (keyboard_MAC_200.jpg)", "keyboard_image_error": "Erreur lors du chargement de l'image:", "performance_speed": "🚀 Vitesse (beam=1)", "performance_balanced": "⚖️ É<PERSON><PERSON>b<PERSON> (beam=4)", "performance_quality": "🎯 Qualité (beam=5)", "performance_custom": "⚙️ Personnalisé", "quality_fast": "⚡ Rapide", "quality_balanced": "⚖️ Équilibré", "quality_quality": "🏆 Qualité", "quality_custom": "⚙️ Personnalisé", "no_voices_available": "Aucune voix disponible", "statistics_not_available": "Statistiques: Non disponibles", "statistics_legacy_system": "Statistiques: Système hérité", "statistics_initialization_error": "Statistiques: Erreur d'initialisation", "connection_status_enter_credentials": "Statut: ❌ Entrez la clé API et la région", "connection_status_testing": "Statut : 🔄 Test de connexion...", "connection_status_invalid_credentials": "Statut : ❌ Identifiants invalides", "connection_status_success": "Statut : ✅ Connexion réussie", "connection_status_failed": "Statut : ❌ Test de connexion échoué", "connection_status_error": "Statut : ❌ Erreur : ", "connection_status_configured": "Statut: ✅ Identifiants définis", "connection_status_not_configured": "Statut : Non configuré", "filtering_enabled": "ACTIVÉ", "filtering_disabled": "DÉSACTIVÉ", "filtering_active": "Actif (X={x}px)", "filtering_detecting": "Détection ({samples}/5 échantillons)", "filtering_waiting": "En attente de détection", "filtering_all_text": "Prend tout le texte sans filtrage", "device_cpu": "CPU", "device_mps": "MPS (Apple Silicon)", "device_cuda": "CUDA (NVIDIA)", "model_status_not_loaded": "<PERSON><PERSON><PERSON><PERSON> non chargé", "model_status_loaded": "<PERSON><PERSON><PERSON><PERSON> char<PERSON>", "model_status_loading": "Chargement du modèle...", "memory_usage": "Mémoire: {memory} MB", "voice_for_reading": "🎤 Voix pour la lecture:", "voice_for_reading_tooltip": "Sélectionnez une voix pour lire les sous-titres et les messages système", "available_languages_info": "Disponible: {count} langues (OCR: {ocr}, TTS: {tts})", "available_source_info": "Disponible: {count} langues (OCR ∩ traducteur)", "available_target_info": "Disponible: {count} langues (TTS ∩ traducteur)", "speed_test_tooltip": "Exécuter le test de vitesse de traduction", "custom_settings_button": "⚙️ Paramètres", "custom_settings_tooltip": "Configurer les paramètres de traduction personnalisés", "similarity_thresholds": "⚙️ Seuils de similarité de base", "tts_duplicate_threshold": "Seuil de duplication TTS", "stability_similarity": "Stabilité de ligne (similarité)", "two_frame_stability": "Stabilité de deux images", "static_similarity": "Similarité statique", "static_stability_threshold": "Seuil de stabilité statique", "static_replacement_threshold": "Seuil de remplacement statique", "cycling_stability": "🔄 Stabilité cyclique", "min_cycles_first_line": "Min cycles 1ère ligne", "min_cycles_second_line": "Min cycles 2ème ligne", "min_cycles_second_growth": "Min cycles croissance 2ème ligne", "single_text_cycles": "Cycles de stabilité de texte unique", "different_texts_count": "Nombre de textes différents pour changer", "dynamic_growth": "📈 Croissance dynamique", "dynamic_growth_similarity": "Similarité de croissance dynamique", "dynamic_min_word_length": "Longueur min des mots ajoutés", "dynamic_max_words": "Max mots ajoutés à la fois", "full_auto_thresholds": "🤖 Seuils entièrement automatiques", "fa_initial_static": "FA: seuil statique initial", "fa_initial_dynamic": "FA: seuil dynamique initial", "fa_stable_static": "FA: seuil statique stable", "fa_stable_dynamic": "FA: seuil dynamique stable", "fa_min_samples": "FA: min échantillons stables", "fa_replace_threshold": "FA: seuil de remplacement", "ocr_analysis": "🔍 OCR et analyse", "ocr_threshold_param": "Seuil OCR", "dynamic_ocr_threshold": "Seuil OCR dynamique", "min_text_length": "Longueur min du texte pour l'analyse", "tests_tab": "Tests", "tolerance_group": "Tolérances de position des sous-titres", "ocr_interval_group": "Intervalle OCR", "filtering_group": "Filtrage par côté gauche", "actions_group": "Actions", "y_threshold_label": "Seuil Y des sous-titres (%):", "reference_logging": "Journalisation de référence pour l'analyse de détection", "current_logging_to": "Journalisation actuelle vers:", "standard_logging": "app.log (journalisation standard)", "static_logging": "static.log (pour sous-titres statiques)", "dynamic_logging": "dynamic.log (pour sous-titres dynamiques)", "clear_test_logs": "Effacer tous les logs de test", "open_logs_folder": "<PERSON><PERSON><PERSON><PERSON>r le dossier des logs", "source_language_label": "Langue source:", "target_language_label": "Langue cible:", "mode_off": "Désactivé", "mode_manual": "<PERSON>", "mode_auto": "Automatique", "mode_smart": "Intelligent", "quality_profile_label": "Profil de qualité:", "profile_fast": "⚡ Rapide", "profile_balanced": "⚖️ Équilibré", "profile_quality": "🏆 Qualité", "profile_custom": "⚙️ Personnalisé", "active_status": " (actif)", "not_exists": "n'existe pas", "error_loading_stats": "Erreur lors du chargement des statistiques: {error}", "reading_uppercase_enabled": "La lecture des majuscules est ACTIVÉE", "reading_uppercase_disabled": "La lecture des majuscules est DÉSACTIVÉE", "speech_rate_set": "Vitesse de parole définie à {rate}", "speech_volume_set": "Volume de la parole défini à {volume}", "ocr_threshold_set": "Seuil OCR défini à {threshold}", "thread_safe_status_update": "Mise à jour de statut thread‑safe.", "start_stop_action": "<PERSON><PERSON><PERSON><PERSON>/<PERSON>ter", "cancel_button": "Annuler", "test_text_placeholder": "Entrez le texte pour le test:", "error_no_input": "ERREUR: En<PERSON>z le texte à traduire!", "swap_languages_button": "🔄 Échanger les langues", "confirmation_dialog": "Confirmation", "success_dialog": "Su<PERSON>ès", "error_dialog": "<PERSON><PERSON><PERSON>", "warning_dialog": "Avertissement", "critical_error_dialog": "Erreur critique", "status_initialized": "✅ Initialisé", "status_not_initialized": "❌ Non initialisé", "device_mps_gpu": "🚀 GPU MPS (Apple Silicon)", "device_cuda_gpu": "🚀 GPU CUDA", "device_cpu_only": "💻 CPU", "memory_usage_model": "~2-3 GB (modèle 600M)", "memory_usage_none": "0 GB", "test_connection_button": "🧪 Test de connexion", "test_connection_tooltip": "Teste la connexion API avec les identifiants actuels", "filtering_status_all_text": "Prend tout le texte sans filtrage", "filtering_status_waiting": "En attente de détection", "filtering_status_active": "Actif (X={x}px)", "filtering_status_detecting": "Détecte ({samples}/5 échantillons)", "reset_left_detection": "Reset de la détection côté gauche", "report_anomaly": "Signaler une anomalie", "dialog_ok": "✅ OK", "dialog_cancel": "❌ Annuler", "dialog_reset": "🔄 Remettre par défaut", "generation_settings": "🔧 Génération", "penalty_settings": "⚖️ Pénalités", "optimization_settings": "🚀 Optimisations", "info_section": "ℹ️ Informations", "max_length_param": "Longueur max:", "min_length_param": "Longueur min:", "num_beams_param": "Nombre de beams:", "use_cache_param": "Utiliser le cache", "enable_fast_mode": "Activer le mode rapide:", "memory_optimization": "Optimisation mémoire:", "beam_search_settings": "🔹 Beam Search", "penalties_settings": "🔹 Pénalités et régulations", "length_limits_settings": "🔹 Limites de longueur", "apple_optimizations": "🔹 Optimisations Apple Silicon M1", "cache_settings": "🔹 Paramètres de cache", "api_settings": "🔹 Paramètres API", "enable_cache": "Activer le cache", "max_cache_size": "Taille max du cache :", "timeout_param": "Timeout :", "rate_limit_param": "<PERSON>ite <PERSON> :", "batch_size_param": "<PERSON><PERSON> du <PERSON> :", "seconds_suffix": " secondes", "tokens_suffix": " tokens", "status_label": "Statut:", "active_log_suffix": " (actif)", "file_not_exists": "n'existe pas", "stats_error_prefix": "Erreur lors du chargement des statistiques: ", "log_files_cleared": "Fichiers de log de test supprimés: ", "no_log_files_to_clear": "Aucun fichier de log de test à supprimer", "logs_folder_opened": "Dossier de logs ouvert: ", "anomaly_report_started": "Rapport d'anomalie démarré via le bouton GUI (Qt)", "detection_reset": "La détection du côté gauche des sous-titres dynamiques a été réinitialisée", "filtering_status_on": "ACTIVÉ", "filtering_status_off": "DÉSACTIVÉ", "all_modes_enabled": "tous les modes activés, paramètres restaurés", "switched_to_static": "basculé en mode statique", "connection_status_missing_credentials": "Statut : ❌ Entrez la clé API et la région", "max_retries_param": "Max tentatives :", "beam_search_group": "🔹 Beam Search", "penalties_group": "🔹 Pénalités et régulations", "no_repeat_ngram_label": "<PERSON><PERSON> no repeat ngram :", "no_repeat_ngram_tooltip": "Bloque la répétition de n-grammes (ex. 'the the the')\n0 = désactivé, 2-4 = valeurs typiques", "repetition_penalty_label": "Pénalité de répétition :", "repetition_penalty_tooltip": "Pénalise les mots répétés\n>1.0 = moins de répétitions, 1.1-1.3 = recommandé", "length_penalty_label": "Pénalité de longueur :", "length_penalty_tooltip": "Affecte la préférence pour des phrases plus longues/courtes\n>1.0 = traductions plus longues, <1.0 = traductions plus courtes", "length_limits_group": "🔹 Limites de longueur", "max_length_label": "Longueur max :", "max_length_not_supported_label": "Longueur max (non supporté) :", "optimizations_group": "🔹 Optimisations pour Apple Silicon M1", "fast_mode_label": "Activer le mode rapide :", "fast_mode_tooltip": "Active les optimisations rapides", "memory_opt_label": "Optimisation mémoire :", "memory_opt_tooltip": "Active les optimisations mémoire", "info_group": "ℹ️ Informations", "reset_to_defaults": "🔄 Réinitialiser par défaut", "ocr_interval_label": "Intervalle OCR (s) :"}