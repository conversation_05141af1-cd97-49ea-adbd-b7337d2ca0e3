#!/bin/bash

# 🍎 Skript na vytvorenie macOS .app bundle pre Subtitle Reader
# Tento skript vytvorí skutočnú macOS aplikáciu bez terminálu

APP_NAME="SubtitleReader"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
APP_PATH="$SCRIPT_DIR/${APP_NAME}.app"

echo "🍎 Vytváram macOS aplikáciu: $APP_NAME.app"

# Vymaž starú aplikáciu ak existuje
if [ -d "$APP_PATH" ]; then
    echo "🗑️ Mažem starú aplikáciu..."
    rm -rf "$APP_PATH"
fi

# Vytvor štruktúru .app bundle
echo "📁 Vytváram štruktúru aplikácie..."
mkdir -p "$APP_PATH/Contents/MacOS"
mkdir -p "$APP_PATH/Contents/Resources"

# Vytvor Info.plist
echo "📄 Vytváram Info.plist..."
cat > "$APP_PATH/Contents/Info.plist" << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>CFBundleExecutable</key>
    <string>SubtitleReader</string>
    <key>CFBundleIdentifier</key>
    <string>com.subtitlereader.app</string>
    <key>CFBundleName</key>
    <string>Subtitle Reader</string>
    <key>CFBundleVersion</key>
    <string>1.0</string>
    <key>CFBundleShortVersionString</key>
    <string>1.0</string>
    <key>CFBundlePackageType</key>
    <string>APPL</string>
    <key>LSUIElement</key>
    <false/>
    <key>NSHighResolutionCapable</key>
    <true/>
</dict>
</plist>
EOF

# Vytvor spúšťací skript
echo "🚀 Vytváram spúšťací skript..."
cat > "$APP_PATH/Contents/MacOS/SubtitleReader" << 'EOF'
#!/bin/bash

# Získaj adresár aplikácie (kde je main_qt.py)
APP_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/../../../" && pwd)"
cd "$APP_DIR"

# Nastav pyenv ak existuje
export PATH="$HOME/.pyenv/bin:$PATH"
if command -v pyenv 1>/dev/null 2>&1; then
    eval "$(pyenv init -)"
    eval "$(pyenv virtualenv-init -)"
    
    # Aktivuj reader-env ak existuje
    if pyenv versions | grep -q "reader-env"; then
        pyenv shell reader-env
        PYTHON_CMD="python"
    else
        PYTHON_CMD="python"
    fi
else
    # Fallback na systémový Python
    if command -v python3 &> /dev/null; then
        PYTHON_CMD="python3"
    else
        PYTHON_CMD="python"
    fi
fi

# Spusti aplikáciu
exec "$PYTHON_CMD" main_qt.py
EOF

# Urob spúšťací skript spustiteľným
chmod +x "$APP_PATH/Contents/MacOS/SubtitleReader"

echo "✅ Aplikácia vytvorená: $APP_PATH"
echo ""
echo "🎯 Ako použiť:"
echo "   1. Dvojklik na SubtitleReader.app"
echo "   2. Aplikácia sa spustí priamo bez terminálu"
echo "   3. Môžeš ju prekopírovať na plochu alebo do Applications"
echo ""
echo "📋 Kopírovanie na plochu:"
echo "   cp -r '$APP_PATH' ~/Desktop/"
echo ""
echo "📋 Kopírovanie do Applications:"
echo "   cp -r '$APP_PATH' /Applications/"
