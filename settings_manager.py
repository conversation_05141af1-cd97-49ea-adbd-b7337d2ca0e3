#!/usr/bin/env python3
"""
Správca nastavení aplikácie - ukladanie a načítavanie konfigurácie.
"""

import json
import logging
import locale
import subprocess
from pathlib import Path
import common_config as config

# Cesta k súboru s nastaveniami
SETTINGS_FILE = Path("app_settings.json")

def get_system_language_and_region():
    """Detekuje systémový jazyk a región cross-platform."""
    try:
        # Try cross-platform implementation first
        try:
            from platform_utils import get_system_info_provider
            provider = get_system_info_provider()
            system_lang = provider.get_system_language()
            if system_lang:
                logging.info(f"🌍 Systémový jazyk detekovaný (cross-platform): {system_lang}")
                return system_lang
        except ImportError:
            logging.warning("⚠️ Cross-platform system info provider not available")
        except Exception as e:
            logging.debug(f"⚠️ Cross-platform language detection failed: {e}")

        # Fallback to legacy macOS implementation
        try:
            import platform
            if platform.system() == "Darwin":
                # Pokus o získanie jazyka z macOS defaults
                result = subprocess.run(
                    ['defaults', 'read', '-g', 'AppleLanguages'],
                    capture_output=True, text=True, timeout=5
                )

                if result.returncode == 0:
                    # Parsovanie výstupu - prvý jazyk v zozname
                    output = result.stdout.strip()
                    # Formát: ( "sk-SK", "en-US", ... )
                    if '(' in output and '"' in output:
                        languages = output.split('"')
                        if len(languages) >= 2:
                            first_lang = languages[1]  # Prvý jazyk
                            logging.info(f"🌍 Systémový jazyk detekovaný (macOS): {first_lang}")
                            return first_lang
        except Exception as e:
            logging.debug(f"⚠️ macOS language detection failed: {e}")

        # Universal fallback na locale
        system_locale = locale.getdefaultlocale()[0]
        if system_locale:
            logging.info(f"🌍 Systémový jazyk z locale: {system_locale}")
            return system_locale.replace('_', '-')

    except Exception as e:
        logging.warning(f"⚠️ Chyba pri detekcii systémového jazyka: {e}")

    # Posledný fallback
    return "en-US"

def map_system_language_to_app_settings(system_lang):
    """Mapuje systémový jazyk na nastavenia aplikácie."""
    # Mapovanie systémových jazykov na podporované jazyky aplikácie
    language_mapping = {
        # Slovenčina
        "sk": {"app": "sk", "reading": "sk", "tts": "sk-SK", "ocr": "slk"},
        "sk-SK": {"app": "sk", "reading": "sk", "tts": "sk-SK", "ocr": "slk"},

        # Čeština
        "cs": {"app": "cs", "reading": "cs", "tts": "cs-CZ", "ocr": "ces"},
        "cs-CZ": {"app": "cs", "reading": "cs", "tts": "cs-CZ", "ocr": "ces"},

        # Angličtina
        "en": {"app": "en", "reading": "en", "tts": "en-US", "ocr": "eng"},
        "en-US": {"app": "en", "reading": "en", "tts": "en-US", "ocr": "eng"},
        "en-GB": {"app": "en", "reading": "en", "tts": "en-GB", "ocr": "eng"},

        # Nemčina
        "de": {"app": "de", "reading": "de", "tts": "de-DE", "ocr": "deu"},
        "de-DE": {"app": "de", "reading": "de", "tts": "de-DE", "ocr": "deu"},

        # Francúzština
        "fr": {"app": "fr", "reading": "fr", "tts": "fr-FR", "ocr": "fra"},
        "fr-FR": {"app": "fr", "reading": "fr", "tts": "fr-FR", "ocr": "fra"},
    }

    # Skús presný match
    if system_lang in language_mapping:
        return language_mapping[system_lang]

    # Skús len jazyk bez regiónu
    lang_only = system_lang.split('-')[0]
    if lang_only in language_mapping:
        return language_mapping[lang_only]

    # Fallback na češtinu
    logging.info(f"🔄 Neznámy systémový jazyk {system_lang}, používam češtinu")
    return language_mapping["cs"]

def get_best_voice_for_language(tts_language):
    """Získa najlepší hlas pre daný TTS jazyk (Premium > Enhanced > Standard)."""
    try:
        # Použije language_detector pre inteligentný výber najlepšieho hlasu
        from language_detector import get_language_detector
        detector = get_language_detector()

        # Konvertuje tts_language (cs-CZ) na lang_code (cs)
        lang_code = tts_language.split('-')[0]

        # Získa najlepší hlas pomocou novej logiky
        best_voice = detector.get_best_voice_for_language(lang_code)

        if best_voice:
            logging.info(f"🎤 Najlepší hlas pre {tts_language}: {best_voice}")
            return best_voice

        # Fallback - skús starú logiku
        import tts_manager
        available_voices = tts_manager.get_available_voices()

        # Fallback - nájdi akýkoľvek hlas pre jazyk
        for voice in available_voices:
            if lang_code.lower() in voice.lower():
                logging.info(f"🎤 Fallback hlas pre {tts_language}: {voice}")
                return voice

        # Posledný fallback
        if available_voices:
            fallback = list(available_voices)[0]
            logging.warning(f"⚠️ Používam fallback hlas: {fallback}")
            return fallback

    except Exception as e:
        logging.error(f"❌ Chyba pri hľadaní najlepšieho hlasu: {e}")

    return "Iveta (Enhanced)"  # Hardcoded fallback

# Predvolené nastavenia (budú sa prepísať pri prvom spustení podľa systému)
DEFAULT_SETTINGS = {
    # Základné nastavenia
    "reading_mode": "full_automatic",
    "reading_language": "cs",
    "tts_language": "cs-CZ",
    "ocr_language": "ces",

    # TTS nastavenia
    "tts_voice": "Iveta (Enhanced)",
    "reading_tts_voice": "Iveta (Enhanced)",
    "selected_reading_voice": "Iveta (Enhanced)",
    "tts_rate": 200,
    "user_tts_rate": 200,
    "tts_volume": 1.0,
    "allow_uppercase_text": True,

    # OCR nastavenia
    "ocr_threshold": 0.99,
    "dynamic_ocr_threshold": 0.90,
    "min_text_length_for_analysis": 3,

    # Prekladač
    "translation_enabled": False,
    "translation_source_language": "cs",
    "translation_target_language": "en",
    "selected_translation_voice": "Karen (Enhanced)",

    # GUI nastavenia
    "always_on_top": False,

    # Aplikačný jazyk
    "app_language": "cs",

    # Detekcia titulkov - záložka 2 - Základné prahy
    "tts_history_similarity_threshold": 0.90,
    "stability_similarity_threshold": 0.80,
    "two_frame_stability_threshold": 0.90,
    "static_similarity_threshold": 0.80,
    "static_stability_threshold": 0.80,
    "static_replacement_threshold": 0.50,

    # Cyklovanie stability
    "min_cycles_first_line_stability": 2,
    "min_cycles_second_line_stability": 2,
    "min_cycles_second_line_growth": 2,
    "stabilita_jedneho_textu_cykly": 2,
    "pocet_roznych_stabilnych_textov_ciel": 3,

    # Hlasy pre jednotlivé jazyky (uložené preferencie)
    "language_voices": {
        "cs-CZ": "Iveta (Enhanced)",
        "sk-SK": "Laura (Enhanced)",
        "en-US": "Karen (Enhanced)",
        "en-GB": "Daniel (Enhanced)",
        "de-DE": "Anna (Enhanced)",
        "fr-FR": "Amelie (Enhanced)"
    },

    # Prvé spustenie flag
    "first_run": True
}

def save_settings():
    """Uloží aktuálne nastavenia do súboru."""
    try:
        settings = {
            # Základné nastavenia
            "reading_mode": getattr(config, 'reading_mode', DEFAULT_SETTINGS["reading_mode"]),
            "reading_language": getattr(config, 'READING_LANGUAGE', DEFAULT_SETTINGS["reading_language"]),
            "tts_language": getattr(config, 'TTS_LANGUAGE', DEFAULT_SETTINGS["tts_language"]),
            "ocr_language": getattr(config, 'OCR_LANGUAGE', DEFAULT_SETTINGS["ocr_language"]),
            
            # TTS nastavenia
            "tts_voice": getattr(config, 'TTS_VOICE', DEFAULT_SETTINGS["tts_voice"]),
            "reading_tts_voice": getattr(config, 'READING_TTS_VOICE', DEFAULT_SETTINGS["reading_tts_voice"]),
            "selected_reading_voice": getattr(config, 'SELECTED_READING_VOICE', DEFAULT_SETTINGS["selected_reading_voice"]),
            "tts_rate": getattr(config, 'TTS_RATE', DEFAULT_SETTINGS["tts_rate"]),
            "user_tts_rate": getattr(config, 'USER_TTS_RATE', DEFAULT_SETTINGS["user_tts_rate"]),
            "tts_volume": getattr(config, 'TTS_VOLUME', DEFAULT_SETTINGS["tts_volume"]),
            "allow_uppercase_text": getattr(config, 'ALLOW_UPPERCASE_TEXT', DEFAULT_SETTINGS["allow_uppercase_text"]),
            
            # OCR nastavenia
            "ocr_threshold": getattr(config, 'OCR_THRESHOLD', DEFAULT_SETTINGS["ocr_threshold"]),
            "dynamic_ocr_threshold": getattr(config, 'DYNAMIC_OCR_THRESHOLD', DEFAULT_SETTINGS["dynamic_ocr_threshold"]),
            "min_text_length_for_analysis": getattr(config, 'MIN_TEXT_LENGTH_FOR_ANALYSIS', DEFAULT_SETTINGS["min_text_length_for_analysis"]),
            
            # Prekladač
            "translation_enabled": getattr(config, 'TRANSLATION_ENABLED', DEFAULT_SETTINGS["translation_enabled"]),
            "translation_source_language": getattr(config, 'TRANSLATION_SOURCE_LANGUAGE', DEFAULT_SETTINGS["translation_source_language"]),
            "translation_target_language": getattr(config, 'TRANSLATION_TARGET_LANGUAGE', DEFAULT_SETTINGS["translation_target_language"]),
            "selected_translation_voice": getattr(config, 'SELECTED_TRANSLATION_VOICE', DEFAULT_SETTINGS["selected_translation_voice"]),
            
            # GUI nastavenia
            "always_on_top": getattr(config, 'ALWAYS_ON_TOP', DEFAULT_SETTINGS["always_on_top"]),
            
            # Aplikačný jazyk
            "app_language": getattr(config, 'APP_LANGUAGE', DEFAULT_SETTINGS["app_language"]),

            # Detekcia titulkov - záložka 2 - Základné prahy
            "tts_history_similarity_threshold": getattr(config, 'TTS_HISTORY_SIMILARITY_THRESHOLD', DEFAULT_SETTINGS["tts_history_similarity_threshold"]),
            "stability_similarity_threshold": getattr(config, 'STABILITY_SIMILARITY_THRESHOLD', DEFAULT_SETTINGS["stability_similarity_threshold"]),
            "two_frame_stability_threshold": getattr(config, 'TWO_FRAME_STABILITY_THRESHOLD', DEFAULT_SETTINGS["two_frame_stability_threshold"]),
            "static_similarity_threshold": getattr(config, 'STATIC_SIMILARITY_THRESHOLD', DEFAULT_SETTINGS["static_similarity_threshold"]),
            "static_stability_threshold": getattr(config, 'STATIC_STABILITY_THRESHOLD', DEFAULT_SETTINGS["static_stability_threshold"]),
            "static_replacement_threshold": getattr(config, 'STATIC_REPLACEMENT_THRESHOLD', DEFAULT_SETTINGS["static_replacement_threshold"]),

            # Cyklovanie stability
            "min_cycles_first_line_stability": getattr(config, 'MIN_CYCLES_FIRST_LINE_STABILITY', DEFAULT_SETTINGS["min_cycles_first_line_stability"]),
            "min_cycles_second_line_stability": getattr(config, 'MIN_CYCLES_SECOND_LINE_STABILITY', DEFAULT_SETTINGS["min_cycles_second_line_stability"]),
            "min_cycles_second_line_growth": getattr(config, 'MIN_CYCLES_SECOND_LINE_GROWTH', DEFAULT_SETTINGS["min_cycles_second_line_growth"]),
            "stabilita_jedneho_textu_cykly": getattr(config, 'STABILITA_JEDNEHO_TEXTU_CYKLY', DEFAULT_SETTINGS["stabilita_jedneho_textu_cykly"]),
            "pocet_roznych_stabilnych_textov_ciel": getattr(config, 'POCET_ROZNYCH_STABILNYCH_TEXTOV_CIEL', DEFAULT_SETTINGS["pocet_roznych_stabilnych_textov_ciel"]),

            # Hlasy pre jednotlivé jazyky
            "language_voices": getattr(config, 'LANGUAGE_VOICES', DEFAULT_SETTINGS["language_voices"]),

            # Prvé spustenie flag
            "first_run": False  # Po uložení už nie je prvé spustenie
        }
        
        with open(SETTINGS_FILE, 'w', encoding='utf-8') as f:
            json.dump(settings, f, indent=2, ensure_ascii=False)
        
        logging.info(f"💾 Nastavenia uložené do {SETTINGS_FILE}")
        return True
        
    except Exception as e:
        logging.error(f"❌ Chyba pri ukladaní nastavení: {e}")
        return False

def load_settings():
    """Načíta nastavenia zo súboru a aplikuje ich na config."""
    try:
        if not SETTINGS_FILE.exists():
            logging.info("📁 Súbor s nastaveniami neexistuje, inicializujem systémové nastavenia")
            return initialize_system_defaults()
        
        with open(SETTINGS_FILE, 'r', encoding='utf-8') as f:
            settings = json.load(f)
        
        # Aplikovanie nastavení na config
        config.reading_mode = settings.get("reading_mode", DEFAULT_SETTINGS["reading_mode"])
        config.READING_LANGUAGE = settings.get("reading_language", DEFAULT_SETTINGS["reading_language"])
        config.TTS_LANGUAGE = settings.get("tts_language", DEFAULT_SETTINGS["tts_language"])
        config.OCR_LANGUAGE = settings.get("ocr_language", DEFAULT_SETTINGS["ocr_language"])
        
        # TTS nastavenia
        config.TTS_VOICE = settings.get("tts_voice", DEFAULT_SETTINGS["tts_voice"])
        config.READING_TTS_VOICE = settings.get("reading_tts_voice", DEFAULT_SETTINGS["reading_tts_voice"])
        config.SELECTED_READING_VOICE = settings.get("selected_reading_voice", DEFAULT_SETTINGS["selected_reading_voice"])
        config.TTS_RATE = settings.get("tts_rate", DEFAULT_SETTINGS["tts_rate"])
        config.USER_TTS_RATE = settings.get("user_tts_rate", DEFAULT_SETTINGS["user_tts_rate"])
        config.TTS_VOLUME = settings.get("tts_volume", DEFAULT_SETTINGS["tts_volume"])
        config.ALLOW_UPPERCASE_TEXT = settings.get("allow_uppercase_text", DEFAULT_SETTINGS["allow_uppercase_text"])
        
        # OCR nastavenia
        config.OCR_THRESHOLD = settings.get("ocr_threshold", DEFAULT_SETTINGS["ocr_threshold"])
        config.DYNAMIC_OCR_THRESHOLD = settings.get("dynamic_ocr_threshold", DEFAULT_SETTINGS["dynamic_ocr_threshold"])
        config.MIN_TEXT_LENGTH_FOR_ANALYSIS = settings.get("min_text_length_for_analysis", DEFAULT_SETTINGS["min_text_length_for_analysis"])
        
        # Prekladač
        config.TRANSLATION_ENABLED = settings.get("translation_enabled", DEFAULT_SETTINGS["translation_enabled"])
        config.TRANSLATION_SOURCE_LANGUAGE = settings.get("translation_source_language", DEFAULT_SETTINGS["translation_source_language"])
        config.TRANSLATION_TARGET_LANGUAGE = settings.get("translation_target_language", DEFAULT_SETTINGS["translation_target_language"])
        config.SELECTED_TRANSLATION_VOICE = settings.get("selected_translation_voice", DEFAULT_SETTINGS["selected_translation_voice"])
        
        # GUI nastavenia
        config.ALWAYS_ON_TOP = settings.get("always_on_top", DEFAULT_SETTINGS["always_on_top"])
        
        # Aplikačný jazyk
        config.APP_LANGUAGE = settings.get("app_language", DEFAULT_SETTINGS["app_language"])

        # Detekcia titulkov - záložka 2 - Základné prahy
        config.TTS_HISTORY_SIMILARITY_THRESHOLD = settings.get("tts_history_similarity_threshold", DEFAULT_SETTINGS["tts_history_similarity_threshold"])
        config.STABILITY_SIMILARITY_THRESHOLD = settings.get("stability_similarity_threshold", DEFAULT_SETTINGS["stability_similarity_threshold"])
        config.TWO_FRAME_STABILITY_THRESHOLD = settings.get("two_frame_stability_threshold", DEFAULT_SETTINGS["two_frame_stability_threshold"])
        config.STATIC_SIMILARITY_THRESHOLD = settings.get("static_similarity_threshold", DEFAULT_SETTINGS["static_similarity_threshold"])
        config.STATIC_STABILITY_THRESHOLD = settings.get("static_stability_threshold", DEFAULT_SETTINGS["static_stability_threshold"])
        config.STATIC_REPLACEMENT_THRESHOLD = settings.get("static_replacement_threshold", DEFAULT_SETTINGS["static_replacement_threshold"])

        # Cyklovanie stability
        config.MIN_CYCLES_FIRST_LINE_STABILITY = settings.get("min_cycles_first_line_stability", DEFAULT_SETTINGS["min_cycles_first_line_stability"])
        config.MIN_CYCLES_SECOND_LINE_STABILITY = settings.get("min_cycles_second_line_stability", DEFAULT_SETTINGS["min_cycles_second_line_stability"])
        config.MIN_CYCLES_SECOND_LINE_GROWTH = settings.get("min_cycles_second_line_growth", DEFAULT_SETTINGS["min_cycles_second_line_growth"])
        config.STABILITA_JEDNEHO_TEXTU_CYKLY = settings.get("stabilita_jedneho_textu_cykly", DEFAULT_SETTINGS["stabilita_jedneho_textu_cykly"])
        config.POCET_ROZNYCH_STABILNYCH_TEXTOV_CIEL = settings.get("pocet_roznych_stabilnych_textov_ciel", DEFAULT_SETTINGS["pocet_roznych_stabilnych_textov_ciel"])

        # Hlasy pre jednotlivé jazyky
        config.LANGUAGE_VOICES = settings.get("language_voices", DEFAULT_SETTINGS["language_voices"])

        # Skontroluj, či je to prvé spustenie
        is_first_run = settings.get("first_run", True)
        if is_first_run:
            logging.info("🚀 Prvé spustenie - inicializujem systémové nastavenia")
            # Pri prvom spustení aplikuj systémové nastavenia, ale zachovaj už načítané hodnoty
            apply_system_language_settings()
        else:
            # Pri ďalších spusteniach načítaj uložené hlasy pre aktuálny jazyk
            apply_saved_voices_for_current_language()

        logging.info(f"📖 Nastavenia načítané z {SETTINGS_FILE}")
        return True
        
    except Exception as e:
        logging.error(f"❌ Chyba pri načítavaní nastavení: {e}")
        logging.info("🔄 Používam predvolené nastavenia")
        return apply_default_settings()

def apply_default_settings():
    """Aplikuje predvolené nastavenia na config."""
    try:
        for key, value in DEFAULT_SETTINGS.items():
            config_key = key.upper() if key not in ["reading_mode"] else key
            setattr(config, config_key, value)
        
        logging.info("✅ Predvolené nastavenia aplikované")
        return True
        
    except Exception as e:
        logging.error(f"❌ Chyba pri aplikovaní predvolených nastavení: {e}")
        return False

def initialize_system_defaults():
    """Inicializuje nastavenia podľa systémového jazyka pri prvom spustení."""
    try:
        # Najprv aplikuj predvolené nastavenia
        apply_default_settings()

        # Potom aplikuj systémové jazykové nastavenia
        apply_system_language_settings()

        return True

    except Exception as e:
        logging.error(f"❌ Chyba pri inicializácii systémových nastavení: {e}")
        return False

def reset_to_defaults():
    """Resetuje všetky nastavenia na predvolené hodnoty a uloží ich."""
    try:
        apply_default_settings()
        save_settings()
        logging.info("🔄 Nastavenia resetované na predvolené hodnoty")
        return True

    except Exception as e:
        logging.error(f"❌ Chyba pri resetovaní nastavení: {e}")
        return False

def save_voice_for_language(language, voice):
    """Uloží preferovaný hlas pre konkrétny jazyk."""
    try:
        if not hasattr(config, 'LANGUAGE_VOICES'):
            config.LANGUAGE_VOICES = DEFAULT_SETTINGS["language_voices"].copy()

        config.LANGUAGE_VOICES[language] = voice
        logging.info(f"💾 Uložený hlas pre {language}: {voice}")

        # Automatické uloženie
        save_settings()
        return True

    except Exception as e:
        logging.error(f"❌ Chyba pri ukladaní hlasu pre jazyk: {e}")
        return False

def get_saved_voice_for_language(language):
    """Získa uložený hlas pre konkrétny jazyk."""
    try:
        # Inicializuj LANGUAGE_VOICES ak neexistuje
        if not hasattr(config, 'LANGUAGE_VOICES'):
            config.LANGUAGE_VOICES = DEFAULT_SETTINGS["language_voices"].copy()

        # Skús načítať uložený hlas
        if language in config.LANGUAGE_VOICES:
            voice = config.LANGUAGE_VOICES[language]
            logging.info(f"📖 Načítaný uložený hlas pre {language}: {voice}")
            return voice

        # Fallback na najlepší hlas a uloží ho
        best_voice = get_best_voice_for_language(language)
        config.LANGUAGE_VOICES[language] = best_voice
        logging.info(f"🎤 Nastavený najlepší hlas pre {language}: {best_voice}")
        return best_voice

    except Exception as e:
        logging.error(f"❌ Chyba pri načítavaní hlasu pre jazyk: {e}")
        return get_best_voice_for_language(language)

def apply_system_language_settings():
    """Aplikuje systémové jazykové nastavenia pri prvom spustení, ale zachová už načítané hodnoty."""
    try:
        # Detekuj systémový jazyk
        system_lang = get_system_language_and_region()
        lang_settings = map_system_language_to_app_settings(system_lang)

        logging.info(f"🌍 Aplikujem systémové jazykové nastavenia: {system_lang}")

        # Nastav jazyky len ak ešte nie sú nastavené alebo sú predvolené
        if not hasattr(config, 'APP_LANGUAGE') or config.APP_LANGUAGE == "cs":
            config.APP_LANGUAGE = lang_settings["app"]

        if not hasattr(config, 'READING_LANGUAGE') or config.READING_LANGUAGE == "cs":
            config.READING_LANGUAGE = lang_settings["reading"]

        if not hasattr(config, 'TTS_LANGUAGE') or config.TTS_LANGUAGE == "cs-CZ":
            config.TTS_LANGUAGE = lang_settings["tts"]

        if not hasattr(config, 'OCR_LANGUAGE') or config.OCR_LANGUAGE == "ces":
            config.OCR_LANGUAGE = lang_settings["ocr"]

        # Nastav najlepší hlas pre systémový jazyk ak ešte nie je nastavený
        current_tts_lang = getattr(config, 'TTS_LANGUAGE', lang_settings["tts"])
        saved_voice = get_saved_voice_for_language(current_tts_lang)

        config.TTS_VOICE = saved_voice
        config.READING_TTS_VOICE = saved_voice
        config.SELECTED_READING_VOICE = saved_voice

        # Nastav prekladač
        if lang_settings["reading"] != "en":
            config.TRANSLATION_SOURCE_LANGUAGE = lang_settings["reading"]
            config.TRANSLATION_TARGET_LANGUAGE = "en"
            config.SELECTED_TRANSLATION_VOICE = get_best_voice_for_language("en-US")
        else:
            config.TRANSLATION_SOURCE_LANGUAGE = "en"
            config.TRANSLATION_TARGET_LANGUAGE = "cs"
            config.SELECTED_TRANSLATION_VOICE = get_best_voice_for_language("cs-CZ")

        logging.info(f"✅ Systémové jazykové nastavenia aplikované:")
        logging.info(f"   📱 Jazyk aplikácie: {config.APP_LANGUAGE}")
        logging.info(f"   📖 Jazyk čítania: {config.READING_LANGUAGE}")
        logging.info(f"   🎤 Hlas: {saved_voice}")

        # Uloží nastavenia
        save_settings()

        # Nastav aj LanguageManager ak existuje
        try:
            # Importuj a nastav jazyk čítania v LanguageManageri
            from i18n_manager import set_reading_language
            set_reading_language(lang_settings["reading"])
            logging.info(f"🔧 LanguageManager nastavený na jazyk čítania: {lang_settings['reading']}")
        except Exception as e:
            logging.debug(f"LanguageManager ešte nie je dostupný: {e}")

        return True

    except Exception as e:
        logging.error(f"❌ Chyba pri aplikovaní systémových jazykových nastavení: {e}")
        return False

def apply_saved_voices_for_current_language():
    """Aplikuje uložené hlasy pre aktuálne nastavené jazyky."""
    try:
        # Načítaj uložený hlas pre aktuálny jazyk čítania
        current_tts_lang = getattr(config, 'TTS_LANGUAGE', 'cs-CZ')
        saved_reading_voice = get_saved_voice_for_language(current_tts_lang)

        config.TTS_VOICE = saved_reading_voice
        config.READING_TTS_VOICE = saved_reading_voice
        config.SELECTED_READING_VOICE = saved_reading_voice

        # Načítaj uložený hlas pre aktuálny jazyk prekladu
        target_lang = getattr(config, 'TRANSLATION_TARGET_LANGUAGE', 'en')
        tts_lang_map = {
            'en': 'en-US', 'cs': 'cs-CZ', 'sk': 'sk-SK',
            'de': 'de-DE', 'fr': 'fr-FR'
        }
        target_tts_lang = tts_lang_map.get(target_lang, f"{target_lang}-{target_lang.upper()}")
        saved_translation_voice = get_saved_voice_for_language(target_tts_lang)

        config.SELECTED_TRANSLATION_VOICE = saved_translation_voice
        config.TRANSLATION_TTS_VOICE = saved_translation_voice

        logging.info(f"🎤 Načítané uložené hlasy:")
        logging.info(f"   📖 Čítanie ({current_tts_lang}): {saved_reading_voice}")
        logging.info(f"   🔄 Preklad ({target_tts_lang}): {saved_translation_voice}")

        return True

    except Exception as e:
        logging.error(f"❌ Chyba pri aplikovaní uložených hlasov: {e}")
        return False
