<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test úvodn<PERSON> h<PERSON> - VOXO LOXO</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 100px 0;
        }
        
        .intro-message {
            animation: fadeInUp 1s ease-out;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-radius: 10px !important;
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        /* Responzívne úpravy pre úvodnú hlášku */
        @media (max-width: 768px) {
            .intro-message {
                font-size: 1rem !important;
                padding: 15px !important;
                margin: 0 10px 20px 10px !important;
            }
        }
        
        .demo-section {
            padding: 50px 0;
            background: #f8f9fa;
        }
        
        .version-demo {
            background: white;
            border-radius: 10px;
            padding: 30px;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <!-- Hero Section s úvodnou hláškou -->
    <section class="hero-section">
        <div class="container text-center">
            <!-- Hlavná hláška -->
            <div class="main-headline mb-5">
                <h1 class="display-2 fw-bold text-white mb-3" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.3);">
                    <i class="fas fa-magic me-3" style="color: #FFD700;"></i>
                    Už nemusíte číst titulky!
                </h1>
                <h2 class="display-5 fw-light text-white-50 mb-4">
                    Naše aplikace Vám je přečte za Vás
                </h2>
                <div class="brand-badge">
                    <span class="badge bg-light text-dark fs-4 px-4 py-2" style="border-radius: 25px; box-shadow: 0 4px 15px rgba(0,0,0,0.2);">
                        VOXO LOXO
                    </span>
                    <div class="mt-2">
                        <span class="text-white-50 fs-5">čtečka titulků</span>
                    </div>
                </div>
            </div>
            <div class="mb-4">
                <span class="badge bg-success fs-6 px-3 py-2">20 minut denně ZDARMA</span>
            </div>
            
            <a href="#demo" class="btn btn-light btn-lg me-3">
                <i class="fas fa-play"></i> Vyzkoušet Demo
            </a>
            <a href="#" class="btn btn-outline-light btn-lg">
                <i class="fas fa-shopping-cart"></i> Koupit za 299 Kč
            </a>
        </div>
    </section>
    
    <!-- Demo sekcia -->
    <section class="demo-section" id="demo">
        <div class="container">
            <h2 class="text-center mb-5">Ukázka úvodní hlášky</h2>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="version-demo">
                        <h4><i class="fas fa-desktop text-primary"></i> Desktop verze</h4>
                        <div class="alert alert-light intro-message" style="background: rgba(255,255,255,0.9); border: none; font-size: 1.1rem;">
                            <i class="fas fa-magic text-primary me-2"></i>
                            <strong>Už nemusíte číst titulky v oblíbených filmech a seriálech, naše aplikace Vám je přečte za Vás.</strong>
                        </div>
                        <p class="text-muted">Standardní zobrazení pro Windows, macOS a Linux</p>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="version-demo">
                        <h4><i class="fas fa-mobile-alt text-warning"></i> Mobilní verze</h4>
                        <div class="alert alert-light intro-message" style="background: rgba(255,255,255,0.9); border: none; font-size: 1rem;">
                            <i class="fas fa-mobile-alt text-primary me-2"></i>
                            <strong>Detekován iOS - VOXO LOXO je aplikace pro Windows, macOS a Linux.</strong>
                        </div>
                        <p class="text-muted">Upravené zobrazení pro mobilní zařízení</p>
                    </div>
                </div>
            </div>
            
            <div class="row mt-4">
                <div class="col-12">
                    <div class="version-demo">
                        <h4><i class="fas fa-code text-success"></i> Technické detaily</h4>
                        <ul class="list-unstyled">
                            <li><strong>Animace:</strong> fadeInUp (1s ease-out)</li>
                            <li><strong>Responzivita:</strong> Automatické přizpůsobení na mobilních zařízeních</li>
                            <li><strong>Detekce OS:</strong> Dynamické zobrazení podle operačního systému</li>
                            <li><strong>Styling:</strong> Bootstrap alert s vlastními CSS úpravami</li>
                            <li><strong>Ikony:</strong> Font Awesome pro vizuální prvky</li>
                        </ul>
                        
                        <h5 class="mt-4">CSS kód:</h5>
                        <pre class="bg-light p-3 rounded"><code>.intro-message {
    animation: fadeInUp 1s ease-out;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    border-radius: 10px !important;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}</code></pre>
                    </div>
                </div>
            </div>
            
            <div class="text-center mt-4">
                <a href="index.php" class="btn btn-primary btn-lg">
                    <i class="fas fa-arrow-left"></i> Zpět na hlavní stránku
                </a>
            </div>
        </div>
    </section>
    
    <script>
        // Simulácia rôznych OS detekcie
        function simulateOSDetection() {
            const messages = [
                {
                    icon: 'fas fa-magic',
                    text: 'Už nemusíte číst titulky v oblíbených filmech a seriálech, naše aplikace Vám je přečte za Vás.',
                    type: 'desktop'
                },
                {
                    icon: 'fas fa-mobile-alt',
                    text: 'Detekován iOS - VOXO LOXO je aplikace pro Windows, macOS a Linux.',
                    type: 'mobile'
                },
                {
                    icon: 'fab fa-android',
                    text: 'Detekován Android - VOXO LOXO je aplikace pro Windows, macOS a Linux.',
                    type: 'mobile'
                }
            ];
            
            let currentIndex = 0;
            const heroMessage = document.querySelector('.hero-section .intro-message');
            
            setInterval(() => {
                const message = messages[currentIndex];
                heroMessage.innerHTML = `
                    <i class="${message.icon} text-primary me-2"></i>
                    <strong>${message.text}</strong>
                `;
                
                currentIndex = (currentIndex + 1) % messages.length;
            }, 4000);
        }
        
        // Spusti simuláciu po načítaní stránky
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(simulateOSDetection, 3000);
        });
    </script>
</body>
</html>
