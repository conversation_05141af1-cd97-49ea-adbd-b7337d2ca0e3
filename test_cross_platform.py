#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cross-platform testing script for Subtitle Reader
Tests all platform-specific functionality to ensure compatibility
"""

import sys
import logging
import platform
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_platform_detection():
    """Test platform detection and constants."""
    print("=" * 60)
    print("🔍 TESTING PLATFORM DETECTION")
    print("=" * 60)
    
    print(f"System: {platform.system()}")
    print(f"Platform: {platform.platform()}")
    print(f"Machine: {platform.machine()}")
    print(f"Python: {platform.python_version()}")
    
    try:
        from platform_utils import IS_MACOS, IS_WINDOWS, IS_LINUX, PLATFORM_NAME
        print(f"✅ Platform utils imported successfully")
        print(f"   IS_MACOS: {IS_MACOS}")
        print(f"   IS_WINDOWS: {IS_WINDOWS}")
        print(f"   IS_LINUX: {IS_LINUX}")
        print(f"   PLATFORM_NAME: {PLATFORM_NAME}")
        return True
    except ImportError as e:
        print(f"❌ Failed to import platform_utils: {e}")
        return False

def test_tts_provider():
    """Test TTS provider functionality."""
    print("\n" + "=" * 60)
    print("🎤 TESTING TTS PROVIDER")
    print("=" * 60)
    
    try:
        from platform_utils import get_tts_provider
        provider = get_tts_provider()
        print(f"✅ TTS provider created: {type(provider).__name__}")
        
        # Test voice detection
        voices = provider.get_available_voices()
        print(f"✅ Found {len(voices)} voices")
        
        # Show first few voices
        for i, voice in enumerate(voices[:3]):
            print(f"   Voice {i+1}: {voice.get('name', 'Unknown')} ({voice.get('language', 'Unknown')})")
        
        # Test speaking (short text)
        test_text = "Test"
        if voices:
            first_voice = voices[0]
            print(f"🔊 Testing speech with voice: {first_voice.get('name', 'Unknown')}")
            success = provider.speak(test_text, first_voice.get('id'), 200)
            print(f"   Speech test: {'✅ Success' if success else '❌ Failed'}")
        
        return True
    except Exception as e:
        print(f"❌ TTS provider test failed: {e}")
        return False

def test_active_app_detector():
    """Test active application detection."""
    print("\n" + "=" * 60)
    print("🖥️  TESTING ACTIVE APP DETECTOR")
    print("=" * 60)
    
    try:
        from platform_utils import get_active_app_detector
        detector = get_active_app_detector()
        print(f"✅ Active app detector created: {type(detector).__name__}")
        
        app_name = detector.get_active_app_name()
        print(f"✅ Current active app: {app_name}")
        
        return True
    except Exception as e:
        print(f"❌ Active app detector test failed: {e}")
        return False

def test_system_info_provider():
    """Test system information provider."""
    print("\n" + "=" * 60)
    print("💻 TESTING SYSTEM INFO PROVIDER")
    print("=" * 60)
    
    try:
        from platform_utils import get_system_info_provider
        provider = get_system_info_provider()
        print(f"✅ System info provider created: {type(provider).__name__}")
        
        # Test hardware fingerprint
        fingerprint = provider.get_hardware_fingerprint()
        print(f"✅ Hardware fingerprint: {fingerprint[:16]}...")
        
        # Test system language
        language = provider.get_system_language()
        print(f"✅ System language: {language}")
        
        return True
    except Exception as e:
        print(f"❌ System info provider test failed: {e}")
        return False

def test_browser_url_detector():
    """Test browser URL detection."""
    print("\n" + "=" * 60)
    print("🌐 TESTING BROWSER URL DETECTOR")
    print("=" * 60)
    
    try:
        from platform_utils import get_browser_url_detector
        detector = get_browser_url_detector()
        print(f"✅ Browser URL detector created: {type(detector).__name__}")
        
        url = detector.get_browser_url()
        if url:
            print(f"✅ Current browser URL: {url[:50]}...")
        else:
            print("ℹ️  No browser URL detected (browser may not be open)")
        
        return True
    except Exception as e:
        print(f"❌ Browser URL detector test failed: {e}")
        return False

def test_cross_platform_utilities():
    """Test cross-platform utility functions."""
    print("\n" + "=" * 60)
    print("🛠️  TESTING CROSS-PLATFORM UTILITIES")
    print("=" * 60)
    
    try:
        from platform_utils import (
            get_app_data_dir, get_temp_dir, get_documents_dir,
            normalize_hotkey_combination, ensure_directory_exists
        )
        
        # Test directory functions
        app_data = get_app_data_dir("SubtitleReader")
        temp_dir = get_temp_dir()
        docs_dir = get_documents_dir()
        
        print(f"✅ App data dir: {app_data}")
        print(f"✅ Temp dir: {temp_dir}")
        print(f"✅ Documents dir: {docs_dir}")
        
        # Test hotkey normalization
        original_hotkey = '<cmd>+<alt>+<ctrl>+v'
        normalized = normalize_hotkey_combination(original_hotkey)
        print(f"✅ Hotkey normalization: {original_hotkey} -> {normalized}")
        
        # Test directory creation
        test_dir = app_data / "test"
        ensure_directory_exists(test_dir)
        print(f"✅ Directory creation test: {test_dir.exists()}")
        
        return True
    except Exception as e:
        print(f"❌ Cross-platform utilities test failed: {e}")
        return False

def main():
    """Run all cross-platform tests."""
    print("🚀 SUBTITLE READER - CROSS-PLATFORM TESTING")
    print(f"Running on: {platform.system()} {platform.release()}")
    
    tests = [
        test_platform_detection,
        test_tts_provider,
        test_active_app_detector,
        test_system_info_provider,
        test_browser_url_detector,
        test_cross_platform_utilities
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            results.append(False)
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    print(f"Tests passed: {passed}/{total}")
    print(f"Success rate: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 All tests passed! Cross-platform implementation is working.")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
