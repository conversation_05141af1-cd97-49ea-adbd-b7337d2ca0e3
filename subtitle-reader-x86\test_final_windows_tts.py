#!/usr/bin/env python3
"""
Finálny test Windows TTS integrácie
Testuje kompletný workflow od detekcie hlasov po TTS prehrávanie
"""

import sys
import logging
import time
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('final_tts_test.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

def test_complete_tts_workflow():
    """Test kompletného TTS workflow na Windows."""
    print("🚀 Finálny Windows TTS Test")
    print("=" * 60)
    
    try:
        # 1. Test platform detection
        print("🔍 1. Testovanie platform detekcie...")
        from platform_utils import get_tts_provider
        tts_provider = get_tts_provider()
        print(f"✅ TTS Provider: {type(tts_provider).__name__}")
        
        # 2. Test voice detection
        print("\n🔍 2. Testovanie detekcie hlasov...")
        voices = tts_provider.get_available_voices()
        print(f"✅ Nájdené hlasy: {len(voices)}")
        
        for i, voice in enumerate(voices):
            print(f"   {i+1}. {voice['name']} ({voice['language']}) - {voice['quality']}")
        
        if not voices:
            print("❌ Žiadne hlasy nenájdené!")
            return False
            
        # 3. Test language detector integration
        print("\n🔍 3. Testovanie LanguageDetector integrácie...")
        from language_detector import LanguageDetector
        detector = LanguageDetector()
        
        # Test TTS language detection
        tts_langs = detector.detect_apple_tts_languages()
        print(f"✅ Detekované TTS jazyky: {sorted(tts_langs)}")
        
        # 4. Test voice selection for specific language
        print("\n🔍 4. Testovanie výberu hlasov pre konkrétny jazyk...")
        test_lang = 'en'  # English should be available
        voices_for_lang = detector.get_voices_for_language(test_lang)
        print(f"✅ Hlasy pre {test_lang}: {len(voices_for_lang)}")
        
        for voice in voices_for_lang:
            print(f"   - {voice['name']} ({voice.get('locale', 'N/A')}) - {voice.get('quality', 'standard')}")
        
        # 5. Test TTS playback
        print("\n🔍 5. Testovanie TTS prehrávania...")
        if voices_for_lang:
            test_voice = voices_for_lang[0]
            test_text = "Hello, this is a Windows TTS test."
            
            print(f"🎤 Testovanie hlasu: {test_voice['name']}")
            print(f"📝 Text: {test_text}")
            
            # Test TTS speak
            success = tts_provider.speak(test_text, test_voice['name'], rate=200)
            if success:
                print("✅ TTS prehrávanie úspešné!")
                time.sleep(3)  # Wait for speech to complete
            else:
                print("❌ TTS prehrávanie zlyhalo!")
                return False
        
        # 6. Test TTS stop
        print("\n🔍 6. Testovanie TTS stop...")
        stop_success = tts_provider.stop()
        print(f"✅ TTS stop: {'úspešné' if stop_success else 'zlyhalo'}")
        
        print("\n" + "=" * 60)
        print("🎉 VŠETKY TESTY ÚSPEŠNÉ!")
        print("✅ Windows TTS integrácia funguje správne")
        print("✅ Hlasy sa detekujú a načítavajú")
        print("✅ TTS prehrávanie funguje")
        print("✅ Cross-platform architektúra je funkčná")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test zlyhal: {e}")
        logging.exception("Test error")
        return False

def test_gui_integration():
    """Test integrácie s GUI."""
    print("\n🔍 Testovanie GUI integrácie...")
    
    try:
        from i18n_manager import LanguageManager
        
        # Create language manager
        lang_manager = LanguageManager()
        
        # Test voice loading for GUI
        test_lang = 'en'
        voices = lang_manager.get_voices_for_language(test_lang)
        
        print(f"✅ GUI voices pre {test_lang}: {len(voices)}")
        for voice in voices:
            print(f"   - {voice.get('name', 'Unknown')}")
            
        return len(voices) > 0
        
    except Exception as e:
        print(f"❌ GUI integrácia zlyhala: {e}")
        return False

if __name__ == "__main__":
    print("🎯 Spúšťam finálny Windows TTS test...")
    
    # Test complete workflow
    workflow_success = test_complete_tts_workflow()
    
    # Test GUI integration
    gui_success = test_gui_integration()
    
    print("\n" + "=" * 60)
    print("📊 FINÁLNE VÝSLEDKY:")
    print(f"   🔧 TTS Workflow: {'✅ ÚSPEŠNÝ' if workflow_success else '❌ ZLYHAL'}")
    print(f"   🖥️ GUI Integrácia: {'✅ ÚSPEŠNÁ' if gui_success else '❌ ZLYHALA'}")
    
    if workflow_success and gui_success:
        print("\n🎉 KOMPLETNÁ WINDOWS TTS INTEGRÁCIA JE FUNKČNÁ!")
        print("🚀 Aplikácia je pripravená na použitie s Windows hlasmi")
        sys.exit(0)
    else:
        print("\n❌ Niektoré testy zlyhali - skontrolujte logy")
        sys.exit(1)
