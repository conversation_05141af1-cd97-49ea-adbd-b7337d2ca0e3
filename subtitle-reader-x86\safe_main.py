#!/usr/bin/env python3
"""
Bezpečná verzia aplikácie bez problematického TTS
"""

import sys
import os
import logging
from PyQt6 import QtWidgets, QtCore, QtGui

# <PERSON>ak<PERSON>ž TTS globálne
os.environ['DISABLE_TTS'] = '1'

def setup_safe_logging():
    """Bezpečné logovanie"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('safe_app.log', encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )

class SafeSubtitleReader(QtWidgets.QMainWindow):
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        print("🖥️ Inicializujem bezpečné GUI...")
        
        self.setWindowTitle("Subtitle Reader - Safe Mode (No TTS)")
        self.setGeometry(100, 100, 900, 700)
        
        # Centrálny widget
        central_widget = QtWidgets.QWidget()
        self.setCentralWidget(central_widget)
        
        # Layout
        layout = QtWidgets.QVBoxLayout()
        central_widget.setLayout(layout)
        
        # Nadpis
        title = QtWidgets.QLabel("🎬 Subtitle Reader - Safe Mode")
        title.setStyleSheet("font-size: 28px; font-weight: bold; padding: 20px; color: #ffffff;")
        title.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)
        
        # Varovanie
        warning = QtWidgets.QLabel("⚠️ TTS je zakázané kvôli nestabilite na Windows ARM64")
        warning.setStyleSheet("font-size: 14px; padding: 10px; background-color: #ff6b35; color: #ffffff; border-radius: 5px;")
        warning.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(warning)
        
        # Status
        self.status_label = QtWidgets.QLabel("✅ Bezpečný režim aktívny - len OCR funkcie")
        self.status_label.setStyleSheet("font-size: 16px; padding: 15px; background-color: #2d2d2d; color: #00ff00; border-radius: 5px;")
        self.status_label.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.status_label)
        
        # Ovládacie tlačidlá
        control_layout = QtWidgets.QHBoxLayout()
        
        self.select_area_btn = QtWidgets.QPushButton("🎯 Vybrať oblasť")
        self.select_area_btn.setStyleSheet("font-size: 16px; padding: 15px; background-color: #2196F3; color: white; border: none; border-radius: 8px;")
        self.select_area_btn.clicked.connect(self.select_area)
        control_layout.addWidget(self.select_area_btn)
        
        self.start_btn = QtWidgets.QPushButton("▶️ Spustiť OCR")
        self.start_btn.setStyleSheet("font-size: 16px; padding: 15px; background-color: #4CAF50; color: white; border: none; border-radius: 8px;")
        self.start_btn.clicked.connect(self.start_ocr)
        control_layout.addWidget(self.start_btn)
        
        self.stop_btn = QtWidgets.QPushButton("⏹️ Zastaviť")
        self.stop_btn.setStyleSheet("font-size: 16px; padding: 15px; background-color: #f44336; color: white; border: none; border-radius: 8px;")
        self.stop_btn.clicked.connect(self.stop_ocr)
        self.stop_btn.setEnabled(False)
        control_layout.addWidget(self.stop_btn)
        
        layout.addLayout(control_layout)
        
        # Nastavenia
        settings_group = QtWidgets.QGroupBox("⚙️ Nastavenia")
        settings_group.setStyleSheet("QGroupBox { font-size: 14px; font-weight: bold; color: #ffffff; }")
        settings_layout = QtWidgets.QFormLayout()
        
        # Jazyk OCR
        self.ocr_language = QtWidgets.QComboBox()
        self.ocr_language.addItems(["eng", "ces", "slk", "deu", "fra"])
        self.ocr_language.setCurrentText("eng")
        settings_layout.addRow("🔍 OCR Jazyk:", self.ocr_language)
        
        # Interval skenovania
        self.scan_interval = QtWidgets.QSpinBox()
        self.scan_interval.setRange(1, 10)
        self.scan_interval.setValue(2)
        self.scan_interval.setSuffix(" s")
        settings_layout.addRow("⏱️ Interval:", self.scan_interval)
        
        settings_group.setLayout(settings_layout)
        layout.addWidget(settings_group)
        
        # Výstupná oblasť
        output_group = QtWidgets.QGroupBox("📖 Rozpoznaný text")
        output_group.setStyleSheet("QGroupBox { font-size: 14px; font-weight: bold; color: #ffffff; }")
        output_layout = QtWidgets.QVBoxLayout()
        
        self.output_area = QtWidgets.QTextEdit()
        self.output_area.setPlaceholderText("Tu sa budú zobrazovať rozpoznané titulky...")
        self.output_area.setStyleSheet("""
            QTextEdit {
                font-size: 14px; 
                padding: 15px; 
                background-color: #0d1117; 
                color: #c9d1d9; 
                border: 2px solid #30363d; 
                border-radius: 8px;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
        """)
        output_layout.addWidget(self.output_area)
        
        # Tlačidlá pre výstup
        output_buttons = QtWidgets.QHBoxLayout()
        
        clear_btn = QtWidgets.QPushButton("🗑️ Vymazať")
        clear_btn.clicked.connect(self.output_area.clear)
        output_buttons.addWidget(clear_btn)
        
        copy_btn = QtWidgets.QPushButton("📋 Kopírovať")
        copy_btn.clicked.connect(self.copy_text)
        output_buttons.addWidget(copy_btn)
        
        output_buttons.addStretch()
        output_layout.addLayout(output_buttons)
        
        output_group.setLayout(output_layout)
        layout.addWidget(output_group)
        
        # Tmavý štýl
        self.setStyleSheet("""
            QMainWindow {
                background-color: #1e1e1e;
                color: #ffffff;
            }
            QLabel, QGroupBox {
                color: #ffffff;
            }
            QPushButton {
                font-size: 12px;
                padding: 8px;
                background-color: #404040;
                color: white;
                border: none;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #505050;
            }
            QComboBox, QSpinBox {
                padding: 5px;
                background-color: #2d2d2d;
                color: #ffffff;
                border: 1px solid #555;
                border-radius: 4px;
            }
        """)
        
        # Zobraz okno
        self.show()
        self.raise_()
        self.activateWindow()
        
        print("✅ Bezpečné GUI vytvorené a zobrazené")
        self.log("🚀 Bezpečná aplikácia spustená (TTS zakázané)")
        
        # Timer pre OCR
        self.ocr_timer = QtCore.QTimer()
        self.ocr_timer.timeout.connect(self.perform_ocr)
        self.is_running = False
    
    def log(self, message):
        """Pridaj správu do výstupu"""
        timestamp = QtCore.QTime.currentTime().toString("hh:mm:ss")
        self.output_area.append(f"[{timestamp}] {message}")
        logging.info(message)
    
    def select_area(self):
        """Simulácia výberu oblasti"""
        self.log("🎯 Výber oblasti - simulácia (implementácia OCR výberu)")
        self.status_label.setText("📍 Oblasť vybraná - pripravené na OCR")
        self.status_label.setStyleSheet("font-size: 16px; padding: 15px; background-color: #2d2d2d; color: #ffff00; border-radius: 5px;")
    
    def start_ocr(self):
        """Spusti OCR skenovanie"""
        if not self.is_running:
            self.is_running = True
            interval = self.scan_interval.value() * 1000  # ms
            self.ocr_timer.start(interval)
            
            self.start_btn.setEnabled(False)
            self.stop_btn.setEnabled(True)
            
            self.status_label.setText("🔄 OCR skenovanie aktívne")
            self.status_label.setStyleSheet("font-size: 16px; padding: 15px; background-color: #2d2d2d; color: #00ff00; border-radius: 5px;")
            self.log("▶️ OCR skenovanie spustené")
    
    def stop_ocr(self):
        """Zastav OCR skenovanie"""
        if self.is_running:
            self.is_running = False
            self.ocr_timer.stop()
            
            self.start_btn.setEnabled(True)
            self.stop_btn.setEnabled(False)
            
            self.status_label.setText("⏹️ OCR skenovanie zastavené")
            self.status_label.setStyleSheet("font-size: 16px; padding: 15px; background-color: #2d2d2d; color: #ff6b35; border-radius: 5px;")
            self.log("⏹️ OCR skenovanie zastavené")
    
    def perform_ocr(self):
        """Vykonaj OCR (simulácia)"""
        try:
            # Simulácia OCR - v skutočnej aplikácii by tu bol screenshot + tesseract
            import random
            sample_texts = [
                "Toto je ukážkový titulok",
                "OCR funguje správne",
                "Rozpoznávanie textu aktívne",
                "Subtitle Reader - Windows ARM64",
                "Testovací text pre OCR"
            ]
            
            if random.random() > 0.7:  # 30% šanca na "rozpoznanie" textu
                text = random.choice(sample_texts)
                self.log(f"📖 OCR: {text}")
                
        except Exception as e:
            self.log(f"❌ OCR chyba: {e}")
    
    def copy_text(self):
        """Kopíruj text do schránky"""
        text = self.output_area.toPlainText()
        clipboard = QtWidgets.QApplication.clipboard()
        clipboard.setText(text)
        self.log("📋 Text skopírovaný do schránky")
    
    def closeEvent(self, event):
        """Ukončenie aplikácie"""
        if self.is_running:
            self.stop_ocr()
        self.log("👋 Aplikácia ukončená")
        event.accept()

def main():
    setup_safe_logging()
    print("🚀 Spúšťam bezpečnú verziu Subtitle Reader...")
    print("⚠️ TTS je zakázané kvôli nestabilite na Windows ARM64")
    
    # Vytvor aplikáciu
    app = QtWidgets.QApplication(sys.argv)
    app.setApplicationName("Subtitle Reader Safe")
    
    # Vytvor hlavné okno
    window = SafeSubtitleReader()
    
    print("🖥️ Bezpečné GUI okno by sa malo zobraziť")
    
    # Spusti event loop
    try:
        sys.exit(app.exec())
    except KeyboardInterrupt:
        print("👋 Bezpečná aplikácia ukončená")
        sys.exit(0)

if __name__ == "__main__":
    main()
