# 🪟 Windows Testing Guide - <PERSON><PERSON><PERSON><PERSON><PERSON> testovací plán

## 🎯 <PERSON>ieľ testovania

Overiť, že zlúčená aplikácia funguje správne na Windows systéme s platform loader mechanizmom.

## 🛠️ Príprava testovacieho prostredia

### Systémové požiadavky

#### Windows x86/x64
- **OS:** Windows 10/11 (64-bit)
- **Python:** 3.8 alebo vyšší
- **RAM:** Minimálne 4GB
- **Disk:** 500MB voľného miesta
- **<PERSON><PERSON>k<PERSON> z<PERSON>ti:** comtypes, winrt-Windows.*

#### Windows ARM64
- **OS:** Windows 11 ARM64
- **Python:** 3.10+ (ARM64 natívny)
- **RAM:** Minimálne 4GB (odporúčané 8GB+)
- **Disk:** 500MB voľného miesta
- **<PERSON><PERSON><PERSON><PERSON> z<PERSON>losti:** Natívne WinRT API

### Inštalácia

#### Pre Windows x86/x64
```cmd
# 1. Klonuj repository
git clone <repository_url>
cd aplikacia/WM

# 2. Vytvor virtual environment
python -m venv venv
venv\Scripts\activate

# 3. Nainštaluj x86-špecifické závislosti
pip install -r requirements_x86.txt

# 4. Overiť inštaláciu
python -c "import platform; print(f'Platform: {platform.system()}, Arch: {platform.machine()}')"
```

#### Pre Windows ARM64
```cmd
# 1. Klonuj repository
git clone <repository_url>
cd aplikacia/WM

# 2. Vytvor virtual environment
python -m venv venv
venv\Scripts\activate

# 3. Nainštaluj univerzálne závislosti (ARM64 kompatibilné)
pip install -r requirements.txt

# 4. Overiť inštaláciu
python -c "import platform; print(f'Platform: {platform.system()}, Arch: {platform.machine()}')"
```

## 🧪 Testovací plán

### FÁZA 1: Základné testy spustenia

#### ✅ Test 1.1: Platform Detection
```cmd
python -c "from platform_loader import get_current_platform; print(get_current_platform())"
```
**Očakávaný výsledok:** `windows`

#### ✅ Test 1.2: Module Loading
```cmd
python -c "
from platform_loader import get_hotkey_manager, get_tts_manager
hm = get_hotkey_manager()
tm = get_tts_manager()
print(f'Hotkey manager: {type(hm).__name__}')
print(f'TTS manager: {type(tm).__name__}')
"
```
**Očakávaný výsledok:** 
- Hotkey manager: obsahuje "win"
- TTS manager: obsahuje "win"

#### ✅ Test 1.3: Aplikácia sa spustí
```cmd
python main_qt.py
```
**Očakávaný výsledok:**
- GUI okno sa otvorí
- Žiadne error dialógy
- V konzole: "🔧 Loaded Windows hotkey manager"

### FÁZA 2: Platform Loader testy

#### ✅ Test 2.1: TTS Functions Loading
```cmd
python -c "
from platform_loader import get_tts_function
speak = get_tts_function('speak_text')
init_worker = get_tts_function('init_tts_worker')
get_voices = get_tts_function('get_available_voices')
print('TTS functions loaded successfully')
"
```

#### ✅ Test 2.2: Hotkey Functions Loading  
```cmd
python -c "
from platform_loader import get_hotkey_function
register = get_hotkey_function('register_global_hotkeys')
print('Hotkey functions loaded successfully')
"
```

#### ✅ Test 2.3: Error Handling
```cmd
python -c "
from platform_loader import get_tts_function
nonexistent = get_tts_function('nonexistent_function')
result = nonexistent('test')
print('Error handling works correctly')
"
```

### FÁZA 3: Windows TTS testy

#### ✅ Test 3.1: Základný TTS test
```cmd
python -c "
from platform_loader import get_tts_function
speak_text = get_tts_function('speak_text')
speak_text('Hello Windows TTS system')
print('TTS test completed')
"
```
**Očakávaný výsledok:** Počuť Windows TTS hlas

#### ✅ Test 3.2: Dostupné hlasy
```cmd
python -c "
from platform_loader import get_tts_function
get_voices = get_tts_function('get_available_voices')
voices = get_voices()
print(f'Available voices: {len(voices)}')
for voice in voices[:5]:
    print(f'  - {voice}')
"
```
**Očakávaný výsledok:** Zoznam Windows TTS hlasov

#### ✅ Test 3.3: TTS Worker inicializácia
```cmd
python -c "
from platform_loader import get_tts_function
init_worker = get_tts_function('init_tts_worker')
init_worker()
print('TTS worker initialized')
"
```

### FÁZA 4: Windows Hotkey testy

#### ✅ Test 4.1: Hotkey registrácia
1. Spustite aplikáciu: `python main_qt.py`
2. Skontrolujte logy pre: "Hotkeys registered successfully"
3. Nechajte aplikáciu bežať

#### ✅ Test 4.2: Funkčnosť hotkeys
**Testovanie jednotlivých hotkeys:**

| Hotkey | Akcia | Test |
|--------|-------|------|
| `Alt+X` | Toggle čítanie | Stlačte a overte zmenu stavu |
| `Alt+C` | Kopírovať text | Stlačte s textom na obrazovke |
| `Ctrl+Alt+I` | Nastavenia | Stlačte a overte otvorenie okna |

**Postup testovania:**
1. Otvorte textový dokument
2. Stlačte `Alt+X` - má sa spustiť čítanie
3. Stlačte znovu `Alt+X` - má sa zastaviť čítanie
4. Stlačte `Alt+C` - má sa kopírovať text pod kurzorom
5. Stlačte `Ctrl+Alt+I` - má sa otvoriť nastavenia

### FÁZA 5: GUI testy

#### ✅ Test 5.1: Všetky záložky
1. Spustite aplikáciu
2. Prejdite všetky záložky:
   - **Hlavná** - základné ovládanie
   - **Nastavenia** - konfigurácia
   - **Hlasy** - TTS nastavenia
   - **Detekcia** - OCR nastavenia
   - **O aplikácii** - informácie

#### ✅ Test 5.2: Zmena jazyka
1. Otvorte záložku "Nastavenia"
2. Zmeňte jazyk aplikácie
3. Reštartujte aplikáciu
4. Overte, že sa jazyk zmenil

#### ✅ Test 5.3: TTS nastavenia
1. Otvorte záložku "Hlasy"
2. Zmeňte TTS hlas
3. Otestujte nový hlas tlačidlom "Test"
4. Uložte nastavenia

### FÁZA 6: OCR testy

#### ✅ Test 6.1: OCR funkcionalita
1. Spustite aplikáciu
2. Stlačte `Alt+X` pre spustenie čítania
3. Otvorte dokument s textom
4. Overte, že sa text rozpoznáva a číta

#### ✅ Test 6.2: Rôzne jazyky
1. Nastavte jazyk čítania na "Slovak"
2. Otvorte slovenský text
3. Overte rozpoznanie
4. Zmeňte na "English" a otestujte anglický text

### FÁZA 7: Integračné testy

#### ✅ Test 7.1: Kompletný workflow
1. **Spustenie:** `python main_qt.py`
2. **Konfigurácia:** Nastavte slovenský jazyk a hlas
3. **Aktivácia:** Stlačte `Alt+X`
4. **Testovanie:** Otvorte PDF/dokument s textom
5. **Verifikácia:** Overte správne čítanie

#### ✅ Test 7.2: Dlhodobé testovanie
1. Nechajte aplikáciu bežať 30 minút
2. Pravidelne testujte hotkeys
3. Sledujte využitie pamäte
4. Overte stabilitu

### FÁZA 8: Error handling testy

#### ✅ Test 8.1: Chýbajúce závislosti
```cmd
# Simulácia chýbajúcej závislosti
pip uninstall keyboard -y
python main_qt.py
# Aplikácia by mala gracefully degradovať
pip install keyboard
```

#### ✅ Test 8.2: Neplatné nastavenia
1. Upravte `app_settings.json` s neplatnými hodnotami
2. Spustite aplikáciu
3. Overte, že sa použijú default hodnoty

#### ✅ Test 8.3: Nedostupné TTS hlasy
1. Nastavte neexistujúci TTS hlas
2. Spustite TTS test
3. Overte fallback na default hlas

## 📋 Checklist pre Windows vývojárov

### Pred testovaním
- [ ] Python 3.8+ nainštalovaný
- [ ] Virtual environment vytvorený
- [ ] Všetky závislosti nainštalované
- [ ] Windows TTS hlasy dostupné
- [ ] Administrátorské práva (pre hotkeys)

### Základné testy
- [ ] Platform detection funguje
- [ ] Windows moduly sa načítavajú
- [ ] Aplikácia sa spúšťa bez chýb
- [ ] GUI sa zobrazuje správne

### Funkcionalita
- [ ] TTS funguje s Windows hlasmi
- [ ] Hotkeys reagujú správne
- [ ] OCR rozpoznáva text
- [ ] Všetky záložky fungujú
- [ ] Nastavenia sa ukladajú

### Performance
- [ ] Aplikácia sa spúšťa do 10 sekúnd
- [ ] Hotkeys reagujú do 1 sekundy
- [ ] TTS sa spúšťa do 2 sekúnd
- [ ] OCR spracováva do 3 sekúnd

### Stabilita
- [ ] Žiadne memory leaks
- [ ] Žiadne crashes pri dlhodobom používaní
- [ ] Graceful handling chýb
- [ ] Správne cleanup pri ukončení

## 🐛 Známe problémy a riešenia

### Problém 1: Hotkeys nefungujú
**Príznaky:** Hotkeys sa neregistrujú alebo nereagujú
**Riešenie:**
```cmd
# Spustite ako administrátor
# Skontrolujte Windows security nastavenia
# Overte, že keyboard library je nainštalovaná
pip install keyboard --force-reinstall
```

### Problém 2: TTS nefunguje
**Príznaky:** Žiadny zvuk pri TTS teste
**Riešenie:**
```cmd
# Skontrolujte Windows TTS nastavenia
# Overte audio výstup
# Reštartujte Windows Audio službu
net stop audiosrv && net start audiosrv
```

### Problém 3: Import errors
**Príznaky:** ModuleNotFoundError pri spustení
**Riešenie:**
```cmd
# Overte virtual environment
# Reinstalujte závislosti
pip install -r requirements.txt --force-reinstall
```

## 📊 Reporting

### Test Report Template
```
WINDOWS TEST REPORT
==================
Dátum: [YYYY-MM-DD]
Tester: [Meno]
Windows verzia: [10/11]
Python verzia: [X.X.X]

VÝSLEDKY:
- Základné testy: [PASS/FAIL]
- Platform loader: [PASS/FAIL] 
- TTS funkcionalita: [PASS/FAIL]
- Hotkey funkcionalita: [PASS/FAIL]
- GUI testy: [PASS/FAIL]
- OCR testy: [PASS/FAIL]
- Integračné testy: [PASS/FAIL]

PROBLÉMY:
[Zoznam nájdených problémov]

ODPORÚČANIA:
[Návrhy na zlepšenie]
```

---
*Windows Testing Guide - Verzia 1.0*
*Pre otázky kontaktujte hlavného vývojára*
