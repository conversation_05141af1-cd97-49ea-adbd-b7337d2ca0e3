<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>iOS Safari Test - VOXO LOXO Pozadia</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        /* Hero sekcia - iOS optimalizovaná */
        .hero-section {
            background: 
                linear-gradient(rgba(25, 42, 86, 0.4), rgba(13, 27, 62, 0.4)),
                url('hero.png') center/cover no-repeat;
            color: white;
            padding: 120px 0;
            position: relative;
            min-height: 100vh;
            display: flex;
            align-items: center;
            /* Vždy scroll pre iOS */
            background-attachment: scroll;
        }
        
        .hero-section .container {
            width: 100%;
            max-width: 1400px;
        }
        
        /* FAQ sekcia - iOS optimalizovaná */
        .faq-section {
            background: 
                rgba(248, 249, 250, 0.9),
                url('faq.png') center/cover no-repeat;
            position: relative;
            min-height: 100vh;
            padding: 100px 0;
            /* Vždy scroll pre iOS */
            background-attachment: scroll;
        }
        
        .faq-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('faq.png') center/cover no-repeat;
            z-index: -1;
            /* Vždy scroll pre iOS */
            background-attachment: scroll;
        }
        
        /* Platformy sekcia */
        .platforms-section {
            background: url('overlay.png') center/cover no-repeat;
            position: relative;
            min-height: 500px;
            color: white;
            background-attachment: scroll;
        }
        
        /* Download sekcia */
        .download-section {
            background:
                linear-gradient(rgba(248, 249, 250, 0.1), rgba(248, 249, 250, 0.1)),
                url('download.png') center/cover no-repeat;
            position: relative;
            min-height: 600px;
            background-attachment: scroll;
        }
        
        /* iOS detekcia info */
        .ios-info {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 15px;
            border-radius: 10px;
            z-index: 1000;
            max-width: 300px;
        }
        
        .device-info {
            background: rgba(255,255,255,0.1);
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
        }
        
        /* Responzívne úpravy */
        @media (max-width: 768px) {
            .hero-section {
                padding: 80px 0;
                min-height: 90vh;
            }
            
            .hero-section h1 {
                font-size: 2.5rem !important;
            }
            
            .hero-section h2 {
                font-size: 1.8rem !important;
            }
            
            .ios-info {
                position: relative;
                top: auto;
                right: auto;
                margin: 20px;
                max-width: none;
            }
        }
    </style>
</head>
<body>
    <!-- iOS detekcia info -->
    <div class="ios-info">
        <h6><i class="fab fa-apple"></i> iOS Safari Test</h6>
        <div id="deviceInfo" class="device-info">
            <small>Detekujem zariadenie...</small>
        </div>
        <div class="mt-2">
            <small><strong>Status pozadí:</strong></small>
            <div id="backgroundStatus"></div>
        </div>
    </div>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container text-center">
            <div class="main-headline mb-5">
                <h1 class="display-1 fw-bold text-white mb-4" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.5); font-size: 4.5rem;">
                    <i class="fas fa-search me-4" style="color: white;"></i>
                    Už nemusíte číst titulky!
                </h1>
                <h2 class="display-3 fw-light text-white mb-5" style="font-size: 3rem;">
                    Naše čtečka titulků Vám je přečte
                </h2>
                <div class="brand-badge">
                    <span class="badge bg-light text-dark mb-3" style="font-size: 2.5rem; padding: 15px 30px; border-radius: 25px;">
                        VOXO LOXO
                    </span>
                    <div class="mt-3">
                        <span class="text-white" style="font-size: 1.8rem; font-weight: 300;">čtečka titulků</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Platforms Section -->
    <section class="platforms-section py-5">
        <div class="container">
            <h2 class="text-center mb-5">Podporované platformy</h2>
            <div class="row">
                <div class="col-md-4">
                    <div class="card bg-dark text-white">
                        <div class="card-body text-center">
                            <i class="fab fa-windows fa-3x mb-3"></i>
                            <h4>Windows</h4>
                            <p>Windows 10/11 s SAPI TTS</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-dark text-white">
                        <div class="card-body text-center">
                            <i class="fab fa-apple fa-3x mb-3"></i>
                            <h4>macOS</h4>
                            <p>macOS s Apple TTS</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-dark text-white">
                        <div class="card-body text-center">
                            <i class="fab fa-linux fa-3x mb-3"></i>
                            <h4>Linux</h4>
                            <p>Linux s espeak TTS</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Download Section -->
    <section class="download-section py-5">
        <div class="container">
            <h2 class="text-center mb-5">Stáhnout aplikaci</h2>
            <div class="row">
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <i class="fab fa-windows fa-3x text-primary mb-3"></i>
                            <h4>Windows</h4>
                            <button class="btn btn-primary">Stáhnout</button>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <i class="fab fa-apple fa-3x text-success mb-3"></i>
                            <h4>macOS</h4>
                            <button class="btn btn-success">Stáhnout</button>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <i class="fab fa-linux fa-3x text-warning mb-3"></i>
                            <h4>Linux</h4>
                            <button class="btn btn-warning">Stáhnout</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="faq-section">
        <div class="container">
            <h2 class="text-center mb-5">Často kladené otázky</h2>
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-body">
                            <h5>Funguje na iOS Safari?</h5>
                            <p>Áno! Táto stránka je optimalizovaná pre iOS Safari s opravenými pozadiami.</p>
                            
                            <h5>Prečo sa pozadia nezobrazujú?</h5>
                            <p>iOS Safari má obmedzenia s background-attachment: fixed. Táto verzia používa scroll attachment.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <script>
        // iOS detekcia a diagnostika
        function detectDevice() {
            const userAgent = navigator.userAgent;
            const platform = navigator.platform;
            const isIOS = /iPad|iPhone|iPod/.test(userAgent) && !window.MSStream;
            const isSafari = /Safari/.test(userAgent) && !/Chrome/.test(userAgent);
            
            const deviceInfo = {
                userAgent: userAgent,
                platform: platform,
                isIOS: isIOS,
                isSafari: isSafari,
                viewport: `${window.innerWidth}x${window.innerHeight}`,
                devicePixelRatio: window.devicePixelRatio
            };
            
            return deviceInfo;
        }
        
        function updateDeviceInfo() {
            const info = detectDevice();
            const deviceInfoEl = document.getElementById('deviceInfo');
            
            deviceInfoEl.innerHTML = `
                <strong>Zariadenie:</strong> ${info.isIOS ? 'iOS' : 'Iné'}<br>
                <strong>Safari:</strong> ${info.isSafari ? 'Áno' : 'Nie'}<br>
                <strong>Rozlíšenie:</strong> ${info.viewport}<br>
                <strong>DPR:</strong> ${info.devicePixelRatio}
            `;
        }
        
        function checkBackgrounds() {
            const images = ['hero.png', 'faq.png', 'overlay.png', 'download.png'];
            const statusEl = document.getElementById('backgroundStatus');
            let statusHTML = '';
            
            images.forEach(img => {
                const testImg = new Image();
                testImg.onload = function() {
                    statusHTML += `<small class="text-success">✓ ${img}</small><br>`;
                    statusEl.innerHTML = statusHTML;
                };
                testImg.onerror = function() {
                    statusHTML += `<small class="text-danger">✗ ${img}</small><br>`;
                    statusEl.innerHTML = statusHTML;
                };
                testImg.src = img;
            });
        }
        
        // Inicializácia
        document.addEventListener('DOMContentLoaded', function() {
            updateDeviceInfo();
            checkBackgrounds();
        });
        
        // Update pri zmene orientácie
        window.addEventListener('orientationchange', function() {
            setTimeout(updateDeviceInfo, 500);
        });
    </script>
</body>
</html>
