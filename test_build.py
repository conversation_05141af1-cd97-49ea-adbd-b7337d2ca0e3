#!/usr/bin/env python3
"""
Test script pre overenie build procesu
"""

import os
import sys
import subprocess

def test_build():
    build_dir = "/Users/<USER>/SubtitleReader_Build"
    app_path = f"{build_dir}/dist/Subtitle Reader.app"
    
    print("🔍 Testovanie build...")
    
    # Skontroluj či existuje aplikácia
    if os.path.exists(app_path):
        print(f"✅ Aplikácia existuje: {app_path}")
        
        # Skontroluj veľkosť
        try:
            result = subprocess.run(['du', '-sh', app_path], capture_output=True, text=True)
            print(f"📦 Veľkosť: {result.stdout.strip()}")
        except:
            print("⚠️ Nemôžem zistiť veľkosť")
        
        # Skúsaj spustiť aplikáciu
        executable = f"{app_path}/Contents/MacOS/Subtitle Reader"
        if os.path.exists(executable):
            print(f"✅ Executable existuje: {executable}")
            
            print("🚀 Skúšam spustiť aplikáciu...")
            try:
                result = subprocess.run([executable], 
                                      capture_output=True, 
                                      text=True, 
                                      timeout=10)
                print(f"📤 Return code: {result.returncode}")
                if result.stdout:
                    print(f"📤 STDOUT:\n{result.stdout}")
                if result.stderr:
                    print(f"❌ STDERR:\n{result.stderr}")
            except subprocess.TimeoutExpired:
                print("⏰ Aplikácia beží dlhšie ako 10 sekúnd - pravdepodobne funguje!")
            except Exception as e:
                print(f"❌ Chyba pri spúšťaní: {e}")
        else:
            print(f"❌ Executable neexistuje: {executable}")
    else:
        print(f"❌ Aplikácia neexistuje: {app_path}")
        
        # Skontroluj build adresár
        if os.path.exists(build_dir):
            print(f"📁 Build adresár existuje: {build_dir}")
            try:
                files = os.listdir(build_dir)
                print(f"📁 Obsah: {files}")
            except:
                print("❌ Nemôžem čítať build adresár")
        else:
            print(f"❌ Build adresár neexistuje: {build_dir}")

if __name__ == "__main__":
    test_build()
