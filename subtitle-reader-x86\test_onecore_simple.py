#!/usr/bin/env python3
"""
Jednoduchý test OneCore hlasov
"""

import asyncio
import sys

async def test_simple_onecore():
    """Jednoduchý test OneCore."""
    print("🚀 Jednoduchý OneCore Test")
    print("=" * 50)
    
    try:
        # Import WinRT
        from winrt.windows.media.speechsynthesis import SpeechSynthesizer
        from winrt.windows.media.speechsynthesis import VoiceInformation
        
        print("✅ WinRT importy úspešné")
        
        # Create synthesizer
        synthesizer = SpeechSynthesizer()
        print("✅ SpeechSynthesizer vytvorený")
        
        # Get current voice
        current_voice = synthesizer.voice
        if current_voice:
            print(f"🎤 Aktuálny hlas: {current_voice.display_name}")
            print(f"   Jazyk: {current_voice.language}")
            print(f"   Pohlavie: {current_voice.gender}")
        else:
            print("❌ Žiadny aktuálny hlas")
        
        # Try to get all voices using static method
        try:
            from winrt.windows.media.speechsynthesis import SpeechSynthesizer as SS
            voices = SS.all_voices
            print(f"✅ Nájdené hlasy cez SS.all_voices: {len(voices)}")
            
            for i, voice in enumerate(voices):
                print(f"   {i+1}. {voice.display_name} ({voice.language}) - {voice.gender}")
                
        except Exception as e:
            print(f"❌ Chyba pri SS.all_voices: {e}")
            
            # Try alternative approach
            try:
                import winrt.windows.media.speechsynthesis as speech
                voices = speech.SpeechSynthesizer.all_voices
                print(f"✅ Nájdené hlasy cez speech.SpeechSynthesizer.all_voices: {len(voices)}")
                
                for i, voice in enumerate(voices):
                    print(f"   {i+1}. {voice.display_name} ({voice.language}) - {voice.gender}")
                    
            except Exception as e2:
                print(f"❌ Chyba pri alternatívnom prístupe: {e2}")
                
                # Show available attributes
                print(f"🔍 Dostupné atribúty SpeechSynthesizer: {[attr for attr in dir(SpeechSynthesizer) if not attr.startswith('_')]}")
                return False
        
        # Test synthesis
        print("\n🔍 Test syntézy...")
        test_text = "Hello, this is a OneCore test."
        
        try:
            stream = await synthesizer.synthesize_text_to_stream_async(test_text)
            print(f"✅ Syntéza úspešná, stream size: {stream.size}")
            
            # Save to file
            from winrt.windows.storage.streams import DataReader
            reader = DataReader(stream)
            await reader.load_async(stream.size)
            data = bytearray(stream.size)
            reader.read_bytes(data)
            
            with open("onecore_simple_test.wav", "wb") as f:
                f.write(data)
            
            print(f"💾 Audio uložené ({len(data)} bajtov)")
            
        except Exception as e:
            print(f"❌ Syntéza zlyhala: {e}")
            return False
        
        print("\n🎉 OneCore test úspešný!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Všeobecná chyba: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    result = asyncio.run(test_simple_onecore())
    sys.exit(0 if result else 1)
