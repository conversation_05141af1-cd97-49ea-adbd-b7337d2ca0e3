@echo off
REM Windows setup script for Subtitle Reader
echo ========================================
echo   Subtitle Reader - Windows Setup
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.10+ from https://python.org
    echo Make sure to check "Add Python to PATH" during installation
    pause
    exit /b 1
)

echo ✅ Python found:
python --version

REM Check if pip is available
pip --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: pip is not available
    echo Please reinstall Python with pip included
    pause
    exit /b 1
)

echo ✅ pip found:
pip --version
echo.

REM Create virtual environment
echo 🔧 Creating virtual environment...
if exist .venv (
    echo Virtual environment already exists, removing old one...
    rmdir /s /q .venv
)

python -m venv .venv
if errorlevel 1 (
    echo ERROR: Failed to create virtual environment
    pause
    exit /b 1
)

echo ✅ Virtual environment created
echo.

REM Activate virtual environment
echo 🔧 Activating virtual environment...
call .venv\Scripts\activate.bat
if errorlevel 1 (
    echo ERROR: Failed to activate virtual environment
    pause
    exit /b 1
)

echo ✅ Virtual environment activated
echo.

REM Upgrade pip
echo 🔧 Upgrading pip...
python -m pip install --upgrade pip

REM Install dependencies
echo 🔧 Installing Windows dependencies...
pip install -r requirements_windows.txt
if errorlevel 1 (
    echo ERROR: Failed to install dependencies
    echo Please check the error messages above
    pause
    exit /b 1
)

echo ✅ Dependencies installed successfully
echo.

REM Check for Tesseract
echo 🔧 Checking for Tesseract OCR...
tesseract --version >nul 2>&1
if errorlevel 1 (
    echo ⚠️  WARNING: Tesseract OCR not found in PATH
    echo Please install Tesseract from:
    echo https://github.com/UB-Mannheim/tesseract/wiki
    echo.
    echo After installation, either:
    echo 1. Add Tesseract to your PATH, or
    echo 2. Set TESSERACT_CMD environment variable
    echo.
) else (
    echo ✅ Tesseract OCR found:
    tesseract --version | findstr "tesseract"
)

echo.
echo ========================================
echo   Setup Complete!
echo ========================================
echo.
echo To run the application:
echo 1. Activate virtual environment: .venv\Scripts\activate.bat
echo 2. Run the application: python main_qt.py
echo.
echo For global hotkeys to work, you may need to:
echo - Run as administrator, or
echo - Allow the application through Windows Defender
echo.
echo Press any key to exit...
pause >nul
