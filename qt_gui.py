from PyQt6 import QtCore, QtGui, QtWidgets
import logging

import common_config as config
from i18n_manager import get_translator, get_language_manager, set_app_language, set_reading_language
from demo_timer import get_demo_widget
from macos_dark_theme import get_main_window_style, get_button_styles, get_text_styles, get_overlay_style, MACOS_DARK_COLORS


class ComboBoxScrollFilter(QtCore.QObject):
    """Event filter pre presmerovanie scrollovania z combobox-ov do parent widgetu."""

    def eventFilter(self, obj, event):
        # Presmeruj wheel events (scrollovanie) z combobox-ov do parent widgetu
        if isinstance(obj, QtWidgets.QComboBox) and event.type() == QtCore.QEvent.Type.Wheel:
            # Presmeruj len ak combobox nie je otvorený
            if not obj.view().isVisible():
                # Nájdi parent widget, ktor<PERSON> môže scrollovať
                parent = obj.parent()
                while parent:
                    # Hľadaj QScrollArea alebo widget s scroll bar-mi
                    if isinstance(parent, QtWidgets.QScrollArea):
                        # Pošli wheel event do scroll area
                        QtCore.QCoreApplication.sendEvent(parent, event)
                        return True  # Event spracovaný
                    elif hasattr(parent, 'verticalScrollBar') and parent.verticalScrollBar():
                        # Widget má vertical scroll bar
                        QtCore.QCoreApplication.sendEvent(parent, event)
                        return True  # Event spracovaný
                    parent = parent.parent()

                # Ak nenájdeme scrollovateľný parent, pošleme event do main window
                main_window = obj.window()
                if main_window and main_window != obj:
                    QtCore.QCoreApplication.sendEvent(main_window, event)
                    return True  # Event spracovaný

        return super().eventFilter(obj, event)


class SubtitleReaderQtGUI(QtWidgets.QMainWindow):
    """PySide6 PoC GUI – QMainWindow s toolbarom, tabmi a status labelom.
    Zachováva kľúčové callbacky a poskytuje kompatibilné metódy, ktoré backend očakáva.
    """

    # Signály pre thread‑safe aktualizácie z iných vlákien
    status_changed = QtCore.pyqtSignal(str)
    reading_state_changed = QtCore.pyqtSignal(bool)
    # 🔧 OPRAVA: Pridaj signály pre thread-safe zobrazenie dialógov
    show_warning_dialog = QtCore.pyqtSignal(str, str)  # title, text
    show_error_dialog = QtCore.pyqtSignal(str, str)  # title, text

    def __init__(self, on_closing_callback, toggle_reading_callback, set_reading_mode_callback):
        super().__init__()
        self.on_closing_callback = on_closing_callback
        self.toggle_reading_callback = toggle_reading_callback
        self.set_reading_mode_callback = set_reading_mode_callback

        # Inicializácia i18n
        logging.info("🔍 GUI: Inicializujem i18n systém...")
        self.translator = get_translator()
        logging.info("🔍 GUI: Translator vytvorený")
        self.language_manager = get_language_manager()
        logging.info("🔍 GUI: LanguageManager vytvorený")

        # Inicializácia event filtra pre combobox-y
        self.combo_scroll_filter = ComboBoxScrollFilter()

        self.setWindowTitle(self.translator.t("app_title"))
        # Bezpečná veľkosť a pozícia
        self.resize(700, 500)  # Trochu väčšie pre lepší vzhľad
        self.move(200, 150)

        # Aplikuj macOS tmavú tému
        self.setStyleSheet(get_main_window_style())

        # Always on top podľa konfigurácie
        self.apply_always_on_top(bool(getattr(config, "ALWAYS_ON_TOP", False)))

        # Central widget – taby
        central = QtWidgets.QWidget(self)
        self.setCentralWidget(central)
        # Qt QShortcut sa neinštalujú v __init__; rozhodnutie prebehne neskôr v main podľa stavu globálnych hotkeys

        vbox = QtWidgets.QVBoxLayout(central)
        vbox.setContentsMargins(8, 8, 8, 8)
        vbox.setSpacing(8)

        self.tabs = QtWidgets.QTabWidget(self)
        vbox.addWidget(self.tabs, 1)

        # Prekladačové parametre boli odstránené

        # Hlavná záložka s posuvníkom
        self.main_tab = QtWidgets.QWidget()
        self.tabs.addTab(self.main_tab, self.translator.t("main_tab"))

        # Vytvoríme scroll area pre celú záložku
        scroll_area = QtWidgets.QScrollArea(self.main_tab)
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(QtCore.Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(QtCore.Qt.ScrollBarPolicy.ScrollBarAsNeeded)

        # Hlavný layout pre záložku obsahuje len scroll area
        main_tab_layout = QtWidgets.QVBoxLayout(self.main_tab)
        main_tab_layout.setContentsMargins(0, 0, 0, 0)
        main_tab_layout.addWidget(scroll_area)

        # Vytvoríme widget pre obsah scroll area
        scroll_content = QtWidgets.QWidget()
        scroll_area.setWidget(scroll_content)

        # Layout pre obsah
        main_lay = QtWidgets.QVBoxLayout(scroll_content)
        main_lay.setSpacing(10)
        main_lay.setContentsMargins(10, 10, 10, 10)

        # Demo/License widget na vrchu
        try:
            self.demo_widget = get_demo_widget(self)
            main_lay.addWidget(self.demo_widget)
        except Exception as e:
            logging.error(f"❌ Chyba pri vytváraní demo widget: {e}")

        # Status label
        self.status_label = QtWidgets.QLabel(self.main_tab)
        self.status_label.setTextInteractionFlags(QtCore.Qt.TextInteractionFlag.TextSelectableByMouse)
        main_lay.addWidget(self.status_label)

        # ===== 1. SPUSTIT ČTENÍ (tlačidlo + obrázok) - HORE =====
        # 🔧 OPRAVA: Použij i18n systém pre komentár sekcie
        # Komentár: self.translator.t("start_reading_section")
        # Keyboard obrázok s overlay tlačidlom
        keyboard_container = QtWidgets.QWidget(self.main_tab)
        main_lay.addWidget(keyboard_container)
        keyboard_container.setMinimumHeight(180)  # Minimálna výška pre overlay

        # Obrázok klávesnice ako pozadie
        try:
            self.keyboard_label = QtWidgets.QLabel(keyboard_container)
            keyboard_pixmap = QtGui.QPixmap("keyboard_MAC_200.jpg")
            if not keyboard_pixmap.isNull():
                self.keyboard_label.setPixmap(keyboard_pixmap)
                self.keyboard_label.setScaledContents(True)
                self.keyboard_label.setGeometry(0, 0, 600, 180)  # Celá šírka a výška
            else:
                self.keyboard_label.setText(self.translator.t("keyboard_image_not_found"))
                text_styles = get_text_styles()
                self.keyboard_label.setStyleSheet(f"{text_styles['caption']}; background-color: {MACOS_DARK_COLORS['content_bg']};")
                self.keyboard_label.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
                self.keyboard_label.setGeometry(0, 0, 600, 180)
        except Exception as e:
            self.keyboard_label = QtWidgets.QLabel(keyboard_container)
            self.keyboard_label.setText(f"{self.translator.t('keyboard_image_error')} {e}")
            text_styles = get_text_styles()
            self.keyboard_label.setStyleSheet(f"{text_styles['caption']}; background-color: {MACOS_DARK_COLORS['content_bg']};")
            self.keyboard_label.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
            self.keyboard_label.setGeometry(0, 0, 600, 180)

        # Tlačítko overlay - vlevo dole nad obrázkem
        self.start_stop_btn = QtWidgets.QPushButton(self.translator.t("start_reading"), keyboard_container)
        self.start_stop_btn.setGeometry(15, 130, 150, 40)  # x, y, šírka, výška - trochu väčšie
        button_styles = get_button_styles()
        self.start_stop_btn.setStyleSheet(button_styles['primary'])
        self.start_stop_btn.clicked.connect(self._on_toggle_reading)
        self.start_stop_btn.raise_()  # Zabezpečí, že je nad obrázkom

        # Text overlay - vedľa tlačidla
        self.hotkey_label = QtWidgets.QLabel(self.translator.t("hotkey_hint"), keyboard_container)
        self._set_translation_key(self.hotkey_label, "hotkey_hint")
        self.hotkey_label.setGeometry(175, 135, 280, 30)  # x, y, šírka, výška - trochu väčšie
        self.hotkey_label.setStyleSheet(get_overlay_style())
        self.hotkey_label.raise_()  # Zabezpečí, že je nad obrázkom

        # Event handler pre resize kontajnera
        def resize_keyboard_elements():
            if hasattr(self, 'keyboard_label'):
                container_size = keyboard_container.size()
                self.keyboard_label.resize(container_size)

        keyboard_container.resizeEvent = lambda event: resize_keyboard_elements()

        # ===== 2. REŽIM ČTENÍ =====
        self.mode_group = QtWidgets.QGroupBox(self.translator.t("mode_group"), self.main_tab)
        self._set_translation_key(self.mode_group, "mode_group")
        main_lay.addWidget(self.mode_group)
        mode_lay = QtWidgets.QHBoxLayout(self.mode_group)
        mode_lay.setSpacing(30)  # Väčšie medzery medzi tlačidlami
        self.mode_buttons = {}

        # Pridaj menší stretch na začiatok (posun viac doľava)
        mode_lay.addStretch(1)

        for key, label_key in [("full_automatic", "mode_full_auto"), ("static", "mode_static"), ("dynamic", "mode_dynamic")]:
            btn = QtWidgets.QRadioButton(self.translator.t(label_key))
            btn.toggled.connect(lambda checked, k=key: self._on_mode_changed(k, checked))
            mode_lay.addWidget(btn)
            self.mode_buttons[key] = btn
            # Pridaj stretch medzi každým tlačidlom
            mode_lay.addStretch(2)

        # Pridaj väčší stretch na koniec
        mode_lay.addStretch(3)

        # ===== 3. JAZYK =====
        self.language_group = QtWidgets.QGroupBox(self.translator.t("language_group"), self.main_tab)
        self._set_translation_key(self.language_group, "language_group")
        main_lay.addWidget(self.language_group)
        language_form = QtWidgets.QGridLayout(self.language_group)
        language_form.setColumnStretch(1, 1)

        # Jazyk aplikácie
        language_form.addWidget(self._create_label_with_translation("app_language"), 0, 0)
        self.app_language_combo = QtWidgets.QComboBox()
        # Zakáž scrollovanie v combobox-e
        self.app_language_combo.installEventFilter(self.combo_scroll_filter)

        # Naplň len dostupné jazyky aplikácie
        for lang_code in self.language_manager.get_available_app_languages():
            display_name = self.language_manager.get_language_display_name(lang_code)
            self.app_language_combo.addItem(display_name, lang_code)

        # Nastav aktuálny jazyk
        current_app_lang = self.language_manager.current_app_language
        app_index = self.app_language_combo.findData(current_app_lang)
        if app_index >= 0:
            self.app_language_combo.setCurrentIndex(app_index)
        else:
            # Fallback na prvý dostupný jazyk
            if self.app_language_combo.count() > 0:
                self.app_language_combo.setCurrentIndex(0)

        self.app_language_combo.currentIndexChanged.connect(self._on_app_language_changed)
        language_form.addWidget(self.app_language_combo, 0, 1)

        # Jazyk čítania titulkov
        language_form.addWidget(self._create_label_with_translation("reading_language"), 1, 0)
        self.reading_language_combo = QtWidgets.QComboBox()
        # Zakáž scrollovanie v combobox-e
        self.reading_language_combo.installEventFilter(self.combo_scroll_filter)

        # Naplň len jazyky dostupné pre čítanie (OCR + TTS)
        for lang_code in self.language_manager.get_available_reading_languages():
            display_name = self.language_manager.get_language_display_name(lang_code)
            self.reading_language_combo.addItem(display_name, lang_code)

        # Nastav aktuálny jazyk
        current_reading_lang = self.language_manager.current_reading_language
        reading_index = self.reading_language_combo.findData(current_reading_lang)
        if reading_index >= 0:
            self.reading_language_combo.setCurrentIndex(reading_index)
        else:
            # Fallback na prvý dostupný jazyk
            if self.reading_language_combo.count() > 0:
                self.reading_language_combo.setCurrentIndex(0)

        self.reading_language_combo.currentIndexChanged.connect(self._on_reading_language_changed)
        language_form.addWidget(self.reading_language_combo, 1, 1)

        # Hlas pre čítanie titulkov
        language_form.addWidget(self._create_label_with_translation("voice_for_reading"), 2, 0)
        self.reading_voice_combo = QtWidgets.QComboBox()
        # Zakáž scrollovanie v combobox-e
        self.reading_voice_combo.installEventFilter(self.combo_scroll_filter)
        self.reading_voice_combo.setToolTip(self.translator.t("voice_for_reading_tooltip"))
        self.reading_voice_combo.currentIndexChanged.connect(self._on_reading_voice_changed)
        language_form.addWidget(self.reading_voice_combo, 2, 1)

        # Informačný label o dostupných jazykoch
        available_count = len(self.language_manager.get_available_reading_languages())
        total_ocr = len(self.language_manager.available_languages.get("ocr", set()))
        total_tts = len(self.language_manager.available_languages.get("tts", set()))

        info_text = self.translator.t("available_languages_info").format(count=available_count, ocr=total_ocr, tts=total_tts)
        self.language_info_label = QtWidgets.QLabel(info_text)
        text_styles = get_text_styles()
        self.language_info_label.setStyleSheet(text_styles['caption'])
        language_form.addWidget(self.language_info_label, 3, 0, 1, 2)

        # ===== 4. PREKLADAČ ODSTRÁNENÝ =====
        # Prekladač bol úplne odstránený zo systému



        # ===== 5. ŘEČ (TTS) =====
        self.tts_group = QtWidgets.QGroupBox(self.translator.t("tts_group"), self.main_tab)
        self._set_translation_key(self.tts_group, "tts_group")
        main_lay.addWidget(self.tts_group)
        tts_form = QtWidgets.QGridLayout(self.tts_group)
        tts_form.setColumnStretch(1, 1)

        # Rychlost řeči
        tts_form.addWidget(self._create_label_with_translation("speech_rate"), 0, 0)
        self.rate_slider = QtWidgets.QSlider(QtCore.Qt.Orientation.Horizontal)
        self.rate_slider.setMinimum(int(getattr(config, "MIN_TTS_RATE", 100)))
        self.rate_slider.setMaximum(int(getattr(config, "MAX_TTS_RATE", 400)))
        self.rate_slider.setSingleStep(int(getattr(config, "TTS_RATE_STEP", 20)))
        self.rate_slider.setTickInterval(int(getattr(config, "TTS_RATE_STEP", 20)))
        self.rate_slider.valueChanged.connect(self._on_rate_slider_changed)
        self._disable_wheel_events(self.rate_slider)
        tts_form.addWidget(self.rate_slider, 0, 1)
        self.rate_value_label = QtWidgets.QLabel("0")
        text_styles = get_text_styles()
        self.rate_value_label.setStyleSheet(text_styles['value'])
        tts_form.addWidget(self.rate_value_label, 0, 2)

        # Hlasitost řeči
        tts_form.addWidget(self._create_label_with_translation("speech_volume"), 1, 0)
        self.volume_slider = QtWidgets.QSlider(QtCore.Qt.Orientation.Horizontal)
        self.volume_slider.setMinimum(0)
        self.volume_slider.setMaximum(100)
        self.volume_slider.setSingleStep(5)
        self.volume_slider.setTickInterval(5)
        self.volume_slider.valueChanged.connect(self._on_volume_slider_changed)
        self._disable_wheel_events(self.volume_slider)
        tts_form.addWidget(self.volume_slider, 1, 1)
        self.volume_value_label = QtWidgets.QLabel("0%")
        self.volume_value_label.setStyleSheet(text_styles['value'])
        tts_form.addWidget(self.volume_value_label, 1, 2)

        # Povolit velká písmena
        self.allow_uppercase_check = QtWidgets.QCheckBox(self.translator.t("allow_uppercase"))
        self._set_translation_key(self.allow_uppercase_check, "allow_uppercase")
        self.allow_uppercase_check.setChecked(bool(getattr(config, "ALLOW_UPPERCASE_TEXT", True)))
        self.allow_uppercase_check.toggled.connect(self._on_toggle_allow_uppercase)
        tts_form.addWidget(self.allow_uppercase_check, 2, 0, 1, 3)  # Span across 3 columns
        # ===== 6. OCR =====
        self.ocr_group = QtWidgets.QGroupBox(self.translator.t("ocr_group"), self.main_tab)
        self._set_translation_key(self.ocr_group, "ocr_group")
        main_lay.addWidget(self.ocr_group)
        ocr_form = QtWidgets.QGridLayout(self.ocr_group)
        ocr_form.setColumnStretch(1, 1)

        # Prahování OCR
        ocr_form.addWidget(self._create_label_with_translation("ocr_threshold"), 0, 0)
        self.ocr_slider = QtWidgets.QSlider(QtCore.Qt.Orientation.Horizontal)
        self.ocr_slider.setMinimum(0)
        self.ocr_slider.setMaximum(100)
        self.ocr_slider.setSingleStep(1)
        self.ocr_slider.setTickInterval(10)
        self.ocr_slider.valueChanged.connect(self._on_ocr_slider_changed)
        self._disable_wheel_events(self.ocr_slider)
        ocr_form.addWidget(self.ocr_slider, 0, 1)
        self.ocr_value_label = QtWidgets.QLabel("0%")
        self.ocr_value_label.setStyleSheet(text_styles['value'])
        ocr_form.addWidget(self.ocr_value_label, 0, 2)

        # ===== 7. PŮVODNÍ NASTAVENÍ =====
        reset_group = QtWidgets.QGroupBox("Původní nastavení", self.main_tab)
        main_lay.addWidget(reset_group)
        reset_layout = QtWidgets.QHBoxLayout(reset_group)

        # Modré tlačítko pro reset
        self.reset_settings_btn = QtWidgets.QPushButton("Načíst původní nastavení")
        button_styles = get_button_styles()
        self.reset_settings_btn.setStyleSheet(button_styles['secondary'])
        self.reset_settings_btn.clicked.connect(self._on_reset_settings)
        reset_layout.addWidget(self.reset_settings_btn)

        # Popis tlačítka
        reset_description = QtWidgets.QLabel("Po stisknutí tlačítka se všechna nastavení obnoví na výchozí hodnoty.")
        text_styles = get_text_styles()
        reset_description.setStyleSheet(f"color: {MACOS_DARK_COLORS['text_tertiary']}; font-size: 11px; font-weight: 400;")
        reset_description.setWordWrap(True)
        reset_layout.addWidget(reset_description, 1)  # stretch factor 1 pre roztiahnutie

        # Načítanie uložených nastavení PRED nastavením GUI
        self._load_settings()

        # Po načítaní nastavení aktualizuj GUI
        logging.info(f"🔍 Debug stavu po načítaní nastavení:")
        logging.info(f"   📱 APP_LANGUAGE: {getattr(config, 'APP_LANGUAGE', 'None')}")
        logging.info(f"   📖 READING_LANGUAGE: {getattr(config, 'READING_LANGUAGE', 'None')}")
        logging.info(f"   🎤 TTS_LANGUAGE: {getattr(config, 'TTS_LANGUAGE', 'None')}")
        logging.info(f"   🗣️ SELECTED_READING_VOICE: {getattr(config, 'SELECTED_READING_VOICE', 'None')}")

        self.update_all_sliders_and_labels()
        self.update_mode_buttons(getattr(config, "reading_mode", "full_automatic"))





        # Záložky – Detekcia
        self.detection_tab = QtWidgets.QWidget()
        self.tabs.addTab(self.detection_tab, self.translator.t("detection_tab"))
        self._build_detection_tab()

        # 🔧 OPRAVA: Pripoj signály pre thread-safe operácie
        self._connect_signals()

    def _connect_signals(self):
        """Pripojí Qt signály pre thread-safe operácie."""
        self.status_changed.connect(self._apply_status)
        self.reading_state_changed.connect(self._apply_reading_state)
        self.show_warning_dialog.connect(self._show_warning_dialog_safe)
        self.show_error_dialog.connect(self._show_error_dialog_safe)

    def _disable_wheel_events(self, slider):
        """Zakáže wheel eventy pre slider aby sa neroloval náhodne."""
        def wheelEvent(event):
            # Ignoruj wheel eventy - nech sa roluje stránka namiesto slidera
            event.ignore()

        # Prepíš wheelEvent metódu slidera
        slider.wheelEvent = wheelEvent

    # ---------- Detekcia titulkov (Detection tab) ----------
    def _build_detection_tab(self):
        lay = QtWidgets.QVBoxLayout(self.detection_tab)

        # Scroll area pre veľa sliderov
        scroll = QtWidgets.QScrollArea()
        scroll.setHorizontalScrollBarPolicy(QtCore.Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll.setVerticalScrollBarPolicy(QtCore.Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll.setWidgetResizable(True)

        scroll_widget = QtWidgets.QWidget()
        scroll_layout = QtWidgets.QVBoxLayout(scroll_widget)
        scroll_layout.setSpacing(10)  # Pridaj medzery medzi sekciami
        scroll_layout.setContentsMargins(10, 10, 10, 10)  # Pridaj okraje

        scroll.setWidget(scroll_widget)
        lay.addWidget(scroll)

        # Sekcia: Základné prahy (zbaliteľná)
        sim_group = QtWidgets.QGroupBox(self.translator.t("similarity_thresholds"), scroll_widget)
        sim_group.setCheckable(True)
        sim_group.setChecked(True)  # Defaultne rozbalené
        scroll_layout.addWidget(sim_group)
        sim_form = QtWidgets.QGridLayout(sim_group)
        sim_form.setColumnStretch(1, 1)

        def add_sim_slider(row, label, attr, minv, maxv, step=0.01):
            label_widget = QtWidgets.QLabel(label)
            sim_form.addWidget(label_widget, row, 0)
            slider = QtWidgets.QSlider(QtCore.Qt.Orientation.Horizontal)
            slider.setMinimum(int(minv * 100))
            slider.setMaximum(int(maxv * 100))
            slider.setSingleStep(int(step * 100))
            self._disable_wheel_events(slider)  # Zakáž wheel eventy
            val_label = QtWidgets.QLabel("")
            def on_change(v):
                val = float(v) / 100.0
                setattr(config, attr, val)
                val_label.setText(f"{val:.2f}")
                logging.info(f"{attr} nastavené na {val:.2f}")
                self._save_settings()  # Automatické uloženie
            slider.valueChanged.connect(on_change)
            sim_form.addWidget(slider, row, 1)
            sim_form.addWidget(val_label, row, 2)
            return slider, val_label, label_widget

        self.tts_hist_slider, self.tts_hist_label, self.tts_hist_text_label = add_sim_slider(0, self.translator.t("tts_duplicate_threshold"), "TTS_HISTORY_SIMILARITY_THRESHOLD", 0.6, 0.99)
        self.stability_sim_slider, self.stability_sim_label, self.stability_sim_text_label = add_sim_slider(1, self.translator.t("stability_similarity"), "STABILITY_SIMILARITY_THRESHOLD", 0.6, 0.99)
        self.two_frame_slider, self.two_frame_label, self.two_frame_text_label = add_sim_slider(2, self.translator.t("two_frame_stability"), "TWO_FRAME_STABILITY_THRESHOLD", 0.6, 0.99)
        self.static_sim_slider, self.static_sim_label, self.static_sim_text_label = add_sim_slider(3, self.translator.t("static_similarity"), "STATIC_SIMILARITY_THRESHOLD", 0.7, 0.99)
        self.static_stab_slider, self.static_stab_label, self.static_stab_text_label = add_sim_slider(4, self.translator.t("static_stability_threshold"), "STATIC_STABILITY_THRESHOLD", 0.7, 0.99)
        self.static_replace_slider, self.static_replace_label, self.static_replace_text_label = add_sim_slider(5, self.translator.t("static_replacement_threshold"), "STATIC_REPLACEMENT_THRESHOLD", 0.2, 0.8, step=0.01)

        # Sekcia: Cyklovanie stability (zbaliteľná)
        cyc_group = QtWidgets.QGroupBox(self.translator.t("cycling_stability"), scroll_widget)
        cyc_group.setCheckable(True)
        cyc_group.setChecked(False)  # Defaultne zbalené
        scroll_layout.addWidget(cyc_group)
        cyc_form = QtWidgets.QGridLayout(cyc_group)
        cyc_form.setColumnStretch(1, 1)

        def add_int_slider(row, label, attr, minv, maxv):
            label_widget = QtWidgets.QLabel(label)
            cyc_form.addWidget(label_widget, row, 0)
            slider = QtWidgets.QSlider(QtCore.Qt.Orientation.Horizontal)
            slider.setMinimum(int(minv))
            slider.setMaximum(int(maxv))
            slider.setSingleStep(1)
            self._disable_wheel_events(slider)  # Zakáž wheel eventy
            val_label = QtWidgets.QLabel("")
            def on_change(v):
                ival = int(v)
                setattr(config, attr, ival)
                val_label.setText(str(ival))
                logging.info(f"{attr} nastavené na {ival}")
                self._save_settings()  # Automatické uloženie
            slider.valueChanged.connect(on_change)
            cyc_form.addWidget(slider, row, 1)
            cyc_form.addWidget(val_label, row, 2)
            return slider, val_label, label_widget

        self.min_cyc_first_slider, self.min_cyc_first_label, self.min_cyc_first_text_label = add_int_slider(0, self.translator.t("min_cycles_first_line"), "MIN_CYCLES_FIRST_LINE_STABILITY", 1, 5)
        self.min_cyc_second_slider, self.min_cyc_second_label, self.min_cyc_second_text_label = add_int_slider(1, self.translator.t("min_cycles_second_line"), "MIN_CYCLES_SECOND_LINE_STABILITY", 1, 5)
        self.min_cyc_second_growth_slider, self.min_cyc_second_growth_label, self.min_cyc_second_growth_text_label = add_int_slider(2, self.translator.t("min_cycles_second_growth"), "MIN_CYCLES_SECOND_LINE_GROWTH", 1, 5)
        self.single_text_cycles_slider, self.single_text_cycles_label, self.single_text_cycles_text_label = add_int_slider(3, self.translator.t("single_text_cycles"), "STABILITA_JEDNEHO_TEXTU_CYKLY", 1, 5)
        self.diff_texts_slider, self.diff_texts_label, self.diff_texts_text_label = add_int_slider(4, self.translator.t("different_texts_count"), "POCET_ROZNYCH_STABILNYCH_TEXTOV_CIEL", 2, 10)

        # Sekcia: Dynamický rast (zbaliteľná)
        dyn_group = QtWidgets.QGroupBox(self.translator.t("dynamic_growth"), scroll_widget)
        dyn_group.setCheckable(True)
        dyn_group.setChecked(False)  # Defaultne zbalené
        scroll_layout.addWidget(dyn_group)
        dyn_form = QtWidgets.QGridLayout(dyn_group)
        dyn_form.setColumnStretch(1, 1)

        # Definuj lokálne funkcie pre dynamickú sekciu
        def add_dyn_sim_slider(row, label, attr, minv, maxv, step=0.01):
            label_widget = QtWidgets.QLabel(label)
            dyn_form.addWidget(label_widget, row, 0)
            slider = QtWidgets.QSlider(QtCore.Qt.Orientation.Horizontal)
            slider.setMinimum(int(minv * 100))
            slider.setMaximum(int(maxv * 100))
            slider.setSingleStep(int(step * 100))
            self._disable_wheel_events(slider)  # Zakáž wheel eventy
            val_label = QtWidgets.QLabel("")
            def on_change(v):
                val = float(v) / 100.0
                setattr(config, attr, val)
                val_label.setText(f"{val:.2f}")
                logging.info(f"{attr} nastavené na {val:.2f}")
            slider.valueChanged.connect(on_change)
            dyn_form.addWidget(slider, row, 1)
            dyn_form.addWidget(val_label, row, 2)
            return slider, val_label, label_widget

        def add_dyn_int_slider(row, label, attr, minv, maxv):
            label_widget = QtWidgets.QLabel(label)
            dyn_form.addWidget(label_widget, row, 0)
            slider = QtWidgets.QSlider(QtCore.Qt.Orientation.Horizontal)
            slider.setMinimum(int(minv))
            slider.setMaximum(int(maxv))
            slider.setSingleStep(1)
            self._disable_wheel_events(slider)  # Zakáž wheel eventy
            val_label = QtWidgets.QLabel("")
            def on_change(v):
                ival = int(v)
                setattr(config, attr, ival)
                val_label.setText(str(ival))
                logging.info(f"{attr} nastavené na {ival}")
                self._save_settings()  # Automatické uloženie
            slider.valueChanged.connect(on_change)
            dyn_form.addWidget(slider, row, 1)
            dyn_form.addWidget(val_label, row, 2)
            return slider, val_label, label_widget

        self.dyn_growth_slider, self.dyn_growth_label, self.dyn_growth_text_label = add_dyn_sim_slider(0, self.translator.t("dynamic_growth_similarity"), "DYNAMIC_GROWTH_SIMILARITY", 0.6, 0.99)
        self.dyn_min_len_slider, self.dyn_min_len_label, self.dyn_min_len_text_label = add_dyn_int_slider(1, self.translator.t("dynamic_min_word_length"), "DYNAMIC_MIN_WORD_LENGTH", 1, 5)
        self.dyn_max_words_slider, self.dyn_max_words_label, self.dyn_max_words_text_label = add_dyn_int_slider(2, self.translator.t("dynamic_max_words"), "DYNAMIC_MAX_WORDS_PER_CYCLE", 1, 10)

        # Sekcia: Full Automatic prahy (zbaliteľná)
        fa_group = QtWidgets.QGroupBox(self.translator.t("full_auto_thresholds"), scroll_widget)
        fa_group.setCheckable(True)
        fa_group.setChecked(False)  # Defaultne zbalené
        scroll_layout.addWidget(fa_group)
        fa_form = QtWidgets.QGridLayout(fa_group)
        fa_form.setColumnStretch(1, 1)

        # Definuj lokálne funkcie pre FA sekciu
        def add_fa_sim_slider(row, label, attr, minv, maxv, step=0.01):
            label_widget = QtWidgets.QLabel(label)
            fa_form.addWidget(label_widget, row, 0)
            slider = QtWidgets.QSlider(QtCore.Qt.Orientation.Horizontal)
            slider.setMinimum(int(minv * 100))
            slider.setMaximum(int(maxv * 100))
            slider.setSingleStep(int(step * 100))
            self._disable_wheel_events(slider)  # Zakáž wheel eventy
            val_label = QtWidgets.QLabel("")
            def on_change(v):
                val = float(v) / 100.0
                setattr(config, attr, val)
                val_label.setText(f"{val:.2f}")
                logging.info(f"{attr} nastavené na {val:.2f}")
                self._save_settings()  # Automatické uloženie
            slider.valueChanged.connect(on_change)
            fa_form.addWidget(slider, row, 1)
            fa_form.addWidget(val_label, row, 2)
            return slider, val_label, label_widget

        def add_fa_int_slider(row, label, attr, minv, maxv):
            label_widget = QtWidgets.QLabel(label)
            fa_form.addWidget(label_widget, row, 0)
            slider = QtWidgets.QSlider(QtCore.Qt.Orientation.Horizontal)
            slider.setMinimum(int(minv))
            slider.setMaximum(int(maxv))
            slider.setSingleStep(1)
            self._disable_wheel_events(slider)  # Zakáž wheel eventy
            val_label = QtWidgets.QLabel("")
            def on_change(v):
                ival = int(v)
                setattr(config, attr, ival)
                val_label.setText(str(ival))
                logging.info(f"{attr} nastavené na {ival}")
                self._save_settings()  # Automatické uloženie
            slider.valueChanged.connect(on_change)
            fa_form.addWidget(slider, row, 1)
            fa_form.addWidget(val_label, row, 2)
            return slider, val_label, label_widget

        self.fa_init_static_slider, self.fa_init_static_label, self.fa_init_static_text_label = add_fa_sim_slider(0, self.translator.t("fa_initial_static"), "FULL_AUTO_INITIAL_STATIC_THRESHOLD", 0.3, 0.8)
        self.fa_init_dynamic_slider, self.fa_init_dynamic_label, self.fa_init_dynamic_text_label = add_fa_sim_slider(1, self.translator.t("fa_initial_dynamic"), "FULL_AUTO_INITIAL_DYNAMIC_THRESHOLD", 0.3, 0.8)
        self.fa_static_thr_slider, self.fa_static_thr_label, self.fa_static_thr_text_label = add_fa_sim_slider(2, self.translator.t("fa_stable_static"), "FULL_AUTO_STABLE_STATIC_THRESHOLD", 0.5, 0.95)
        self.fa_dynamic_thr_slider, self.fa_dynamic_thr_label, self.fa_dynamic_thr_text_label = add_fa_sim_slider(3, self.translator.t("fa_stable_dynamic"), "FULL_AUTO_STABLE_DYNAMIC_THRESHOLD", 0.5, 0.95)
        self.fa_min_samples_slider, self.fa_min_samples_label, self.fa_min_samples_text_label = add_fa_int_slider(4, self.translator.t("fa_min_samples"), "FULL_AUTO_STABLE_MIN_SAMPLES", 4, 20)
        self.fa_replace_slider, self.fa_replace_label, self.fa_replace_text_label = add_fa_sim_slider(5, self.translator.t("fa_replace_threshold"), "FULL_AUTO_REPLACE_THRESHOLD", 0.2, 0.6)

        # Sekcia: OCR a analýza (zbaliteľná)
        ocr_group = QtWidgets.QGroupBox(self.translator.t("ocr_analysis"), scroll_widget)
        ocr_group.setCheckable(True)
        ocr_group.setChecked(False)  # Defaultne zbalené
        scroll_layout.addWidget(ocr_group)
        ocr_form = QtWidgets.QGridLayout(ocr_group)
        ocr_form.setColumnStretch(1, 1)

        # Definuj lokálne funkcie pre OCR sekciu aby sa predišlo konfliktom s row indexmi
        def add_ocr_sim_slider(row, label, attr, minv, maxv, step=0.01):
            label_widget = QtWidgets.QLabel(label)
            ocr_form.addWidget(label_widget, row, 0)
            slider = QtWidgets.QSlider(QtCore.Qt.Orientation.Horizontal)
            slider.setMinimum(int(minv * 100))
            slider.setMaximum(int(maxv * 100))
            slider.setSingleStep(int(step * 100))
            self._disable_wheel_events(slider)  # Zakáž wheel eventy
            val_label = QtWidgets.QLabel("")
            def on_change(v):
                val = float(v) / 100.0
                setattr(config, attr, val)
                val_label.setText(f"{val:.2f}")
                logging.info(f"{attr} nastavené na {val:.2f}")
                self._save_settings()  # Automatické uloženie
            slider.valueChanged.connect(on_change)
            ocr_form.addWidget(slider, row, 1)
            ocr_form.addWidget(val_label, row, 2)
            return slider, val_label, label_widget

        def add_ocr_int_slider(row, label, attr, minv, maxv):
            label_widget = QtWidgets.QLabel(label)
            ocr_form.addWidget(label_widget, row, 0)
            slider = QtWidgets.QSlider(QtCore.Qt.Orientation.Horizontal)
            slider.setMinimum(int(minv))
            slider.setMaximum(int(maxv))
            slider.setSingleStep(1)
            self._disable_wheel_events(slider)  # Zakáž wheel eventy
            val_label = QtWidgets.QLabel("")
            def on_change(v):
                ival = int(v)
                setattr(config, attr, ival)
                val_label.setText(str(ival))
                logging.info(f"{attr} nastavené na {ival}")
                self._save_settings()  # Automatické uloženie
            slider.valueChanged.connect(on_change)
            ocr_form.addWidget(slider, row, 1)
            ocr_form.addWidget(val_label, row, 2)
            return slider, val_label, label_widget

        self.ocr_thr_slider, self.ocr_thr_label, self.ocr_thr_text_label = add_ocr_sim_slider(0, self.translator.t("ocr_threshold_param"), "OCR_THRESHOLD", 0.7, 1.0)
        self.dyn_ocr_thr_slider, self.dyn_ocr_thr_label, self.dyn_ocr_thr_text_label = add_ocr_sim_slider(1, self.translator.t("dynamic_ocr_threshold"), "DYNAMIC_OCR_THRESHOLD", 0.7, 1.0)
        self.min_text_len_slider, self.min_text_len_label, self.min_text_len_text_label = add_ocr_int_slider(2, self.translator.t("min_text_length"), "MIN_TEXT_LENGTH_FOR_ANALYSIS", 3, 10)

        # Sekcia: OCR interval (zbaliteľná)
        ocr_interval_group = QtWidgets.QGroupBox(self.translator.t("ocr_interval_group"), scroll_widget)
        ocr_interval_group.setCheckable(True)
        ocr_interval_group.setChecked(True)  # Defaultne rozbalené
        scroll_layout.addWidget(ocr_interval_group)
        ocr_interval_form = QtWidgets.QHBoxLayout(ocr_interval_group)
        ocr_interval_form.addWidget(QtWidgets.QLabel(self.translator.t("ocr_interval_label")))
        self.ocr_interval_slider = QtWidgets.QSlider(QtCore.Qt.Orientation.Horizontal)
        self.ocr_interval_slider.setMinimum(1)
        self.ocr_interval_slider.setMaximum(100)
        self.ocr_interval_slider.setSingleStep(1)
        self.ocr_interval_slider.valueChanged.connect(self._on_ocr_interval_changed)
        self._disable_wheel_events(self.ocr_interval_slider)
        ocr_interval_form.addWidget(self.ocr_interval_slider, 1)
        self.ocr_interval_value = QtWidgets.QLabel("0.00s")
        ocr_interval_form.addWidget(self.ocr_interval_value)

        # Pridaj stretch na koniec
        scroll_layout.addStretch(1)

        # Inicializuj hodnoty sliderov z configu a labely
        self._refresh_detection_values_from_config()

    def _refresh_detection_values_from_config(self):
        try:
            def set_slider_val(slider, label, val, scale=100):
                slider.blockSignals(True)
                slider.setValue(int(round(float(val) * scale)))
                slider.blockSignals(False)
                label.setText(f"{float(val):.2f}")

            def set_int_slider(slider, label, val):
                slider.blockSignals(True)
                slider.setValue(int(val))
                slider.blockSignals(False)
                label.setText(str(int(val)))

            # Prahy podobnosti
            set_slider_val(self.tts_hist_slider, self.tts_hist_label, getattr(config, 'TTS_HISTORY_SIMILARITY_THRESHOLD', 0.85))
            set_slider_val(self.stability_sim_slider, self.stability_sim_label, getattr(config, 'STABILITY_SIMILARITY_THRESHOLD', 0.90))
            set_slider_val(self.two_frame_slider, self.two_frame_label, getattr(config, 'TWO_FRAME_STABILITY_THRESHOLD', 0.90))
            set_slider_val(self.static_sim_slider, self.static_sim_label, getattr(config, 'STATIC_SIMILARITY_THRESHOLD', 0.90))
            set_slider_val(self.static_stab_slider, self.static_stab_label, getattr(config, 'STATIC_STABILITY_THRESHOLD', 0.95))
            set_slider_val(self.static_replace_slider, self.static_replace_label, getattr(config, 'STATIC_REPLACEMENT_THRESHOLD', 0.30))

            # Cyklovanie stability
            set_int_slider(self.min_cyc_first_slider, self.min_cyc_first_label, getattr(config, 'MIN_CYCLES_FIRST_LINE_STABILITY', 2))
            set_int_slider(self.min_cyc_second_slider, self.min_cyc_second_label, getattr(config, 'MIN_CYCLES_SECOND_LINE_STABILITY', 2))
            set_int_slider(self.min_cyc_second_growth_slider, self.min_cyc_second_growth_label, getattr(config, 'MIN_CYCLES_SECOND_LINE_GROWTH', 2))
            set_int_slider(self.single_text_cycles_slider, self.single_text_cycles_label, getattr(config, 'STABILITA_JEDNEHO_TEXTU_CYKLY', 2))
            set_int_slider(self.diff_texts_slider, self.diff_texts_label, getattr(config, 'POCET_ROZNYCH_STABILNYCH_TEXTOV_CIEL', 3))

            # Dynamický rast
            set_slider_val(self.dyn_growth_slider, self.dyn_growth_label, getattr(config, 'DYNAMIC_GROWTH_SIMILARITY', 0.9))
            set_int_slider(self.dyn_min_len_slider, self.dyn_min_len_label, getattr(config, 'DYNAMIC_MIN_WORD_LENGTH', 2))
            set_int_slider(self.dyn_max_words_slider, self.dyn_max_words_label, getattr(config, 'DYNAMIC_MAX_WORDS_PER_CYCLE', 3))

            # Full Automatic
            set_slider_val(self.fa_init_static_slider, self.fa_init_static_label, getattr(config, 'FULL_AUTO_INITIAL_STATIC_THRESHOLD', 0.6))
            set_slider_val(self.fa_init_dynamic_slider, self.fa_init_dynamic_label, getattr(config, 'FULL_AUTO_INITIAL_DYNAMIC_THRESHOLD', 0.6))
            set_slider_val(self.fa_static_thr_slider, self.fa_static_thr_label, getattr(config, 'FULL_AUTO_STABLE_STATIC_THRESHOLD', 0.8))
            set_slider_val(self.fa_dynamic_thr_slider, self.fa_dynamic_thr_label, getattr(config, 'FULL_AUTO_STABLE_DYNAMIC_THRESHOLD', 0.8))
            set_int_slider(self.fa_min_samples_slider, self.fa_min_samples_label, getattr(config, 'FULL_AUTO_STABLE_MIN_SAMPLES', 8))
            set_slider_val(self.fa_replace_slider, self.fa_replace_label, getattr(config, 'FULL_AUTO_REPLACE_THRESHOLD', 0.4))

            # OCR
            set_slider_val(self.ocr_thr_slider, self.ocr_thr_label, getattr(config, 'OCR_THRESHOLD', 0.99))
            set_slider_val(self.dyn_ocr_thr_slider, self.dyn_ocr_thr_label, getattr(config, 'DYNAMIC_OCR_THRESHOLD', 0.90))
            set_int_slider(self.min_text_len_slider, self.min_text_len_label, getattr(config, 'MIN_TEXT_LENGTH_FOR_ANALYSIS', 3))

            # OCR interval – mapovanie na percentá (1–100 -> 0.10–1.00s)
            cur_interval = float(getattr(config, "current_ocr_interval", 0.3))
            mapped = max(1, min(100, int(round(cur_interval * 100))))
            self.ocr_interval_slider.blockSignals(True)
            self.ocr_interval_slider.setValue(mapped)
            self.ocr_interval_slider.blockSignals(False)
            self.ocr_interval_value.setText(f"{cur_interval:.2f}s")
        except Exception as e:
            logging.error(f"[QtGUI] Chyba pri načítaní detection hodnôt: {e}")

    # ---------- Lifecycle ----------
    def closeEvent(self, event: QtGui.QCloseEvent) -> None:
        try:
            if callable(self.on_closing_callback):
                self.on_closing_callback()
        except Exception as e:
            logging.error(f"[QtGUI] Chyba pri on_closing_callback: {e}")
        # necháme app ukončiť – backend on_closing zavolá sys.exit(0)
        event.accept()
    def _on_nllb_enabled_changed(self, checked):
        """Zapne/vypne NLLB prekladač"""
        try:
            from translation import translation_config as trans_config
            from translation.translation_manager import translation_manager

            trans_config.NLLB_ENABLED = checked

            if checked:
                # Inicializácia prekladača
                if translation_manager.initialize():
                    logging.info("[QtGUI] NLLB prekladač zapnutý")
                else:
                    logging.error("[QtGUI] Chyba pri zapnutí NLLB prekladača")
                    self.nllb_enabled_check.setChecked(False)
                    return
            else:
                # Vypnutie prekladača
                translation_manager.cleanup()
                logging.info("[QtGUI] NLLB prekladač vypnutý")

            self._update_nllb_system_info()

        except Exception as e:
            logging.error(f"[QtGUI] Chyba pri prepínaní NLLB: {e}")

    def _on_nllb_auto_translate_changed(self, checked):
        """Zapne/vypne automatický preklad"""
        try:
            from translation import translation_config as trans_config
            trans_config.NLLB_AUTO_TRANSLATE = checked
            logging.info(f"[QtGUI] Automatický preklad: {'zapnutý' if checked else 'vypnutý'}")

        except Exception as e:
            logging.error(f"[QtGUI] Chyba pri prepínaní automatického prekladu: {e}")

    def _on_nllb_source_changed(self):
        """Zmení zdrojový jazyk"""
        try:
            from translation import translation_config as trans_config

            lang_code = self.nllb_source_combo.currentData()
            if lang_code:
                trans_config.set_language_pair(lang_code, trans_config.NLLB_TARGET_LANGUAGE)
                logging.info(f"[QtGUI] NLLB zdrojový jazyk zmenený na: {lang_code}")

        except Exception as e:
            logging.error(f"[QtGUI] Chyba pri zmene zdrojového jazyka: {e}")

    def _on_nllb_target_changed(self):
        """Zmení cieľový jazyk"""
        try:
            from translation import translation_config as trans_config

            lang_code = self.nllb_target_combo.currentData()
            if lang_code:
                trans_config.set_language_pair(trans_config.NLLB_SOURCE_LANGUAGE, lang_code)
                logging.info(f"[QtGUI] NLLB cieľový jazyk zmenený na: {lang_code}")

        except Exception as e:
            logging.error(f"[QtGUI] Chyba pri zmene cieľového jazyka: {e}")

    def _on_nllb_swap_languages(self):
        """Vymení zdrojový a cieľový jazyk"""
        try:
            source_idx = self.nllb_source_combo.currentIndex()
            target_idx = self.nllb_target_combo.currentIndex()

            self.nllb_source_combo.setCurrentIndex(target_idx)
            self.nllb_target_combo.setCurrentIndex(source_idx)

            logging.info("[QtGUI] NLLB jazyky vymenené")

        except Exception as e:
            logging.error(f"[QtGUI] Chyba pri výmene jazykov: {e}")

    def _on_nllb_mode_changed(self):
        """Zmení mód prekladu"""
        try:
            from translation import translation_config as trans_config

            mode = self.nllb_mode_combo.currentData()
            if mode:
                trans_config.set_translation_mode(mode)
                self._update_nllb_mode_description()
                logging.info(f"[QtGUI] NLLB mód zmenený na: {mode}")

        except Exception as e:
            logging.error(f"[QtGUI] Chyba pri zmene módu: {e}")

    def _on_nllb_quality_changed(self):
        """Zmení kvalitný profil"""
        try:
            from translation import translation_config as trans_config

            profile = self.nllb_quality_combo.currentData()
            if profile:
                trans_config.set_quality_profile(profile)
                self._update_nllb_quality_description()
                logging.info(f"[QtGUI] NLLB kvalitný profil zmenený na: {profile}")

        except Exception as e:
            logging.error(f"[QtGUI] Chyba pri zmene kvalitného profilu: {e}")

    def _on_nllb_max_length_changed(self, value):
        """Zmení maximálnu dĺžku"""
        try:
            from translation import translation_config as trans_config
            trans_config.NLLB_MAX_LENGTH = value

        except Exception as e:
            logging.error(f"[QtGUI] Chyba pri zmene max length: {e}")

    def _on_nllb_beam_size_changed(self, value):
        """Zmení beam size"""
        try:
            from translation import translation_config as trans_config
            trans_config.NLLB_NUM_BEAMS = value

        except Exception as e:
            logging.error(f"[QtGUI] Chyba pri zmene beam size: {e}")

    def _on_nllb_temperature_changed(self, value):
        """Zmení temperature"""
        try:
            from translation import translation_config as trans_config
            trans_config.NLLB_TEMPERATURE = value

        except Exception as e:
            logging.error(f"[QtGUI] Chyba pri zmene temperature: {e}")

    def _on_nllb_repetition_changed(self, value):
        """Zmení repetition penalty"""
        try:
            from translation import translation_config as trans_config
            trans_config.NLLB_REPETITION_PENALTY = value

        except Exception as e:
            logging.error(f"[QtGUI] Chyba pri zmene repetition penalty: {e}")

    def _on_nllb_custom_settings(self):
        """Zobrazí dialóg pre vlastné nastavenia"""
        try:
            from translation import translation_config as trans_config

            # Vytvorenie dialógu s aktuálnymi nastaveniami
            current_settings = trans_config.get_current_translation_settings()

            dialog = NLLBCustomDialog(current_settings, self)
            if dialog.exec() == QtWidgets.QDialog.Accepted:
                # Aplikovanie nových nastavení
                new_settings = dialog.get_settings()

                # Aktualizácia konfigurácie
                for key, value in new_settings.items():
                    if hasattr(trans_config, f'NLLB_{key.upper()}'):
                        setattr(trans_config, f'NLLB_{key.upper()}', value)

                # Aktualizácia GUI
                self._load_nllb_settings()
                logging.info("[QtGUI] NLLB vlastné nastavenia aktualizované")

        except Exception as e:
            logging.error(f"[QtGUI] Chyba pri zobrazení vlastných nastavení: {e}")

    def _on_nllb_initialize(self):
        """Inicializuje NLLB model"""
        try:
            from translation.translation_manager import translation_manager

            # Zobrazenie progress dialógu
            progress = QtWidgets.QProgressDialog(self.translator.t("initializing_progress"), self.translator.t("cancel_button"), 0, 0, self)
            progress.setWindowModality(QtCore.Qt.WindowModality.WindowModal)
            progress.show()

            # Inicializácia v separátnom thread (simulácia)
            QtCore.QTimer.singleShot(100, lambda: self._do_nllb_initialize(progress))

        except Exception as e:
            logging.error(f"[QtGUI] Chyba pri inicializácii NLLB: {e}")

    def _do_nllb_initialize(self, progress):
        """Vykoná inicializáciu NLLB modelu"""
        try:
            from translation.translation_manager import translation_manager

            success = translation_manager.initialize()
            progress.close()

            if success:
                QtWidgets.QMessageBox.information(
                    self, self.translator.t("success_dialog"), self.translator.t("nllb_init_success_text")
                )
                self._update_nllb_system_info()
            else:
                QtWidgets.QMessageBox.warning(
                    self, self.translator.t("error_dialog"), self.translator.t("nllb_init_failed_text")
                )

        except Exception as e:
            progress.close()
            logging.error(f"[QtGUI] Chyba pri inicializácii: {e}")
            QtWidgets.QMessageBox.critical(
                self, self.translator.t("critical_error_dialog"), self.translator.t("nllb_init_error_text").format(error=e)
            )

    def _on_nllb_test(self):
        """Spustí test prekladu"""
        try:
            from translation.translation_manager import translation_manager
            from translation import translation_config as trans_config

            if not trans_config.NLLB_ENABLED:
                QtWidgets.QMessageBox.warning(
                    self, self.translator.t("warning_dialog"), self.translator.t("nllb_not_enabled_text")
                )
                return

            # Test text
            test_text = "Hello, this is a test translation."
            source_lang = trans_config.NLLB_SOURCE_LANGUAGE
            target_lang = trans_config.NLLB_TARGET_LANGUAGE

            # Preklad
            result = translation_manager.translate_text_sync(test_text, source_lang, target_lang)

            if result:
                QtWidgets.QMessageBox.information(
                    self, self.translator.t("success_dialog"),
                    self.translator.t("nllb_test_success_text").format(source=source_lang, text=test_text, target=target_lang, result=result)
                )
                self._update_nllb_stats()
            else:
                QtWidgets.QMessageBox.warning(
                    self, self.translator.t("error_dialog"), self.translator.t("nllb_test_failed_text")
                )

        except Exception as e:
            logging.error(f"[QtGUI] Chyba pri teste prekladu: {e}")
            QtWidgets.QMessageBox.critical(
                self, "Chyba", f"Chyba pri teste prekladu:\n{e}"
            )

    def _on_nllb_cleanup(self):
        """Vyčistí NLLB model z pamäte"""
        try:
            from translation.translation_manager import translation_manager

            reply = QtWidgets.QMessageBox.question(
                self, self.translator.t("confirmation_dialog"),
                self.translator.t("nllb_cleanup_confirm_text"),
                QtWidgets.QMessageBox.Yes | QtWidgets.QMessageBox.No
            )

            if reply == QtWidgets.QMessageBox.Yes:
                translation_manager.cleanup()
                self._update_nllb_system_info()
                QtWidgets.QMessageBox.information(
                    self, self.translator.t("success_dialog"), self.translator.t("nllb_cleanup_success_text")
                )
                logging.info("[QtGUI] NLLB model vyčistený z pamäte")

        except Exception as e:
            logging.error(f"[QtGUI] Chyba pri čistení pamäte: {e}")

    def _on_nllb_reset_stats(self):
        """Resetuje štatistiky prekladov"""
        try:
            from translation.translation_manager import translation_manager

            reply = QtWidgets.QMessageBox.question(
                self, self.translator.t("confirmation_dialog"),
                self.translator.t("nllb_stats_reset_confirm_text"),
                QtWidgets.QMessageBox.Yes | QtWidgets.QMessageBox.No
            )

            if reply == QtWidgets.QMessageBox.Yes:
                translation_manager.reset_stats()
                self._update_nllb_stats()
                QtWidgets.QMessageBox.information(
                    self, self.translator.t("success_dialog"), self.translator.t("nllb_stats_reset_success_text")
                )
                logging.info("[QtGUI] NLLB štatistiky resetované")

        except Exception as e:
            logging.error(f"[QtGUI] Chyba pri resetovaní štatistík: {e}")

    def _on_nllb_translate_test(self):
        """Preloží test text"""
        try:
            from translation.translation_manager import translation_manager
            from translation import translation_config as trans_config

            if not trans_config.NLLB_ENABLED:
                self.nllb_test_output.setPlainText(self.translator.t("nllb_translate_error"))
                return

            input_text = self.nllb_test_input.toPlainText().strip()
            if not input_text:
                self.nllb_test_output.setPlainText(self.translator.t("nllb_no_input"))
                return

            source_lang = trans_config.NLLB_SOURCE_LANGUAGE
            target_lang = trans_config.NLLB_TARGET_LANGUAGE

            # Zobrazenie loading správy
            self.nllb_test_output.setPlainText(self.translator.t("nllb_translating"))
            QtCore.QCoreApplication.processEvents()

            # Preklad
            result = translation_manager.translate_text_sync(input_text, source_lang, target_lang)

            if result:
                self.nllb_test_output.setPlainText(result)
                self._update_nllb_stats()
            else:
                self.nllb_test_output.setPlainText(self.translator.t("nllb_test_failed_text"))

        except Exception as e:
            logging.error(f"[QtGUI] Chyba pri teste prekladu: {e}")
            self.nllb_test_output.setPlainText(self.translator.t("nllb_test_error_text").format(error=e))

    def _load_nllb_settings(self):
        """Načíta aktuálne NLLB nastavenia"""
        try:
            from translation import translation_config as trans_config
            from translation.nllb_translator import nllb_translator

            # Základné nastavenia
            self.nllb_enabled_check.setChecked(trans_config.NLLB_ENABLED)
            self.nllb_auto_translate_check.setChecked(trans_config.NLLB_AUTO_TRANSLATE)

            # Naplnenie jazykových combo boxov
            supported_langs = nllb_translator.get_supported_languages()

            self.nllb_source_combo.clear()
            self.nllb_target_combo.clear()

            for lang_code in sorted(supported_langs):
                lang_name = trans_config.get_language_name(lang_code)
                self.nllb_source_combo.addItem(f"{lang_name} ({lang_code})", lang_code)
                self.nllb_target_combo.addItem(f"{lang_name} ({lang_code})", lang_code)

            # Nastavenie aktuálnych jazykov
            source_idx = self.nllb_source_combo.findData(trans_config.NLLB_SOURCE_LANGUAGE)
            if source_idx >= 0:
                self.nllb_source_combo.setCurrentIndex(source_idx)

            target_idx = self.nllb_target_combo.findData(trans_config.NLLB_TARGET_LANGUAGE)
            if target_idx >= 0:
                self.nllb_target_combo.setCurrentIndex(target_idx)

            # Mód prekladu
            mode_idx = self.nllb_mode_combo.findData(trans_config.NLLB_TRANSLATION_MODE)
            if mode_idx >= 0:
                self.nllb_mode_combo.setCurrentIndex(mode_idx)

            # Kvalitný profil
            quality_idx = self.nllb_quality_combo.findData(trans_config.NLLB_QUALITY_PROFILE)
            if quality_idx >= 0:
                self.nllb_quality_combo.setCurrentIndex(quality_idx)

            # Pokročilé nastavenia
            self.nllb_max_length_spin.setValue(trans_config.NLLB_MAX_LENGTH)
            self.nllb_beam_size_spin.setValue(trans_config.NLLB_NUM_BEAMS)
            self.nllb_temperature_spin.setValue(trans_config.NLLB_TEMPERATURE)
            self.nllb_repetition_spin.setValue(trans_config.NLLB_REPETITION_PENALTY)

            # Aktualizácia popisov
            self._update_nllb_mode_description()
            self._update_nllb_quality_description()
            self._update_nllb_system_info()
            self._update_nllb_stats()

        except Exception as e:
            logging.error(f"[QtGUI] Chyba pri načítaní NLLB nastavení: {e}")

    def _update_nllb_mode_description(self):
        """Aktualizuje popis módu prekladu"""
        try:
            from translation import translation_config as trans_config

            current_mode = self.nllb_mode_combo.currentData()
            if current_mode in trans_config.TRANSLATION_MODES:
                mode_info = trans_config.TRANSLATION_MODES[current_mode]
                self.nllb_mode_desc.setText(mode_info['description'])

        except Exception as e:
            logging.error(f"[QtGUI] Chyba pri aktualizácii popisu módu: {e}")

    def _update_nllb_quality_description(self):
        """Aktualizuje popis kvalitného profilu"""
        try:
            from translation import translation_config as trans_config

            current_profile = self.nllb_quality_combo.currentData()
            if current_profile in trans_config.QUALITY_PROFILES:
                profile_info = trans_config.QUALITY_PROFILES[current_profile]
                self.nllb_quality_desc.setText(profile_info['description'])

                # Zobraz/skry custom button
                self.nllb_custom_btn.setVisible(current_profile == "custom")

        except Exception as e:
            logging.error(f"[QtGUI] Chyba pri aktualizácii popisu profilu: {e}")

    def _update_nllb_system_info(self):
        """Aktualizuje systémové informácie"""
        try:
            from translation.nllb_translator import nllb_translator
            import torch

            # Device info
            text_styles = get_text_styles()
            if torch.backends.mps.is_available():
                device_text = "🚀 MPS GPU (Apple Silicon)"
                device_color = text_styles['success']
            elif torch.cuda.is_available():
                device_text = "🚀 CUDA GPU"
                device_color = text_styles['success']
            else:
                device_text = "💻 CPU"
                device_color = text_styles['warning']

            self.nllb_device_label.setText(device_text)
            self.nllb_device_label.setStyleSheet(device_color)

            # Model status
            if nllb_translator.is_initialized:
                status_text = self.translator.t("status_initialized")
                status_color = text_styles['success']
            else:
                status_text = self.translator.t("status_not_initialized")
                status_color = text_styles['error']

            self.nllb_model_status_label.setText(status_text)
            self.nllb_model_status_label.setStyleSheet(status_color)

            # Memory info (aproximácia)
            if nllb_translator.is_initialized:
                memory_text = "~2-3 GB (600M model)"
                memory_color = f"color: {MACOS_DARK_COLORS['text_tertiary']}; font-weight: 400;"
            else:
                memory_text = "0 GB"
                memory_color = text_styles['caption']

            self.nllb_memory_label.setText(memory_text)
            self.nllb_memory_label.setStyleSheet(memory_color)

        except Exception as e:
            logging.error(f"[QtGUI] Chyba pri aktualizácii systémových informácií: {e}")

    def _update_nllb_stats(self):
        """Aktualizuje štatistiky prekladov"""
        try:
            from translation.translation_manager import translation_manager

            stats = translation_manager.get_translation_stats()

            self.nllb_total_translations_label.setText(str(stats.get('total_translations', 0)))
            self.nllb_successful_label.setText(str(stats.get('successful_translations', 0)))
            self.nllb_avg_time_label.setText(f"{stats.get('average_time', 0.0):.2f}s")
            self.nllb_success_rate_label.setText(f"{stats.get('success_rate', 0.0):.1f}%")

        except Exception as e:
            logging.error(f"[QtGUI] Chyba pri aktualizácii štatistík: {e}")

    # ---------- NLLB-200 Translation tab ----------
    def _build_nllb_tab(self):
        """Vytvorí záložku pre NLLB-200 prekladač"""
        lay = QtWidgets.QVBoxLayout(self.nllb_tab)
        lay.setSpacing(15)

        # Hlavný nadpis
        title = QtWidgets.QLabel("🌐 NLLB-200 Neural Machine Translation")
        title_font = title.font()
        title_font.setBold(True)
        title_font.setPointSize(14)
        title.setFont(title_font)
        title.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        text_styles = get_text_styles()
        title.setStyleSheet(f"color: {MACOS_DARK_COLORS['text_tertiary']}; margin: 10px 0px; font-weight: 700;")
        lay.addWidget(title)

        # Popis
        desc = QtWidgets.QLabel(self.translator.t("nllb_description"))
        desc.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        desc.setStyleSheet(f"{text_styles['caption']}; margin-bottom: 15px;")
        lay.addWidget(desc)

        # Scroll area pre obsah
        scroll = QtWidgets.QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setHorizontalScrollBarPolicy(QtCore.Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll.setVerticalScrollBarPolicy(QtCore.Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        lay.addWidget(scroll)

        scroll_widget = QtWidgets.QWidget()
        scroll_layout = QtWidgets.QVBoxLayout(scroll_widget)
        scroll_layout.setSpacing(15)

        # ===== 1. ZÁKLADNÉ NASTAVENIA =====
        basic_group = QtWidgets.QGroupBox(self.translator.t("nllb_basic_settings"))
        basic_layout = QtWidgets.QGridLayout(basic_group)
        scroll_layout.addWidget(basic_group)

        # Zapnutie/vypnutie NLLB
        basic_layout.addWidget(QtWidgets.QLabel(self.translator.t("nllb_status_label")), 0, 0)
        self.nllb_enabled_check = QtWidgets.QCheckBox(self.translator.t("nllb_enabled_checkbox"))
        self.nllb_enabled_check.toggled.connect(self._on_nllb_enabled_changed)
        basic_layout.addWidget(self.nllb_enabled_check, 0, 1)

        # Automatický preklad
        basic_layout.addWidget(QtWidgets.QLabel(self.translator.t("auto_translate_label")), 1, 0)
        self.nllb_auto_translate_check = QtWidgets.QCheckBox(self.translator.t("auto_translate_checkbox"))
        self.nllb_auto_translate_check.toggled.connect(self._on_nllb_auto_translate_changed)
        basic_layout.addWidget(self.nllb_auto_translate_check, 1, 1)

        # ===== 2. JAZYKOVÉ NASTAVENIA =====
        lang_group = QtWidgets.QGroupBox(self.translator.t("nllb_language_settings"))
        lang_layout = QtWidgets.QGridLayout(lang_group)
        scroll_layout.addWidget(lang_group)

        # Zdrojový jazyk
        lang_layout.addWidget(QtWidgets.QLabel(self.translator.t("source_language_label")), 0, 0)
        self.nllb_source_combo = QtWidgets.QComboBox()
        # Zakáž scrollovanie v combobox-e
        self.nllb_source_combo.installEventFilter(self.combo_scroll_filter)
        self.nllb_source_combo.currentIndexChanged.connect(self._on_nllb_source_changed)
        lang_layout.addWidget(self.nllb_source_combo, 0, 1)

        # Cieľový jazyk
        lang_layout.addWidget(QtWidgets.QLabel(self.translator.t("target_language_label")), 1, 0)
        self.nllb_target_combo = QtWidgets.QComboBox()
        # Zakáž scrollovanie v combobox-e
        self.nllb_target_combo.installEventFilter(self.combo_scroll_filter)
        self.nllb_target_combo.currentIndexChanged.connect(self._on_nllb_target_changed)
        lang_layout.addWidget(self.nllb_target_combo, 1, 1)

        # Swap button
        self.nllb_swap_btn = QtWidgets.QPushButton(self.translator.t("swap_languages_button"))
        self.nllb_swap_btn.clicked.connect(self._on_nllb_swap_languages)
        lang_layout.addWidget(self.nllb_swap_btn, 0, 2, 2, 1)

        # ===== 3. MÓDY PREKLADU =====
        mode_group = QtWidgets.QGroupBox("🎯 Módy prekladu")
        mode_layout = QtWidgets.QGridLayout(mode_group)
        scroll_layout.addWidget(mode_group)

        mode_layout.addWidget(QtWidgets.QLabel(self.translator.t("translation_mode_label")), 0, 0)
        self.nllb_mode_combo = QtWidgets.QComboBox()
        # Zakáž scrollovanie v combobox-e
        self.nllb_mode_combo.installEventFilter(self.combo_scroll_filter)
        self.nllb_mode_combo.addItem(self.translator.t("mode_off"), "off")
        self.nllb_mode_combo.addItem(self.translator.t("mode_manual"), "manual")
        self.nllb_mode_combo.addItem(self.translator.t("mode_auto"), "auto")
        self.nllb_mode_combo.addItem(self.translator.t("mode_smart"), "smart")
        self.nllb_mode_combo.currentIndexChanged.connect(self._on_nllb_mode_changed)
        mode_layout.addWidget(self.nllb_mode_combo, 0, 1)

        # Popis módu
        self.nllb_mode_desc = QtWidgets.QLabel()
        text_styles = get_text_styles()
        self.nllb_mode_desc.setStyleSheet(f"{text_styles['caption']}; font-size: 10px;")
        self.nllb_mode_desc.setWordWrap(True)
        mode_layout.addWidget(self.nllb_mode_desc, 1, 0, 1, 2)

        # ===== 4. KVALITNÉ PROFILY =====
        quality_group = QtWidgets.QGroupBox(self.translator.t("nllb_quality_profiles"))
        quality_layout = QtWidgets.QGridLayout(quality_group)
        scroll_layout.addWidget(quality_group)

        quality_layout.addWidget(QtWidgets.QLabel(self.translator.t("quality_profile_label")), 0, 0)
        self.nllb_quality_combo = QtWidgets.QComboBox()
        # Zakáž scrollovanie v combobox-e
        self.nllb_quality_combo.installEventFilter(self.combo_scroll_filter)
        self.nllb_quality_combo.addItem(self.translator.t("profile_fast"), "fast")
        self.nllb_quality_combo.addItem(self.translator.t("profile_balanced"), "balanced")
        self.nllb_quality_combo.addItem(self.translator.t("profile_quality"), "quality")
        self.nllb_quality_combo.addItem(self.translator.t("profile_custom"), "custom")
        self.nllb_quality_combo.currentIndexChanged.connect(self._on_nllb_quality_changed)
        quality_layout.addWidget(self.nllb_quality_combo, 0, 1)

        # Popis profilu
        self.nllb_quality_desc = QtWidgets.QLabel()
        text_styles = get_text_styles()
        self.nllb_quality_desc.setStyleSheet(f"{text_styles['caption']}; font-size: 10px;")
        self.nllb_quality_desc.setWordWrap(True)
        quality_layout.addWidget(self.nllb_quality_desc, 1, 0, 1, 2)

        # Tlačidlo pre vlastné nastavenia
        self.nllb_custom_btn = QtWidgets.QPushButton(self.translator.t("nllb_custom_settings"))
        self.nllb_custom_btn.clicked.connect(self._on_nllb_custom_settings)
        self.nllb_custom_btn.setVisible(False)
        quality_layout.addWidget(self.nllb_custom_btn, 2, 0, 1, 2)

        # Nastavenie scroll area
        scroll.setWidget(scroll_widget)

        # Inicializácia hodnôt
        self._load_nllb_settings()



        # Kompatibilita s backendom: Tk-like mode_var get/set
        class _ModeVar:
            def __init__(self, gui):
                self.gui = gui
            def get(self):
                for key, btn in self.gui.mode_buttons.items():
                    if btn.isChecked():
                        return key
                return getattr(config, 'reading_mode', 'full_automatic')
            def set(self, value):
                if value in self.gui.mode_buttons:
                    self.gui.mode_buttons[value].setChecked(True)
        self.mode_var = _ModeVar(self)



        # 🚀 NOVÉ: Inicializácia multi-translator systému
        self._init_multi_translator()

        # Zviditeľnenie okna
        self.show()
        self.raise_()
        self.activateWindow()

    def _init_multi_translator(self):
        """🚀 NOVÉ: Inicializácia multi-translator systému."""
        try:
            # Pokus o import a inicializáciu multi-translator
            from multi_translator import init_multi_translator, get_translator_info

            # Získanie aktuálneho režimu z GUI
            selected_mode = self.translator_engine_combo.currentData() or "balanced"

            # 🔧 OPRAVA: Získanie aktuálnych jazykov z GUI namiesto z config
            current_src_lang = self.source_lang_combo.currentData() or getattr(config, "TRANSLATION_SOURCE_LANGUAGE", "en")
            current_tgt_lang = self.target_lang_combo.currentData() or getattr(config, "TRANSLATION_TARGET_LANGUAGE", "cs")

            logging.info(f"🔧 Multi-translator inicializácia s jazykmi: {current_src_lang} → {current_tgt_lang}")

            # Inicializácia
            success = init_multi_translator(
                mode=selected_mode,
                src_lang=current_src_lang,
                tgt_lang=current_tgt_lang
            )

            if success:
                logging.info(f"✅ Multi-translator inicializovaný v režime: {selected_mode}")

                # Aktualizácia štatistík v GUI
                try:
                    info = get_translator_info()
                    if info.get("status") == "initialized":
                        device_info = info.get("device_info", {})
                        device = device_info.get("current_device", "unknown")
                        engine = device_info.get("engine", "unknown")

                        stats_text = f"Engine: {engine} | Zariadenie: {device}"
                        # 🔧 OPRAVA: Použij i18n systém pre štatistiky
                        try:
                            stats_prefix = self.translator.t("translator_stats")
                            self.translation_stats_label.setText(f"{stats_prefix}: {stats_text}")
                        except Exception as e:
                            logging.warning(f"⚠️ Chyba pri získavaní i18n štatistík textu: {e}")
                            self.translation_stats_label.setText(f"Štatistiky: {stats_text}")  # Fallback

                except Exception as e:
                    logging.warning(f"⚠️  Chyba pri aktualizácii štatistík: {e}")

            else:
                logging.warning("⚠️  Multi-translator inicializácia zlyhala")
                # 🔧 OPRAVA: Použij i18n systém pre nedostupný multi-translator
                try:
                    unavailable_text = self.translator.t("translator_stats_unavailable")
                    self.translation_stats_label.setText(unavailable_text)
                except Exception as e:
                    logging.warning(f"⚠️ Chyba pri získavaní i18n nedostupný text: {e}")
                    self.translation_stats_label.setText("Štatistiky: Multi-translator nedostupný")  # Fallback

        except ImportError:
            logging.info("📝 Multi-translator nie je dostupný, používam legacy systém")
            # 🔧 OPRAVA: Použij i18n systém pre legacy systém
            try:
                legacy_text = self.translator.t("translator_stats_legacy")
                self.translation_stats_label.setText(legacy_text)
            except Exception as e:
                logging.warning(f"⚠️ Chyba pri získavaní i18n legacy text: {e}")
                self.translation_stats_label.setText("Štatistiky: Legacy systém")  # Fallback

        except Exception as e:
            logging.error(f"❌ Chyba pri inicializácii multi-translator: {e}")
            # 🔧 OPRAVA: Použij i18n systém pre chybu inicializácie
            try:
                error_text = self.translator.t("translator_stats_error")
                self.translation_stats_label.setText(error_text)
            except Exception as e2:
                logging.warning(f"⚠️ Chyba pri získavaní i18n error text: {e2}")
                self.translation_stats_label.setText("Štatistiky: Chyba inicializácie")  # Fallback

    # ---------- Pomocné aktualizácie ovládacích prvkov ----------
    def update_mode_buttons(self, current_mode: str):
        for key, btn in self.mode_buttons.items():
            btn.setChecked(key == current_mode)

    def _on_mode_changed(self, key: str, checked: bool):
        if checked:
            try:
                self.set_reading_mode_callback(key)
                logging.info(f"[QtGUI] Režim nastavený na: {key}")
                self._save_settings()  # Automatické uloženie
            except Exception as e:
                logging.error(f"[QtGUI] Chyba pri nastavovaní režimu: {e}")

    def _on_rate_slider_changed(self, value: int):
        try:
            config.TTS_RATE = int(value)
            config.USER_TTS_RATE = int(value)
            config.MANUAL_TTS_RATE_ADJUSTMENT = True  # Označíme, že používateľ manuálne nastavil rýchlosť
            self.rate_value_label.setText(str(config.TTS_RATE))
            logging.info(f"🎛️ Rýchlosť reči nastavená používateľom na {config.TTS_RATE}")
            self._save_settings()  # Automatické uloženie
        except Exception as e:
            logging.error(f"[QtGUI] Chyba pri zmene rýchlosti: {e}")








    def _on_volume_slider_changed(self, value: int):
        try:
            config.TTS_VOLUME = max(0.0, min(1.0, float(value) / 100.0))
            self.volume_value_label.setText(f"{int(value)}%")
            logging.info(f"Hlasitosť reči nastavená na {config.TTS_VOLUME:.2f}")
            self._save_settings()  # Automatické uloženie
        except Exception as e:
            logging.error(f"[QtGUI] Chyba pri zmene hlasitosti: {e}")

    def _on_ocr_slider_changed(self, value: int):
        try:
            config.OCR_THRESHOLD = max(0.0, min(1.0, float(value) / 100.0))
            self.ocr_value_label.setText(f"{int(value)}%")
            logging.info(f"Prahovanie OCR nastavené na {config.OCR_THRESHOLD:.2f}")
            self._save_settings()  # Automatické uloženie
        except Exception as e:
            logging.error(f"[QtGUI] Chyba pri zmene OCR threshold: {e}")

    def _on_ocr_interval_changed(self, value: int):
        # mapovanie 1..100 -> 0.10..1.00 s
        seconds = max(0.10, min(1.00, float(value) / 100.0))
        config.READING_OCR_INTERVAL = seconds
        config.DETECTION_OCR_INTERVAL = seconds
        config.current_ocr_interval = seconds
        self.ocr_interval_value.setText(f"{seconds:.2f}s")
        logging.info(f"OCR interval nastavený na {seconds:.2f}s")



    def update_all_sliders_and_labels(self):
        # Zaisti, že sa vykoná v GUI threade
        QtCore.QTimer.singleShot(0, self._apply_update_all_sliders_and_labels)

    @QtCore.pyqtSlot()
    def _apply_update_all_sliders_and_labels(self):
        try:
            # Rýchlosť
            self.rate_slider.blockSignals(True)
            self.rate_slider.setMinimum(int(getattr(config, "MIN_TTS_RATE", 100)))
            self.rate_slider.setMaximum(int(getattr(config, "MAX_TTS_RATE", 400)))
            self.rate_slider.setValue(int(getattr(config, "TTS_RATE", 200)))
            self.rate_slider.blockSignals(False)
            self.rate_value_label.setText(str(getattr(config, "TTS_RATE", 200)))

            # Hlasitosť
            vol_percent = int(round(float(getattr(config, "TTS_VOLUME", 1.0)) * 100))
            self.volume_slider.blockSignals(True)
            self.volume_slider.setValue(vol_percent)
            self.volume_slider.blockSignals(False)
            self.volume_value_label.setText(f"{vol_percent}%")

            # Checkboxy

            self.allow_uppercase_check.blockSignals(True)
            self.allow_uppercase_check.setChecked(bool(getattr(config, "ALLOW_UPPERCASE_TEXT", True)))
            self.allow_uppercase_check.blockSignals(False)

            # Prekladač
            translation_enabled = bool(getattr(config, "TRANSLATION_ENABLED", False))
            if hasattr(self, 'translation_group'):
                self.translation_group.blockSignals(True)
                self.translation_group.setChecked(translation_enabled)
                self.translation_group.blockSignals(False)

            if hasattr(self, 'source_lang_combo'):
                current_source = getattr(config, "TRANSLATION_SOURCE_LANGUAGE", "en")
                source_index = self.source_lang_combo.findData(current_source)
                if source_index >= 0:
                    self.source_lang_combo.blockSignals(True)
                    self.source_lang_combo.setCurrentIndex(source_index)
                    self.source_lang_combo.blockSignals(False)

            if hasattr(self, 'target_lang_combo'):
                current_target = getattr(config, "TRANSLATION_TARGET_LANGUAGE", "cs")
                target_index = self.target_lang_combo.findData(current_target)
                if target_index >= 0:
                    self.target_lang_combo.blockSignals(True)
                    self.target_lang_combo.setCurrentIndex(target_index)
                    self.target_lang_combo.blockSignals(False)

            # Translator engine
            if hasattr(self, 'translator_engine_combo'):
                current_engine = getattr(config, "TRANSLATOR_ENGINE", "balanced")
                engine_index = self.translator_engine_combo.findData(current_engine)
                if engine_index >= 0:
                    self.translator_engine_combo.blockSignals(True)
                    self.translator_engine_combo.setCurrentIndex(engine_index)
                    self.translator_engine_combo.blockSignals(False)

            # Aktualizuj stav režimov podľa prekladača
            if translation_enabled:
                self._disable_other_modes(True)

            # OCR threshold
            ocr_percent = int(round(float(getattr(config, "OCR_THRESHOLD", 0.99)) * 100))
            self.ocr_slider.blockSignals(True)
            self.ocr_slider.setValue(ocr_percent)
            self.ocr_slider.blockSignals(False)
            self.ocr_value_label.setText(f"{ocr_percent}%")

            # Aktualizuj hlasy
            self._update_reading_voices()
            self._update_translation_voices()

            # Aktualizuj slidery z druhej záložky (Detekce titulků)
            self._update_detection_sliders()

        except Exception as e:
            logging.error(f"[QtGUI] Chyba pri update sliderov: {e}")



    # ---------- Stavové a kompatibilné metódy, ktoré backend očakáva ----------
    def update_status(self, message: str):
        """Thread‑safe aktualizácia statusu."""
        try:
            self.status_changed.emit(str(message))
        except Exception as e:
            logging.debug(f"[QtGUI] update_status emit failed: {e}")

    def update_start_button_text(self):
        self.reading_state_changed.emit(bool(getattr(config, "is_reading", False)))

    def show_warning_dialog_threadsafe(self, title: str, text: str):
        """Thread-safe zobrazenie warning dialógu z iných vlákien."""
        try:
            self.show_warning_dialog.emit(title, text)
        except Exception as e:
            logging.error(f"[QtGUI] Chyba pri emitovaní warning dialog signálu: {e}")

    def show_error_dialog_threadsafe(self, title: str, text: str):
        """Thread-safe zobrazenie error dialógu z iných vlákien."""
        try:
            self.show_error_dialog.emit(title, text)
        except Exception as e:
            logging.error(f"[QtGUI] Chyba pri emitovaní error dialog signálu: {e}")

    def destroy(self):
        self.close()

    # ---------- Interné aplikovanie zmien v UI (bežia v GUI threade) ----------
    @QtCore.pyqtSlot(str)
    def _apply_status(self, message: str):
        self.status_label.setText(message)
        self.statusBar().showMessage(message, 3000)
        logging.info(f"Status: {message}")

    @QtCore.pyqtSlot(bool)
    def _apply_reading_state(self, is_reading: bool):
        """Aktualizuje stav tlačidla čítania."""
        try:
            if is_reading:
                self.start_stop_btn.setText(self.translator.t("stop_reading"))
                self.action_toggle_reading.setText(self.translator.t("stop_reading"))
            else:
                self.start_stop_btn.setText(self.translator.t("start_reading"))
                self.action_toggle_reading.setText(self.translator.t("start_reading"))
        except Exception as e:
            logging.error(f"[QtGUI] Chyba pri aktualizácii reading state: {e}")

    @QtCore.pyqtSlot(str, str)
    def _show_warning_dialog_safe(self, title: str, text: str):
        """Thread-safe zobrazenie warning dialógu v main thread."""
        try:
            QtWidgets.QMessageBox.warning(self, title, text)
            logging.info("✅ Warning dialog zobrazený thread-safe")
        except Exception as e:
            logging.error(f"❌ Chyba pri zobrazovaní warning dialógu: {e}")

    @QtCore.pyqtSlot(str, str)
    def _show_error_dialog_safe(self, title: str, text: str):
        """Thread-safe zobrazenie error dialógu v main thread."""
        try:
            QtWidgets.QMessageBox.critical(self, title, text)
            logging.info("✅ Error dialog zobrazený thread-safe")
        except Exception as e:
            logging.error(f"❌ Chyba pri zobrazovaní error dialógu: {e}")

    # ---------- QShortcut hotkeys (Qt) ----------
    def _install_qshortcuts(self):
        if getattr(self, '_qt_shortcuts_installed', False):
            return
        self._qt_shortcuts_installed = True
        # Cmd+Alt+Ctrl+U – Toggle reading (PÔVODNÁ FUNGUJÚCA KOMBINÁCIA)
        # Debounced wrapper, aby Qt neodpálilo toggle viac krát na jedno stlačenie
        def _debounced_toggle():
            try:
                from platform_loader import get_hotkey_manager
                hotkey_manager = get_hotkey_manager()
                TOGGLE_DEBOUNCE_SECONDS = getattr(hotkey_manager, 'TOGGLE_DEBOUNCE_SECONDS', 0.35) if hotkey_manager else 0.35
                import time as _time
                if not hasattr(self, '_last_qt_toggle_ts'):
                    self._last_qt_toggle_ts = 0.0
                now = _time.time()
                if now - self._last_qt_toggle_ts < TOGGLE_DEBOUNCE_SECONDS:
                    logging.info(f"[DEBOUNCE][Qt] Ignorujem toggle_reading (interval={(now - self._last_qt_toggle_ts):.3f}s < {TOGGLE_DEBOUNCE_SECONDS}s)")
                    return
                self._last_qt_toggle_ts = now
                # Namiesto priameho volania callbacku posielame príkaz do hotkey queue, aby bol jednotný tok
                try:
                    from platform_loader import get_hotkey_manager
                    hotkey_manager = get_hotkey_manager()
                    if hotkey_manager and hasattr(hotkey_manager, 'enqueue_hotkey_command'):
                        enqueue_hotkey_command = hotkey_manager.enqueue_hotkey_command
                    enqueue_hotkey_command('toggle_reading')
                except Exception:
                    self._on_toggle_reading()
            except Exception as e:
                logging.error(f"[QtGUI] Chyba v _debounced_toggle: {e}")
        QtGui.QShortcut(QtGui.QKeySequence("Meta+Alt+Ctrl+V"), self, activated=_debounced_toggle)
        # Cmd+Alt+Ctrl+Left/Right – Volume
        QtGui.QShortcut(QtGui.QKeySequence("Meta+Alt+Ctrl+Left"), self, activated=lambda: self._adjust_volume(-getattr(config, 'TTS_VOLUME_STEP', 0.05)))
        QtGui.QShortcut(QtGui.QKeySequence("Meta+Alt+Ctrl+Right"), self, activated=lambda: self._adjust_volume(getattr(config, 'TTS_VOLUME_STEP', 0.05)))
        # Cmd+Alt+Ctrl+Down/Up – Rate
        QtGui.QShortcut(QtGui.QKeySequence("Meta+Alt+Ctrl+Down"), self, activated=lambda: self._adjust_rate(-getattr(config, 'TTS_RATE_STEP', 20)))
        QtGui.QShortcut(QtGui.QKeySequence("Meta+Alt+Ctrl+Up"), self, activated=lambda: self._adjust_rate(getattr(config, 'TTS_RATE_STEP', 20)))
        # Cmd+Alt+Ctrl+I – Report anomaly
        QtGui.QShortcut(QtGui.QKeySequence("Meta+Alt+Ctrl+I"), self, activated=self._on_anomaly_report)

    def _adjust_volume(self, delta: float):
        try:
            from platform_loader import get_tts_manager
            tts_manager = get_tts_manager()
            if tts_manager and hasattr(tts_manager, 'set_tts_volume'):
                set_tts_volume = tts_manager.set_tts_volume
            new_volume = max(0.0, min(1.0, float(getattr(config, 'TTS_VOLUME', 1.0)) + float(delta)))
            set_tts_volume(new_volume)
            # UI refresh
            self.update_all_sliders_and_labels()
        except Exception as e:
            logging.error(f"[QtGUI] Chyba pri zmene hlasitosti cez shortcut: {e}")

    def _adjust_rate(self, delta: int):
        try:
            from platform_loader import get_tts_manager
            tts_manager = get_tts_manager()
            if tts_manager and hasattr(tts_manager, 'set_tts_rate'):
                set_tts_rate = tts_manager.set_tts_rate
            new_rate = int(getattr(config, 'TTS_RATE', 200) + int(delta))
            set_tts_rate(new_rate)
            # UI refresh
            self.update_all_sliders_and_labels()
        except Exception as e:
            logging.error(f"[QtGUI] Chyba pri zmene rýchlosti cez shortcut: {e}")

    def _on_anomaly_report(self):
        try:
            from testing import report_anomaly
            logging.info("Anomaly report spustený cez GUI tlačidlo (Qt)")
            report_anomaly()
        except Exception as e:
            logging.error(f"[QtGUI] Chyba pri spustení anomaly reportu: {e}")

    def _on_reset_settings(self):
        """Reset všetkých nastavení na pôvodné hodnoty podľa systému."""
        try:
            logging.info("🔄 Resetujem všetky nastavenia na systémové predvolené...")

            # Zastavenie čítania ak beží
            if getattr(config, 'is_reading', False):
                from app_logic import stop_reading
                stop_reading()

            # Použij settings_manager pre inteligentný reset
            import settings_manager

            # Resetuj na systémové nastavenia
            if settings_manager.initialize_system_defaults():
                logging.info("✅ Systémové nastavenia aplikované")
            else:
                logging.warning("⚠️ Fallback na predvolené nastavenia")
                # Fallback na základné nastavenia ak systémová detekcia zlyhá
                config.reading_mode = "full_automatic"
                config.READING_LANGUAGE = "cs"
                config.TTS_LANGUAGE = "cs-CZ"
                config.OCR_LANGUAGE = "ces"
                config.TTS_VOICE = "Iveta (Enhanced)"
                config.READING_TTS_VOICE = "Iveta (Enhanced)"
                config.SELECTED_READING_VOICE = "Iveta (Enhanced)"

            # Reset TTS nastavení na predvolené
            config.TTS_RATE = 200
            config.USER_TTS_RATE = 200
            config.TTS_VOLUME = 1.0
            config.ALLOW_UPPERCASE_TEXT = True

            # Reset OCR nastavení na predvolené
            config.OCR_THRESHOLD = 0.99
            config.DYNAMIC_OCR_THRESHOLD = 0.90
            config.MIN_TEXT_LENGTH_FOR_ANALYSIS = 3

            # Reset všetkých detection sliderov na predvolené
            config.TTS_HISTORY_SIMILARITY_THRESHOLD = 0.90
            config.STABILITY_SIMILARITY_THRESHOLD = 0.80
            config.TWO_FRAME_STABILITY_THRESHOLD = 0.90
            config.STATIC_SIMILARITY_THRESHOLD = 0.80
            config.STATIC_STABILITY_THRESHOLD = 0.80
            config.STATIC_REPLACEMENT_THRESHOLD = 0.50
            config.MIN_CYCLES_FIRST_LINE_STABILITY = 2
            config.MIN_CYCLES_SECOND_LINE_STABILITY = 2
            config.MIN_CYCLES_SECOND_LINE_GROWTH = 2
            config.STABILITA_JEDNEHO_TEXTU_CYKLY = 2
            config.POCET_ROZNYCH_STABILNYCH_TEXTOV_CIEL = 3

            # Reset prekladača - nastaví sa automaticky podľa systémového jazyka
            config.TRANSLATION_ENABLED = False

            # Aktualizácia jazykových nastavení podľa aktuálneho config
            if hasattr(self, 'language_manager'):
                current_reading_lang = getattr(config, 'READING_LANGUAGE', 'cs')
                self.language_manager.set_reading_language(current_reading_lang)

            # Kompletná aktualizácia GUI
            self.update_all_sliders_and_labels()
            self.update_mode_buttons(getattr(config, "reading_mode", "full_automatic"))
            self._update_detection_sliders()  # Aktualizuj detection slidery
            self._update_reading_voices()
            self._update_translation_voices()

            # Uloženie resetovaných nastavení
            self._save_settings()

            logging.info("✅ Všetky nastavenia boli resetované na systémové predvolené hodnoty")

            # Zobraz potvrdenie používateľovi
            from PyQt6.QtWidgets import QMessageBox
            # 🔧 OPRAVA: Použij i18n systém pre reset dialog
            try:
                reset_title = self.translator.t("reset_completed_title")
                reset_message = self.translator.t("reset_completed_message")
            except Exception as e:
                logging.warning(f"⚠️ Chyba pri získavaní i18n reset dialog textu: {e}")
                reset_title = "Reset dokončený"  # Fallback
                reset_message = "Všetky nastavenia boli obnovené na výchozí hodnoty podľa systémového jazyka."

            QMessageBox.information(
                self,
                reset_title,
                reset_message
            )

        except Exception as e:
            logging.error(f"[QtGUI] Chyba pri resetovaní nastavení: {e}")

    def _save_settings(self):
        """Uloží aktuálne nastavenia."""
        try:
            import settings_manager
            settings_manager.save_settings()
        except Exception as e:
            logging.error(f"[QtGUI] Chyba pri ukladaní nastavení: {e}")

    def _load_settings(self):
        """Načíta uložené nastavenia."""
        try:
            import settings_manager
            if settings_manager.load_settings():
                logging.info("✅ Nastavenia načítané z súboru")
            else:
                logging.info("⚠️ Použité predvolené nastavenia")
        except Exception as e:
            logging.error(f"[QtGUI] Chyba pri načítavaní nastavení: {e}")

    def _update_detection_sliders(self):
        """Aktualizuje slidery v záložke Detekce titulků podľa config hodnôt."""
        try:
            # Základné prahy (float slidery)
            if hasattr(self, 'tts_hist_slider'):
                val = int(getattr(config, 'TTS_HISTORY_SIMILARITY_THRESHOLD', 0.90) * 100)
                self.tts_hist_slider.blockSignals(True)
                self.tts_hist_slider.setValue(val)
                self.tts_hist_slider.blockSignals(False)
                self.tts_hist_label.setText(f"{val/100:.2f}")

            if hasattr(self, 'stability_sim_slider'):
                val = int(getattr(config, 'STABILITY_SIMILARITY_THRESHOLD', 0.80) * 100)
                self.stability_sim_slider.blockSignals(True)
                self.stability_sim_slider.setValue(val)
                self.stability_sim_slider.blockSignals(False)
                self.stability_sim_label.setText(f"{val/100:.2f}")

            if hasattr(self, 'two_frame_slider'):
                val = int(getattr(config, 'TWO_FRAME_STABILITY_THRESHOLD', 0.90) * 100)
                self.two_frame_slider.blockSignals(True)
                self.two_frame_slider.setValue(val)
                self.two_frame_slider.blockSignals(False)
                self.two_frame_label.setText(f"{val/100:.2f}")

            if hasattr(self, 'static_sim_slider'):
                val = int(getattr(config, 'STATIC_SIMILARITY_THRESHOLD', 0.80) * 100)
                self.static_sim_slider.blockSignals(True)
                self.static_sim_slider.setValue(val)
                self.static_sim_slider.blockSignals(False)
                self.static_sim_label.setText(f"{val/100:.2f}")

            if hasattr(self, 'static_stab_slider'):
                val = int(getattr(config, 'STATIC_STABILITY_THRESHOLD', 0.80) * 100)
                self.static_stab_slider.blockSignals(True)
                self.static_stab_slider.setValue(val)
                self.static_stab_slider.blockSignals(False)
                self.static_stab_label.setText(f"{val/100:.2f}")

            if hasattr(self, 'static_replace_slider'):
                val = int(getattr(config, 'STATIC_REPLACEMENT_THRESHOLD', 0.50) * 100)
                self.static_replace_slider.blockSignals(True)
                self.static_replace_slider.setValue(val)
                self.static_replace_slider.blockSignals(False)
                self.static_replace_label.setText(f"{val/100:.2f}")

            # Cyklovanie stability (int slidery)
            if hasattr(self, 'min_cyc_first_slider'):
                val = getattr(config, 'MIN_CYCLES_FIRST_LINE_STABILITY', 2)
                self.min_cyc_first_slider.blockSignals(True)
                self.min_cyc_first_slider.setValue(val)
                self.min_cyc_first_slider.blockSignals(False)
                self.min_cyc_first_label.setText(str(val))

            if hasattr(self, 'min_cyc_second_slider'):
                val = getattr(config, 'MIN_CYCLES_SECOND_LINE_STABILITY', 2)
                self.min_cyc_second_slider.blockSignals(True)
                self.min_cyc_second_slider.setValue(val)
                self.min_cyc_second_slider.blockSignals(False)
                self.min_cyc_second_label.setText(str(val))

            if hasattr(self, 'min_cyc_second_growth_slider'):
                val = getattr(config, 'MIN_CYCLES_SECOND_LINE_GROWTH', 2)
                self.min_cyc_second_growth_slider.blockSignals(True)
                self.min_cyc_second_growth_slider.setValue(val)
                self.min_cyc_second_growth_slider.blockSignals(False)
                self.min_cyc_second_growth_label.setText(str(val))

            if hasattr(self, 'single_text_cycles_slider'):
                val = getattr(config, 'STABILITA_JEDNEHO_TEXTU_CYKLY', 2)
                self.single_text_cycles_slider.blockSignals(True)
                self.single_text_cycles_slider.setValue(val)
                self.single_text_cycles_slider.blockSignals(False)
                self.single_text_cycles_label.setText(str(val))

            if hasattr(self, 'diff_texts_slider'):
                val = getattr(config, 'POCET_ROZNYCH_STABILNYCH_TEXTOV_CIEL', 3)
                self.diff_texts_slider.blockSignals(True)
                self.diff_texts_slider.setValue(val)
                self.diff_texts_slider.blockSignals(False)
                self.diff_texts_label.setText(str(val))

            logging.debug("🔧 Slidery v záložke Detekce titulků aktualizované")

        except Exception as e:
            logging.error(f"[QtGUI] Chyba pri aktualizácii detection sliderov: {e}")



    @QtCore.pyqtSlot(bool)
    def _apply_reading_state(self, is_reading: bool):
        button_styles = get_button_styles()
        if is_reading:
            self.start_stop_btn.setText(self.translator.t("stop_reading"))
            # Červené pozadie pre stop
            self.start_stop_btn.setStyleSheet(button_styles['danger'])
        else:
            self.start_stop_btn.setText(self.translator.t("start_reading"))
            # Modré pozadie pre start
            self.start_stop_btn.setStyleSheet(button_styles['primary'])

    # ---------- Toolbar a akcie ----------
    def _build_toolbar(self):
        # 🔧 OPRAVA: Použij i18n systém pre toolbar title
        try:
            toolbar_title = self.translator.t("toolbar_actions")
        except Exception as e:
            logging.warning(f"⚠️ Chyba pri získavaní i18n toolbar title: {e}")
            toolbar_title = "Akcie"  # Fallback

        tb = self.addToolBar(toolbar_title)
        tb.setMovable(False)

        self.action_toggle_reading = QtGui.QAction(self.translator.t("start_stop_action"), self)
        self.action_toggle_reading.triggered.connect(self._on_toggle_reading)
        tb.addAction(self.action_toggle_reading)

        tb.addSeparator()



    # ---------- Handlery ----------
    def _on_toggle_reading(self):
        try:
            self.toggle_reading_callback()
            self.update_start_button_text()
        except Exception as e:
            logging.error(f"[QtGUI] Chyba pri toggle_reading: {e}")



    def _on_toggle_allow_uppercase(self, checked: bool):
        try:
            config.ALLOW_UPPERCASE_TEXT = bool(checked)
            # 🔧 OPRAVA: Použij i18n systém pre uppercase reading status
            try:
                status_prefix = self.translator.t("uppercase_reading_status")
                status_value = self.translator.t("reading_allowed") if checked else self.translator.t("reading_forbidden")
                logging.info(f"{status_prefix} {status_value}")
            except Exception as e:
                logging.warning(f"⚠️ Chyba pri získavaní i18n uppercase status textu: {e}")
                logging.info("Čtení velkých písmen je " + ("povoleno" if checked else "zakázáno"))  # Fallback
        except Exception as e:
            logging.error(f"[QtGUI] Chyba pri prepínaní allow uppercase: {e}")

    # def _on_toggle_translation - ODSTRÁNENÉ

    def _disable_other_modes(self, disable: bool):
        """Zakáže/povolí ostatné režimy okrem statického."""
        try:
            # Zakáž/povoľ všetky režimy okrem statického
            for key, button in self.mode_buttons.items():
                if key != "static":
                    button.setEnabled(not disable)
                    if disable:
                        button.setStyleSheet(f"color: {MACOS_DARK_COLORS['text_disabled']}; background-color: {MACOS_DARK_COLORS['card_bg']};")
                    else:
                        button.setStyleSheet("")  # Reset na defaultný štýl
        except Exception as e:
            logging.error(f"[QtGUI] Chyba pri zakázaní režimov: {e}")

    def _on_source_language_changed(self):
        try:
            selected_data = self.source_lang_combo.currentData()
            if selected_data:
                # Nastaví source jazyk prekladača + OCR jazyk (už obsahuje translation manager sync)
                success = self.language_manager.set_translation_source_language(selected_data)

                if success:
                    logging.info(f"🔄 Prekladač source jazyk úspešne nastavený: {selected_data}")
                    self._save_settings()  # Automatické uloženie
                else:
                    logging.error(f"❌ Nepodarilo sa nastaviť source jazyk: {selected_data}")

        except Exception as e:
            logging.error(f"[QtGUI] Chyba při změně zdrojového jazyka: {e}")

    def _on_target_language_changed(self):
        try:
            selected_data = self.target_lang_combo.currentData()
            if selected_data:
                # Nastaví target jazyk prekladača + TTS jazyk + najlepší hlas (už obsahuje translation manager sync)
                success = self.language_manager.set_translation_target_language(selected_data)

                if success:
                    logging.info(f"🔄 Prekladač target jazyk úspešne nastavený: {selected_data}")
                    # Aktualizuje hlasy pre nový cieľový jazyk
                    self._update_translation_voices()
                    self._save_settings()  # Automatické uloženie
                else:
                    logging.error(f"❌ Nepodarilo sa nastaviť target jazyk: {selected_data}")

        except Exception as e:
            logging.error(f"[QtGUI] Chyba při změně cílového jazyka: {e}")

    def _on_app_language_changed(self):
        """Zmena jazyka aplikácie."""
        try:
            selected_data = self.app_language_combo.currentData()
            if selected_data:
                # Zmení jazyk aplikácie
                set_app_language(selected_data)

                # Aktualizuje GUI texty
                self.refresh_gui_texts()

                # Aktualizuj texty v demo widget
                try:
                    from demo_timer import get_demo_widget
                    demo_widget = get_demo_widget()
                    if demo_widget:
                        demo_widget.refresh_gui_texts()
                except Exception as e:
                    logging.warning(f"⚠️ Chyba pri aktualizácii demo widget textov: {e}")

                # 🔧 OPRAVA: TTS hlášky sa NEAKTUALIZUJÚ pri zmene jazyka aplikácie!
                # TTS hlášky zostávajú v jazyku čítania, nie aplikácie
                logging.info(f"🔊 TTS hlášky zostávajú v jazyku čítania (nezmenené)")

                logging.info(f"[GUI] Jazyk aplikace změněn na: {selected_data}")
                self._save_settings()  # Automatické uloženie
        except Exception as e:
            logging.error(f"[QtGUI] Chyba při změně jazyka aplikace: {e}")

    def _on_reading_language_changed(self):
        """Zmena jazyka čítania titulkov."""
        try:
            selected_data = self.reading_language_combo.currentData()
            if selected_data:
                # Zmení jazyk čítania (OCR + TTS)
                set_reading_language(selected_data)

                # Synchronizuje prekladač - nastaví source language
                if hasattr(self, 'source_lang_combo'):
                    source_index = self.source_lang_combo.findData(selected_data)
                    if source_index >= 0:
                        self.source_lang_combo.blockSignals(True)
                        self.source_lang_combo.setCurrentIndex(source_index)
                        self.source_lang_combo.blockSignals(False)

                        # Aktualizuje config
                        config.TRANSLATION_SOURCE_LANGUAGE = selected_data

                        # Aktualizuje translation manager
                        try:
                            import translation_manager
                            translation_manager.set_source_language(selected_data)
                            logging.info(f"🔄 Prekladač synchronizovaný: source jazyk → {selected_data}")
                        except Exception as e:
                            logging.error(f"Chyba pri synchronizácii prekladača: {e}")

                logging.info(f"[GUI] Jazyk čtení titulků změněn na: {selected_data}")

                # Aktualizuje TTS engine ak je potrebné
                self._update_tts_language()

                # Aktualizuje hlasy pre nový jazyk
                self._update_reading_voices()

                # Automatické uloženie nastavení
                self._save_settings()

        except Exception as e:
            logging.error(f"[QtGUI] Chyba při změně jazyka čtení: {e}")

    def refresh_gui_texts(self):
        """Aktualizuje všetky texty v GUI podľa aktuálneho jazyka."""
        try:
            # Aktualizuj translator
            self.translator = get_translator()

            # Aktualizuj názov okna
            self.setWindowTitle(self.translator.t("app_title"))

            # Aktualizuj názvy tabov
            if hasattr(self, 'tabs'):
                self.tabs.setTabText(0, self.translator.t("main_tab"))
                if self.tabs.count() > 1:
                    self.tabs.setTabText(1, self.translator.t("detection_tab"))

            # Aktualizuj hlavné tlačidlo
            if hasattr(self, 'start_stop_btn'):
                if hasattr(self, 'is_reading') and self.is_reading:
                    self.start_stop_btn.setText(self.translator.t("stop_reading"))
                else:
                    self.start_stop_btn.setText(self.translator.t("start_reading"))

            # Aktualizuj skupiny pomocou uložených translation keys
            self._refresh_group_titles()

            # Aktualizuj všetky elementy s uloženými translation keys
            self._refresh_elements_with_translation_keys()

            # Aktualizuj všetky ostatné elementy (fallback pre elementy bez translation keys)
            self._refresh_fallback_elements()

            # Aktualizuj dropdown menu pre prekladače
            if hasattr(self, 'translator_engine_combo'):
                current_selection = self.translator_engine_combo.currentData()
                self.translator_engine_combo.blockSignals(True)
                self.translator_engine_combo.clear()
                self.translator_engine_combo.addItem(self.translator.t("translator_engine_speed"), "speed")
                self.translator_engine_combo.addItem(self.translator.t("translator_engine_balanced"), "balanced")
                self.translator_engine_combo.addItem(self.translator.t("translator_engine_quality"), "quality")
                self.translator_engine_combo.addItem(self.translator.t("translator_engine_nllb"), "nllb")
                self.translator_engine_combo.addItem(self.translator.t("translator_engine_microsoft"), "microsoft")
                # Obnoviť výber
                index = self.translator_engine_combo.findData(current_selection)
                if index >= 0:
                    self.translator_engine_combo.setCurrentIndex(index)
                self.translator_engine_combo.blockSignals(False)

            # Aktualizuj dropdown menu pre výkon
            if hasattr(self, 'performance_mode_combo'):
                current_selection = self.performance_mode_combo.currentData()
                self.performance_mode_combo.blockSignals(True)
                self.performance_mode_combo.clear()
                self.performance_mode_combo.addItem(self.translator.t("performance_speed"), "speed")
                self.performance_mode_combo.addItem(self.translator.t("performance_balanced"), "balanced")
                self.performance_mode_combo.addItem(self.translator.t("performance_quality"), "quality")
                self.performance_mode_combo.addItem(self.translator.t("performance_custom"), "custom")
                # Obnoviť výber
                index = self.performance_mode_combo.findData(current_selection)
                if index >= 0:
                    self.performance_mode_combo.setCurrentIndex(index)
                self.performance_mode_combo.blockSignals(False)

            # Aktualizuj tlačidlá
            if hasattr(self, 'clear_cache_btn'):
                self.clear_cache_btn.setText("🗑️ " + self.translator.t("clear_cache"))
                self.clear_cache_btn.setToolTip(self.translator.t("tooltip_clear_cache"))
            if hasattr(self, 'optimize_memory_btn'):
                self.optimize_memory_btn.setText("🧹 " + self.translator.t("optimize_memory"))
                self.optimize_memory_btn.setToolTip(self.translator.t("tooltip_optimize_memory"))
            if hasattr(self, 'speed_test_btn'):
                self.speed_test_btn.setText(self.translator.t("speed_test"))
                self.speed_test_btn.setToolTip(self.translator.t("speed_test_tooltip"))
            if hasattr(self, 'custom_params_button'):
                self.custom_params_button.setText(self.translator.t("custom_settings_button"))
                self.custom_params_button.setToolTip(self.translator.t("custom_settings_tooltip"))

            # Aktualizuj tooltips
            if hasattr(self, 'translator_engine_combo'):
                self.translator_engine_combo.setToolTip(self.translator.t("tooltip_translator_type"))
            if hasattr(self, 'performance_mode_combo'):
                self.performance_mode_combo.setToolTip(self.translator.t("tooltip_performance"))

            # Aktualizuj radio button texty
            if hasattr(self, 'mode_buttons'):
                for key, label_key in [("full_automatic", "mode_full_auto"), ("static", "mode_static"), ("dynamic", "mode_dynamic")]:
                    if key in self.mode_buttons:
                        self.mode_buttons[key].setText(self.translator.t(label_key))

            # Aktualizuj checkbox texty
            if hasattr(self, 'allow_uppercase_check'):
                self.allow_uppercase_check.setText(self.translator.t("allow_uppercase"))

            # Aktualizuj labels
            for child in self.findChildren(QtWidgets.QLabel):
                text = child.text()
                # Hotkey hint sa aktualizuje cez translation key systém
                if "Prahování OCR:" in text or "OCR prah:" in text or "OCR threshold:" in text:
                    child.setText(self.translator.t("ocr_threshold"))
                elif "🎤 Hlas pre čítanie:" in text or "🎤 Voice for reading:" in text or "🎤 Hlas pro čtení:" in text:
                    child.setText(self.translator.t("voice_for_reading"))
                elif "🎤 Hlas pre preklad:" in text or "🎤 Voice for translation:" in text or "🎤 Hlas pro překlad:" in text:
                    child.setText(self.translator.t("voice_for_translation"))
                elif "Rychlost řeči:" in text or "Rýchlosť reči:" in text or "Speech rate:" in text:
                    child.setText(self.translator.t("speech_rate"))
                elif "Hlasitost řeči:" in text or "Hlasitosť reči:" in text or "Speech volume:" in text:
                    child.setText(self.translator.t("speech_volume"))
                # Jazykové labely
                elif "Jazyk aplikace:" in text or "Application Language:" in text or "Jazyk aplikácie:" in text:
                    child.setText(self.translator.t("app_language"))
                elif "Jazyk čtení titulků:" in text or "Subtitle Reading Language:" in text or "Jazyk čítania titulkov:" in text:
                    child.setText(self.translator.t("reading_language"))
                elif "Preklad z:" in text or "Translate from:" in text or "Preložiť z:" in text:
                    child.setText(self.translator.t("translate_from"))
                elif "Preklad do:" in text or "Translate to:" in text or "Preložiť do:" in text:
                    child.setText(self.translator.t("translate_to"))
                # OCR interval label
                elif "OCR interval:" in text or "OCR interval" in text:
                    child.setText(self.translator.t("ocr_interval_label"))

            # Aktualizuj štatistiky
            if hasattr(self, 'translation_stats_label'):
                self.translation_stats_label.setText(self.translator.t("translator_stats"))

            # Aktualizuj informačné labely o dostupných jazykoch
            if hasattr(self, 'language_info_label'):
                available_count = len(self.language_manager.get_available_reading_languages())
                total_ocr = len(self.language_manager.available_languages.get("ocr", set()))
                total_tts = len(self.language_manager.available_languages.get("tts", set()))
                info_text = self.translator.t("available_languages_info").format(count=available_count, ocr=total_ocr, tts=total_tts)
                self.language_info_label.setText(info_text)

            if hasattr(self, 'source_info_label'):
                source_count = len(self.language_manager.get_available_source_languages()) if hasattr(self.language_manager, 'get_available_source_languages') else 0
                source_info = self.translator.t("available_source_info").format(count=source_count)
                self.source_info_label.setText(source_info)

            if hasattr(self, 'target_info_label'):
                target_count = len(self.language_manager.get_available_target_languages()) if hasattr(self.language_manager, 'get_available_target_languages') else 0
                target_info = self.translator.t("available_target_info").format(count=target_count)
                self.target_info_label.setText(target_info)

            # Aktualizuj filtering status
            if hasattr(self, 'filtering_status_label'):
                self._apply_filtering_display()

            # Aktualizuj button texty
            if hasattr(self, 'reset_left_btn'):
                self.reset_left_btn.setText(self.translator.t("reset_left_detection"))
            if hasattr(self, 'anomaly_btn'):
                self.anomaly_btn.setText(self.translator.t("report_anomaly"))

            # Aktualizuj text labely v detekčnej záložke
            self._refresh_detection_text_labels()

            logging.info("🔄 GUI texty aktualizované")

        except Exception as e:
            logging.error(f"[QtGUI] Chyba pri aktualizácii GUI textov: {e}")

    def _refresh_group_titles(self):
        """Aktualizuje názvy skupín pomocou uložených translation keys."""
        try:
            # Aktualizuj skupiny s uloženými translation keys
            if hasattr(self, 'mode_group'):
                self.mode_group.setTitle(self.translator.t("mode_group"))
            if hasattr(self, 'language_group'):
                self.language_group.setTitle(self.translator.t("language_group"))
            if hasattr(self, 'tts_group'):
                self.tts_group.setTitle(self.translator.t("tts_group"))
            if hasattr(self, 'ocr_group'):
                self.ocr_group.setTitle(self.translator.t("ocr_group"))

            # Aktualizuj všetky GroupBox kontajnery s translation_key atribútom
            for child in self.findChildren(QtWidgets.QGroupBox):
                if hasattr(child, 'translation_key'):
                    child.setTitle(self.translator.t(child.translation_key))

        except Exception as e:
            logging.error(f"[QtGUI] Chyba pri aktualizácii group titles: {e}")

    def _refresh_elements_with_translation_keys(self):
        """Aktualizuje všetky elementy s uloženými translation keys."""
        try:
            # Aktualizuj všetky QLabel elementy s translation_key atribútom
            for child in self.findChildren(QtWidgets.QLabel):
                if hasattr(child, 'translation_key'):
                    child.setText(self.translator.t(child.translation_key))

            # Aktualizuj všetky QPushButton elementy s translation_key atribútom
            for child in self.findChildren(QtWidgets.QPushButton):
                if hasattr(child, 'translation_key'):
                    child.setText(self.translator.t(child.translation_key))

            # Aktualizuj všetky QCheckBox elementy s translation_key atribútom
            for child in self.findChildren(QtWidgets.QCheckBox):
                if hasattr(child, 'translation_key'):
                    child.setText(self.translator.t(child.translation_key))

        except Exception as e:
            logging.error(f"[QtGUI] Chyba pri aktualizácii elements with translation keys: {e}")

    def _set_translation_key(self, widget, translation_key):
        """Nastaví translation key pre widget, aby sa mohol aktualizovať pri zmene jazyka."""
        widget.translation_key = translation_key

    def _create_label_with_translation(self, translation_key, parent=None):
        """Vytvorí QLabel s translation key pre automatické aktualizovanie."""
        label = QtWidgets.QLabel(self.translator.t(translation_key), parent)
        self._set_translation_key(label, translation_key)
        return label

    def _refresh_fallback_elements(self):
        """Fallback aktualizácia pre elementy bez translation keys."""
        # Aktualizuj všetky GroupBox kontajnery (pre elementy bez translation keys)
        for child in self.findChildren(QtWidgets.QGroupBox):
            if hasattr(child, 'translation_key'):
                continue  # Preskočiť elementy s translation keys

            title = child.title()
            # Hlavná záložka
            if "Režim čtení" in title or "Reading Mode" in title or "Režim čítania" in title:
                child.setTitle(self.translator.t("mode_group"))
            elif "OCR" in title and ("Režim čtení" not in title and "Reading Mode" not in title):
                child.setTitle(self.translator.t("ocr_group"))
            elif "Jazyk" in title or "Language" in title:
                child.setTitle(self.translator.t("language_group"))
            elif "Prekladač" in title or "Translator" in title:
                child.setTitle(self.translator.t("translator_group"))
            elif "Řeč (TTS)" in title or "Speech (TTS)" in title or "TTS" in title:
                child.setTitle(self.translator.t("tts_group"))
            # Záložka detekcie
            elif "Základní prahy podobnosti" in title or "Basic similarity thresholds" in title or "Základné prahy" in title:
                child.setTitle(self.translator.t("similarity_thresholds"))
            elif "Cyklování stability" in title or "Cycling stability" in title:
                child.setTitle(self.translator.t("cycling_stability"))
            elif "Dynamický růst" in title or "Dynamic growth" in title:
                child.setTitle(self.translator.t("dynamic_growth"))
            elif "Plně automatické prahy" in title or "Fully automatic thresholds" in title:
                child.setTitle(self.translator.t("full_auto_thresholds"))

    def _refresh_detection_text_labels(self):
        """Aktualizuje text labely v detekčnej záložke podľa aktuálneho jazyka."""
        try:
            # Základné prahy podobnosti
            if hasattr(self, 'tts_hist_text_label'):
                self.tts_hist_text_label.setText(self.translator.t("tts_duplicate_threshold"))
            if hasattr(self, 'stability_sim_text_label'):
                self.stability_sim_text_label.setText(self.translator.t("stability_similarity"))
            if hasattr(self, 'two_frame_text_label'):
                self.two_frame_text_label.setText(self.translator.t("two_frame_stability"))
            if hasattr(self, 'static_sim_text_label'):
                self.static_sim_text_label.setText(self.translator.t("static_similarity"))
            if hasattr(self, 'static_stab_text_label'):
                self.static_stab_text_label.setText(self.translator.t("static_stability_threshold"))
            if hasattr(self, 'static_replace_text_label'):
                self.static_replace_text_label.setText(self.translator.t("static_replacement_threshold"))

            # Cyklovanie stability
            if hasattr(self, 'min_cyc_first_text_label'):
                self.min_cyc_first_text_label.setText(self.translator.t("min_cycles_first_line"))
            if hasattr(self, 'min_cyc_second_text_label'):
                self.min_cyc_second_text_label.setText(self.translator.t("min_cycles_second_line"))
            if hasattr(self, 'min_cyc_second_growth_text_label'):
                self.min_cyc_second_growth_text_label.setText(self.translator.t("min_cycles_second_growth"))
            if hasattr(self, 'single_text_cycles_text_label'):
                self.single_text_cycles_text_label.setText(self.translator.t("single_text_cycles"))
            if hasattr(self, 'diff_texts_text_label'):
                self.diff_texts_text_label.setText(self.translator.t("different_texts_count"))

            # Dynamický rast
            if hasattr(self, 'dyn_growth_text_label'):
                self.dyn_growth_text_label.setText(self.translator.t("dynamic_growth_similarity"))
            if hasattr(self, 'dyn_min_len_text_label'):
                self.dyn_min_len_text_label.setText(self.translator.t("dynamic_min_word_length"))
            if hasattr(self, 'dyn_max_words_text_label'):
                self.dyn_max_words_text_label.setText(self.translator.t("dynamic_max_words"))

            # Full Automatic prahy
            if hasattr(self, 'fa_init_static_text_label'):
                self.fa_init_static_text_label.setText(self.translator.t("fa_initial_static"))
            if hasattr(self, 'fa_init_dynamic_text_label'):
                self.fa_init_dynamic_text_label.setText(self.translator.t("fa_initial_dynamic"))
            if hasattr(self, 'fa_static_thr_text_label'):
                self.fa_static_thr_text_label.setText(self.translator.t("fa_stable_static"))
            if hasattr(self, 'fa_dynamic_thr_text_label'):
                self.fa_dynamic_thr_text_label.setText(self.translator.t("fa_stable_dynamic"))
            if hasattr(self, 'fa_min_samples_text_label'):
                self.fa_min_samples_text_label.setText(self.translator.t("fa_min_samples"))
            if hasattr(self, 'fa_replace_text_label'):
                self.fa_replace_text_label.setText(self.translator.t("fa_replace_threshold"))

            # OCR a analýza
            if hasattr(self, 'ocr_thr_text_label'):
                self.ocr_thr_text_label.setText(self.translator.t("ocr_threshold_param"))
            if hasattr(self, 'dyn_ocr_thr_text_label'):
                self.dyn_ocr_thr_text_label.setText(self.translator.t("dynamic_ocr_threshold"))
            if hasattr(self, 'min_text_len_text_label'):
                self.min_text_len_text_label.setText(self.translator.t("min_text_length"))

        except Exception as e:
            logging.error(f"[QtGUI] Chyba pri aktualizácii detection text labelov: {e}")

    def _update_tts_language(self):
        """Aktualizuje TTS jazyk."""
        try:
            tts_lang = self.language_manager.get_tts_language()
            logging.info(f"🔊 TTS jazyk aktualizovaný na: {tts_lang}")

            # TTS hlášky používajú jazyk čítania (nie aplikácie)
            from i18n_manager import update_tts_language
            import common_config as config
            reading_lang = getattr(config, 'READING_LANGUAGE', 'cs')
            update_tts_language(reading_lang)
            logging.info(f"🔊 TTS hlášky aktualizované na jazyk čítania: {reading_lang}")

            # Tu by sa mal aktualizovať TTS engine
            # Implementácia závisí od TTS systému

        except Exception as e:
            logging.error(f"[QtGUI] Chyba pri aktualizácii TTS jazyka: {e}")

    def _update_reading_voices(self):
        """Aktualizuje dropdown menu hlasov pre čítanie podľa zvoleného jazyka."""
        try:
            if not hasattr(self, 'reading_voice_combo'):
                return

            current_lang = self.reading_language_combo.currentData()
            if not current_lang:
                return

            # Vyčisti aktuálne hlasy
            self.reading_voice_combo.blockSignals(True)
            self.reading_voice_combo.clear()

            # Získaj hlasy pre jazyk
            voices = self.language_manager.get_voices_for_language(current_lang)

            if voices:
                for voice in voices:
                    voice_name = voice['name']

                    # Skontroluj, či hlas už obsahuje (Enhanced), (Standard) alebo (Premium)
                    if any(suffix in voice_name for suffix in [" (Enhanced)", " (Standard)", " (Premium)"]):
                        # Hlas už má suffix, použij ho ako je
                        display_name = voice_name
                    else:
                        # 🔧 OPRAVA: Pridaj suffix podľa kvality hlasu (Premium > Enhanced > Standard)
                        voice_quality = voice.get('quality', 'standard')
                        if voice_quality == 'premium':
                            voice_type = " (Premium)"
                        elif voice_quality == 'enhanced':
                            voice_type = " (Enhanced)"
                        else:
                            voice_type = " (Standard)"
                        display_name = f"{voice_name}{voice_type}"

                    self.reading_voice_combo.addItem(display_name, display_name)

                # Nastav aktuálny hlas - najprv skús načítať uložený hlas pre jazyk
                current_tts_lang = getattr(config, 'TTS_LANGUAGE', 'cs-CZ')
                saved_voice = None

                try:
                    import settings_manager
                    saved_voice = settings_manager.get_saved_voice_for_language(current_tts_lang)
                except Exception as e:
                    logging.debug(f"Chyba pri načítavaní uloženého hlasu: {e}")

                # Ak už máme nastavený najlepší hlas v config, použij ho
                current_config_voice = getattr(config, "SELECTED_READING_VOICE", None)

                # Preferuj config hlas ak existuje, inak uložený hlas, inak fallback
                if current_config_voice:
                    preferred_voice = current_config_voice
                else:
                    preferred_voice = saved_voice or getattr(config, "TTS_VOICE", "")

                voice_index = self.reading_voice_combo.findData(preferred_voice)

                logging.info(f"🔍 Debug načítavania hlasu pre {current_tts_lang}:")
                logging.info(f"   💾 Uložený hlas: {saved_voice}")
                logging.info(f"   ⚙️ Config hlas: {current_config_voice}")
                logging.info(f"   🎯 Preferovaný hlas: {preferred_voice}")
                logging.info(f"   📍 Index v combo: {voice_index}")

                # Ak sa hlas nenašiel presne, skús nájsť podobný
                if voice_index < 0 and preferred_voice:
                    # Skús nájsť hlas podľa základného názvu (bez Enhanced/Standard/Premium)
                    base_voice_name = preferred_voice.replace(" (Enhanced)", "").replace(" (Standard)", "").replace(" (Premium)", "")
                    logging.info(f"   🔍 Hľadám podobný hlas pre: {base_voice_name}")

                    # Najprv skús nájsť akúkoľvek verziu s rovnakým základným názvom
                    # Priorita: Premium > Enhanced > Standard
                    for priority_suffix in [" (Premium)", " (Enhanced)", " (Standard)"]:
                        target_voice = f"{base_voice_name}{priority_suffix}"
                        for i in range(self.reading_voice_combo.count()):
                            combo_voice = self.reading_voice_combo.itemData(i)
                            if combo_voice == target_voice:
                                voice_index = i
                                preferred_voice = combo_voice
                                logging.info(f"   ✅ Nájdený hlas s prioritou: {combo_voice}")
                                break
                        if voice_index >= 0:  # Ak sa našiel, prestaň hľadať
                            break

                    # Ak sa Standard nenašiel, skús základný názov bez prípony
                    if voice_index < 0:
                        for i in range(self.reading_voice_combo.count()):
                            combo_voice = self.reading_voice_combo.itemData(i)
                            if combo_voice == base_voice_name:
                                voice_index = i
                                preferred_voice = combo_voice
                                logging.info(f"   ✅ Nájdený presný základný hlas: {combo_voice}")
                                break

                    # Ak sa ani to nenašlo, skús akýkoľvek hlas s rovnakým základným názvom
                    if voice_index < 0:
                        for i in range(self.reading_voice_combo.count()):
                            combo_voice = self.reading_voice_combo.itemData(i)
                            combo_base = combo_voice.replace(" (Enhanced)", "").replace(" (Standard)", "").replace(" (Premium)", "")
                            if combo_base == base_voice_name:
                                voice_index = i
                                preferred_voice = combo_voice  # Použij plný názov z combo
                                logging.info(f"   ✅ Nájdený podobný hlas: {combo_voice}")
                                break

                if voice_index >= 0:
                    self.reading_voice_combo.setCurrentIndex(voice_index)
                    # Aktualizuj config s načítaným hlasom
                    config.SELECTED_READING_VOICE = preferred_voice
                    config.READING_TTS_VOICE = preferred_voice
                    if not getattr(config, "TRANSLATION_ENABLED", False):
                        config.TTS_VOICE = preferred_voice
                    logging.info(f"✅ Nastavený hlas pre {current_tts_lang}: {preferred_voice}")
                else:
                    # Fallback na prvý hlas
                    self.reading_voice_combo.setCurrentIndex(0)
                    if self.reading_voice_combo.count() > 0:
                        first_voice = self.reading_voice_combo.itemData(0)
                        config.SELECTED_READING_VOICE = first_voice
                        config.READING_TTS_VOICE = first_voice
                        logging.info(f"🎤 Fallback na prvý dostupný hlas: {first_voice}")
            else:
                # Žiadne hlasy dostupné
                self.reading_voice_combo.addItem(self.translator.t("no_voices_available"), "")

            self.reading_voice_combo.blockSignals(False)
            logging.info(f"🎤 Aktualizované hlasy pre čítanie jazyka {current_lang}: {len(voices)} hlasov")

        except Exception as e:
            logging.error(f"[QtGUI] Chyba pri aktualizácii hlasov pre čítanie: {e}")

    def _update_translation_voices(self):
        """Aktualizuje dropdown menu hlasov pre preklad podľa zvoleného cieľového jazyka."""
        try:
            if not hasattr(self, 'translation_voice_combo'):
                return

            current_lang = self.target_lang_combo.currentData()
            if not current_lang:
                return

            # Vyčisti aktuálne hlasy
            self.translation_voice_combo.blockSignals(True)
            self.translation_voice_combo.clear()

            # Získaj hlasy pre jazyk
            voices = self.language_manager.get_voices_for_language(current_lang)

            if voices:
                for voice in voices:
                    voice_name = voice['name']

                    # Skontroluj, či hlas už obsahuje (Enhanced), (Standard) alebo (Premium)
                    if any(suffix in voice_name for suffix in [" (Enhanced)", " (Standard)", " (Premium)"]):
                        # Hlas už má suffix, použij ho ako je
                        display_name = voice_name
                    else:
                        # 🔧 OPRAVA: Pridaj suffix podľa kvality hlasu (Premium > Enhanced > Standard)
                        voice_quality = voice.get('quality', 'standard')
                        if voice_quality == 'premium':
                            voice_type = " (Premium)"
                        elif voice_quality == 'enhanced':
                            voice_type = " (Enhanced)"
                        else:
                            voice_type = " (Standard)"
                        display_name = f"{voice_name}{voice_type}"

                    self.translation_voice_combo.addItem(display_name, display_name)

                # Nastav aktuálny hlas - najprv skús načítať uložený hlas pre jazyk
                target_lang = getattr(config, 'TRANSLATION_TARGET_LANGUAGE', 'en')
                # Mapovanie jazyka na TTS jazyk
                tts_lang_map = {
                    'en': 'en-US', 'cs': 'cs-CZ', 'sk': 'sk-SK',
                    'de': 'de-DE', 'fr': 'fr-FR'
                }
                tts_lang = tts_lang_map.get(target_lang, f"{target_lang}-{target_lang.upper()}")
                saved_voice = None

                try:
                    import settings_manager
                    saved_voice = settings_manager.get_saved_voice_for_language(tts_lang)
                except Exception as e:
                    logging.debug(f"Chyba pri načítavaní uloženého hlasu pre preklad: {e}")

                # Použij uložený hlas ak existuje, inak aktuálny
                preferred_voice = saved_voice or getattr(config, "SELECTED_TRANSLATION_VOICE", config.TTS_VOICE)
                voice_index = self.translation_voice_combo.findData(preferred_voice)

                if voice_index >= 0:
                    self.translation_voice_combo.setCurrentIndex(voice_index)
                    # Aktualizuj config s načítaným hlasom
                    config.SELECTED_TRANSLATION_VOICE = preferred_voice
                    config.TRANSLATION_TTS_VOICE = preferred_voice
                    if getattr(config, "TRANSLATION_ENABLED", False):
                        config.TTS_VOICE = preferred_voice
                    logging.info(f"🎤 Načítaný uložený hlas pre preklad {tts_lang}: {preferred_voice}")
                else:
                    # Fallback na prvý hlas
                    self.translation_voice_combo.setCurrentIndex(0)
                    if self.translation_voice_combo.count() > 0:
                        first_voice = self.translation_voice_combo.itemData(0)
                        config.SELECTED_TRANSLATION_VOICE = first_voice
                        config.TRANSLATION_TTS_VOICE = first_voice
                        logging.info(f"🎤 Fallback na prvý dostupný hlas pre preklad: {first_voice}")
            else:
                # Žiadne hlasy dostupné
                self.translation_voice_combo.addItem(self.translator.t("no_voices_available"), "")

            self.translation_voice_combo.blockSignals(False)
            logging.info(f"🎤 Aktualizované hlasy pre preklad jazyka {current_lang}: {len(voices)} hlasov")

        except Exception as e:
            logging.error(f"[QtGUI] Chyba pri aktualizácii hlasov pre preklad: {e}")

    def _on_reading_voice_changed(self):
        """Handler pre zmenu hlasu čítania."""
        try:
            selected_voice = self.reading_voice_combo.currentData()
            if selected_voice:
                config.SELECTED_READING_VOICE = selected_voice
                config.READING_TTS_VOICE = selected_voice
                # Ak nie je preklad aktívny, nastav aj hlavný TTS hlas
                if not getattr(config, "TRANSLATION_ENABLED", False):
                    config.TTS_VOICE = selected_voice

                # Uloží hlas pre aktuálny jazyk (s plným názvom vrátane prípony)
                current_tts_lang = getattr(config, 'TTS_LANGUAGE', 'cs-CZ')
                try:
                    import settings_manager
                    settings_manager.save_voice_for_language(current_tts_lang, selected_voice)
                    logging.info(f"💾 Uložený hlas pre {current_tts_lang}: {selected_voice}")
                except Exception as e:
                    logging.error(f"Chyba pri ukladaní hlasu pre jazyk: {e}")

                logging.info(f"🎤 Hlas pre čítanie zmenený na: {selected_voice}")
                self._save_settings()  # Automatické uloženie
        except Exception as e:
            logging.error(f"[QtGUI] Chyba pri zmene hlasu čítania: {e}")

    # def _on_translation_voice_changed - ODSTRÁNENÉ

    # def _on_translator_engine_changed - ODSTRÁNENÉ

    # Všetky prekladačové metódy boli odstránené

    # ---------- Always-on-top ----------
    def apply_always_on_top(self, enabled: bool):
        flags = self.windowFlags()
        if enabled:
            flags |= QtCore.Qt.WindowType.WindowStaysOnTopHint
        else:
            flags &= ~QtCore.Qt.WindowType.WindowStaysOnTopHint
        self.setWindowFlags(flags)
        # Re-aplikovať flagy
        self.show()


# Prekladačové triedy boli odstránené

# Zvyšok prekladačových tried bol odstránený

# Všetky prekladačové triedy boli odstránené
