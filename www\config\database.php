<?php
/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> konfigur<PERSON>cia pre VOXO LOXO
 */

// Databázové nastavenia
$db_config = [
    'host' => 'sql20.hostcreators.sk',
    'port' => '3325',
    'dbname' => 'd52810_voxoloxo',
    'username' => 'u52810_radis',
    'password' => 'Emanuel<PERSON><PERSON><PERSON>sky1975?',
    'charset' => 'utf8mb4'
];

try {
    $dsn = "mysql:host={$db_config['host']};port={$db_config['port']};dbname={$db_config['dbname']};charset={$db_config['charset']}";
    $pdo = new PDO($dsn, $db_config['username'], $db_config['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
    ]);
} catch (PDOException $e) {
    error_log("Database connection failed: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Database connection failed']);
    exit();
}

// Stripe konfigurácia
define('STRIPE_SECRET_KEY', 'sk_test_your_stripe_secret_key');
define('STRIPE_PUBLISHABLE_KEY', 'pk_test_your_stripe_publishable_key');
define('STRIPE_WEBHOOK_SECRET', 'whsec_your_webhook_secret');

// Aplikačné nastavenia
define('APP_URL', 'https://voxoloxo.com');
define('APP_NAME', 'VOXO LOXO - Cross-Platform');
define('SUPPORT_EMAIL', '<EMAIL>');

// Cenové nastavenia
define('ANNUAL_LICENSE_PRICE', 29900); // 299 Kč v haléřoch (Stripe používa najmenšie jednotky)
define('CURRENCY', 'czk');

// Bezpečnostné nastavenia
define('JWT_SECRET', 'your_jwt_secret_key_here');
define('PASSWORD_SALT', 'your_password_salt_here');

// Email nastavenia (pre SMTP)
define('SMTP_HOST', 'smtp.voxoloxo.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your_smtp_password');

// Funkcie pre prácu s databázou

/**
 * Vytvorí nového užívateľa
 */
function createUser($email, $password, $first_name, $last_name) {
    global $pdo;
    
    $password_hash = password_hash($password . PASSWORD_SALT, PASSWORD_DEFAULT);
    
    $stmt = $pdo->prepare("
        INSERT INTO users (email, password_hash, first_name, last_name, created_at) 
        VALUES (?, ?, ?, ?, NOW())
    ");
    
    $stmt->execute([$email, $password_hash, $first_name, $last_name]);
    return $pdo->lastInsertId();
}

/**
 * Overí užívateľa
 */
function verifyUser($email, $password) {
    global $pdo;
    
    $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ? AND status = 'active'");
    $stmt->execute([$email]);
    $user = $stmt->fetch();
    
    if ($user && password_verify($password . PASSWORD_SALT, $user['password_hash'])) {
        return $user;
    }
    
    return false;
}

/**
 * Vytvorí novú licenciu
 */
function createLicense($user_id, $purchase_id, $stripe_payment_intent_id) {
    global $pdo;
    
    $license_key = generateLicenseKey();
    $expires_at = date('Y-m-d H:i:s', strtotime('+1 year'));
    
    $stmt = $pdo->prepare("
        INSERT INTO licenses (user_id, license_key, purchase_id, stripe_payment_intent_id, expires_at, status, created_at) 
        VALUES (?, ?, ?, ?, ?, 'active', NOW())
    ");
    
    $stmt->execute([$user_id, $license_key, $purchase_id, $stripe_payment_intent_id, $expires_at]);
    
    return [
        'license_key' => $license_key,
        'expires_at' => $expires_at
    ];
}

/**
 * Generuje licenčný kľúč
 */
function generateLicenseKey() {
    $chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    $key = '';
    for ($i = 0; $i < 4; $i++) {
        if ($i > 0) $key .= '-';
        for ($j = 0; $j < 4; $j++) {
            $key .= $chars[random_int(0, strlen($chars) - 1)];
        }
    }
    return $key;
}

/**
 * Odošle email
 */
function sendEmail($to, $subject, $body, $is_html = true) {
    // Jednoduchý PHP mail() pre testovanie
    $headers = "From: " . SUPPORT_EMAIL . "\r\n";
    $headers .= "Reply-To: " . SUPPORT_EMAIL . "\r\n";

    if ($is_html) {
        $headers .= "Content-Type: text/html; charset=UTF-8\r\n";
    } else {
        $headers .= "Content-Type: text/plain; charset=UTF-8\r\n";
    }

    // Pokus o odoslanie
    $result = mail($to, $subject, $body, $headers);

    if ($result) {
        error_log("Email successfully sent to {$to}: {$subject}");
        return true;
    } else {
        error_log("Failed to send email to {$to}: {$subject}");
        return false;
    }
}

/**
 * Vytvorí email token pre overenie
 */
function createEmailVerificationToken($user_id) {
    global $pdo;
    
    $token = bin2hex(random_bytes(32));
    $expires_at = date('Y-m-d H:i:s', strtotime('+24 hours'));
    
    $stmt = $pdo->prepare("
        INSERT INTO email_verification_tokens (user_id, token, expires_at, created_at) 
        VALUES (?, ?, ?, NOW())
        ON DUPLICATE KEY UPDATE token = VALUES(token), expires_at = VALUES(expires_at)
    ");
    
    $stmt->execute([$user_id, $token, $expires_at]);
    
    return $token;
}

/**
 * Overí email token
 */
function verifyEmailToken($token) {
    global $pdo;
    
    $stmt = $pdo->prepare("
        SELECT user_id FROM email_verification_tokens 
        WHERE token = ? AND expires_at > NOW() AND used_at IS NULL
    ");
    $stmt->execute([$token]);
    $result = $stmt->fetch();
    
    if ($result) {
        // Označí token ako použitý
        $stmt = $pdo->prepare("UPDATE email_verification_tokens SET used_at = NOW() WHERE token = ?");
        $stmt->execute([$token]);
        
        // Aktivuje užívateľa
        $stmt = $pdo->prepare("UPDATE users SET email_verified_at = NOW(), status = 'active' WHERE id = ?");
        $stmt->execute([$result['user_id']]);
        
        return $result['user_id'];
    }
    
    return false;
}
?>
