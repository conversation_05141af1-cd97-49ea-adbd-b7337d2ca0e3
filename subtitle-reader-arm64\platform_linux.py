#!/usr/bin/env python3
"""
Linux-specific implementations for Subtitle Reader application.
"""

import sys
import logging
import subprocess
import hashlib
import locale
import os
from typing import Optional, Dict, List, Any
from pathlib import Path

from platform_utils import (
    TTSProvider, ActiveAppDetector, SystemInfoProvider, 
    BrowserURLDetector, PlatformError, IS_LINUX
)

if not IS_LINUX:
    raise ImportError("This module is only for Linux platform")

# Linux-specific imports
try:
    import pyttsx3
    PYTTSX3_AVAILABLE = True
except ImportError:
    PYTTSX3_AVAILABLE = False
    logging.warning("⚠️ pyttsx3 not available - TTS will not work")

class LinuxTTSProvider(TTSProvider):
    """Linux TTS implementation using pyttsx3 or espeak."""
    
    def __init__(self):
        super().__init__()
        self.engine = None
        self.use_espeak = False
        self._initialize_engine()
    
    def _check_platform_support(self) -> bool:
        return IS_LINUX
    
    def _initialize_engine(self):
        """Initialize TTS engine."""
        # Try pyttsx3 first
        if PYTTSX3_AVAILABLE:
            try:
                self.engine = pyttsx3.init()
                self.engine.setProperty('rate', 200)
                self.engine.setProperty('volume', 1.0)
                logging.info("✅ Linux TTS engine (pyttsx3) initialized")
                return
            except Exception as e:
                logging.warning(f"⚠️ pyttsx3 initialization failed: {e}")
        
        # Check for espeak
        try:
            subprocess.run(['espeak', '--version'], capture_output=True, check=True)
            self.use_espeak = True
            logging.info("✅ Linux TTS engine (espeak) available")
        except (subprocess.CalledProcessError, FileNotFoundError):
            logging.error("❌ No TTS engine available (install espeak or pyttsx3)")
    
    def speak(self, text: str, voice: str = None, rate: int = 200) -> bool:
        """Speak text using Linux TTS."""
        if self.engine:
            return self._speak_pyttsx3(text, voice, rate)
        elif self.use_espeak:
            return self._speak_espeak(text, rate)
        else:
            logging.error("❌ No TTS engine available")
            return False
    
    def _speak_pyttsx3(self, text: str, voice: str, rate: int) -> bool:
        """Speak using pyttsx3."""
        try:
            self.engine.setProperty('rate', rate)
            if voice:
                voices = self.engine.getProperty('voices')
                for v in voices:
                    if voice.lower() in v.name.lower():
                        self.engine.setProperty('voice', v.id)
                        break
            
            self.engine.say(text)
            self.engine.runAndWait()
            return True
        except Exception as e:
            logging.error(f"❌ pyttsx3 speak error: {e}")
            return False
    
    def _speak_espeak(self, text: str, rate: int) -> bool:
        """Speak using espeak."""
        try:
            # Convert rate (words per minute to espeak speed)
            espeak_speed = max(80, min(450, rate))
            
            subprocess.run([
                'espeak', 
                '-s', str(espeak_speed),
                text
            ], check=True)
            return True
        except Exception as e:
            logging.error(f"❌ espeak speak error: {e}")
            return False
    
    def stop(self) -> bool:
        """Stop current TTS playback."""
        if self.engine:
            try:
                self.engine.stop()
                return True
            except Exception as e:
                logging.error(f"❌ TTS stop error: {e}")
        
        # For espeak, we can't easily stop it
        return False
    
    def get_available_voices(self) -> List[Dict[str, Any]]:
        """Get list of available Linux TTS voices."""
        voices = []
        
        if self.engine:
            try:
                engine_voices = self.engine.getProperty('voices')
                for voice in engine_voices:
                    voice_info = {
                        'id': voice.id,
                        'name': voice.name,
                        'language': getattr(voice, 'languages', ['en'])[0] if hasattr(voice, 'languages') else 'en',
                        'quality': 'standard'
                    }
                    voices.append(voice_info)
            except Exception as e:
                logging.error(f"❌ Error getting voices: {e}")
        
        elif self.use_espeak:
            # espeak has limited voice info
            voices.append({
                'id': 'default',
                'name': 'espeak default',
                'language': 'en',
                'quality': 'basic'
            })
        
        return voices
    
    def is_speaking(self) -> bool:
        """Check if TTS is currently speaking."""
        if self.engine:
            try:
                return self.engine.isBusy()
            except:
                pass
        return False

class LinuxActiveAppDetector(ActiveAppDetector):
    """Linux active application detection (limited functionality)."""
    
    def _check_platform_support(self) -> bool:
        return IS_LINUX
    
    def get_active_app_name(self) -> str:
        """Get name of currently active application on Linux."""
        try:
            # Try xdotool (if available)
            result = subprocess.run([
                'xdotool', 'getactivewindow', 'getwindowname'
            ], capture_output=True, text=True, timeout=2)
            
            if result.returncode == 0:
                return result.stdout.strip()
                
        except (subprocess.CalledProcessError, FileNotFoundError):
            pass
        
        # Try wmctrl (if available)
        try:
            result = subprocess.run([
                'wmctrl', '-l'
            ], capture_output=True, text=True, timeout=2)
            
            if result.returncode == 0:
                # Parse wmctrl output to find active window
                # This is a simplified implementation
                lines = result.stdout.strip().split('\n')
                if lines:
                    # Return first window name as fallback
                    parts = lines[0].split(None, 3)
                    if len(parts) >= 4:
                        return parts[3]
                        
        except (subprocess.CalledProcessError, FileNotFoundError):
            pass
        
        logging.debug("⚠️ No method available for active app detection on Linux")
        return ""
    
    def get_active_window_title(self) -> str:
        """Get title of currently active window."""
        return self.get_active_app_name()  # Same implementation for now

class LinuxSystemInfoProvider(SystemInfoProvider):
    """Linux system information provider."""
    
    def _check_platform_support(self) -> bool:
        return IS_LINUX
    
    def get_system_language(self) -> str:
        """Get Linux system default language."""
        try:
            # Try locale
            lang_code = locale.getdefaultlocale()[0]
            if lang_code:
                if '_' in lang_code:
                    lang, region = lang_code.split('_', 1)
                    return f"{lang}-{region}"
                return lang_code
        except Exception:
            pass
        
        # Try environment variables
        for env_var in ['LANG', 'LANGUAGE', 'LC_ALL']:
            lang = os.getenv(env_var)
            if lang:
                # Parse format like 'en_US.UTF-8'
                if '.' in lang:
                    lang = lang.split('.')[0]
                if '_' in lang:
                    lang, region = lang.split('_', 1)
                    return f"{lang}-{region}"
                return lang
        
        return "en-US"
    
    def get_hardware_fingerprint(self) -> str:
        """Get Linux hardware fingerprint."""
        components = []
        
        try:
            # Try to get machine ID
            machine_id_paths = [
                '/etc/machine-id',
                '/var/lib/dbus/machine-id'
            ]
            
            for path in machine_id_paths:
                try:
                    with open(path, 'r') as f:
                        machine_id = f.read().strip()
                        if machine_id:
                            components.append(f"machine_id:{machine_id}")
                            break
                except:
                    continue
            
            # Try to get DMI info
            dmi_paths = [
                '/sys/class/dmi/id/product_uuid',
                '/sys/class/dmi/id/board_serial',
                '/sys/class/dmi/id/chassis_serial'
            ]
            
            for path in dmi_paths:
                try:
                    with open(path, 'r') as f:
                        value = f.read().strip()
                        if value and value.lower() not in ['none', 'not specified', '']:
                            components.append(f"dmi:{Path(path).name}:{value}")
                except:
                    continue
            
            # Try to get CPU info
            try:
                with open('/proc/cpuinfo', 'r') as f:
                    for line in f:
                        if line.startswith('Serial'):
                            serial = line.split(':', 1)[1].strip()
                            if serial:
                                components.append(f"cpu_serial:{serial}")
                                break
            except:
                pass
            
            if not components:
                # Fallback to hostname
                import socket
                hostname = socket.gethostname()
                components.append(f"hostname:{hostname}")
            
            # Create hash
            combined = "|".join(sorted(components))
            fingerprint = hashlib.sha256(combined.encode('utf-8')).hexdigest()[:32]
            
            logging.info(f"🔐 Linux hardware fingerprint created: {fingerprint[:8]}...")
            return fingerprint
            
        except Exception as e:
            logging.error(f"❌ Error creating hardware fingerprint: {e}")
            # Emergency fallback
            import platform
            fallback = hashlib.sha256(platform.node().encode('utf-8')).hexdigest()[:32]
            return fallback

class LinuxBrowserURLDetector(BrowserURLDetector):
    """Linux browser URL detection (limited functionality)."""
    
    def _check_platform_support(self) -> bool:
        return IS_LINUX
    
    def get_browser_url(self) -> str:
        """Get current browser URL (Linux implementation limited)."""
        logging.warning("⚠️ Browser URL detection not implemented for Linux")
        # TODO: Implement using browser-specific methods or D-Bus
        return ""
    
    def get_youtube_video_time(self, url: str) -> Optional[float]:
        """Get current YouTube video time (Linux implementation limited)."""
        logging.warning("⚠️ YouTube video time detection not implemented for Linux")
        return None
