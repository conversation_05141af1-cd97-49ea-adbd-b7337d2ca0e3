<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test pozadí - VOXO LOXO</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        /* Hero sekcia s hero.png pozadím */
        .hero-section {
            background:
                linear-gradient(rgba(25, 42, 86, 0.4), rgba(13, 27, 62, 0.4)),
                url('hero.png') center/cover no-repeat;
            color: white;
            padding: 120px 0;
            position: relative;
            min-height: 100vh;
            display: flex;
            align-items: center;
        }

        .hero-section .container {
            width: 100%;
            max-width: 1400px;
        }

        /* iOS Safari fix - fallback bez fixed attachment */
        @supports (-webkit-touch-callout: none) {
            .hero-section {
                background-attachment: scroll;
            }
        }

        /* Špecifický fix pre iOS */
        @media screen and (-webkit-min-device-pixel-ratio: 2) {
            .hero-section {
                background-attachment: scroll !important;
            }
        }
        
        /* Fallback pre chýbajúci obrázok */
        .hero-section.no-image {
            background:
                linear-gradient(135deg, rgba(25, 42, 86, 0.4) 0%, rgba(13, 27, 62, 0.4) 100%),
                repeating-linear-gradient(
                    45deg,
                    transparent,
                    transparent 10px,
                    rgba(255,255,255,0.1) 10px,
                    rgba(255,255,255,0.1) 20px
                );
        }
        
        /* FAQ sekcia so statickým pozadím */
        .faq-section {
            background:
                rgba(248, 249, 250, 0.9),
                url('faq.png') center/cover no-repeat;
            position: relative;
            min-height: 100vh;
            padding: 100px 0;
        }

        .faq-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('faq.png') center/cover no-repeat;
            z-index: -1;
        }

        /* iOS Safari fix pre FAQ sekciu */
        @supports (-webkit-touch-callout: none) {
            .faq-section,
            .faq-section::before {
                background-attachment: scroll;
            }
        }

        @media screen and (-webkit-min-device-pixel-ratio: 2) {
            .faq-section,
            .faq-section::before {
                background-attachment: scroll !important;
            }
        }
        
        /* Fallback pre chýbajúci FAQ obrázok */
        .faq-section.no-image::before {
            background: 
                repeating-linear-gradient(
                    90deg,
                    #f8f9fa,
                    #f8f9fa 50px,
                    #e9ecef 50px,
                    #e9ecef 100px
                ) fixed;
        }
        
        .faq-section .container {
            position: relative;
            z-index: 1;
            background: rgba(255, 255, 255, 0.5);
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }

        /* Podporované platformy sekcia s overlay.png */
        .platforms-section {
            background: url('overlay.png') center/cover no-repeat;
            position: relative;
            min-height: 500px;
            color: white;
            padding: 100px 0;
        }

        /* Download sekcia s download.png */
        .download-section {
            background:
                linear-gradient(rgba(248, 249, 250, 0.1), rgba(248, 249, 250, 0.1)),
                url('download.png') center/cover no-repeat;
            position: relative;
            min-height: 600px;
            padding: 100px 0;
        }

        /* Fallback pre chýbajúce nové obrázky */
        .platforms-section.no-image {
            background:
                linear-gradient(135deg, #343a40 0%, #495057 100%),
                repeating-linear-gradient(
                    45deg,
                    transparent,
                    transparent 20px,
                    rgba(255,255,255,0.1) 20px,
                    rgba(255,255,255,0.1) 40px
                );
            color: white;
        }

        .download-section.no-image {
            background:
                linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%),
                repeating-linear-gradient(
                    -45deg,
                    transparent,
                    transparent 15px,
                    rgba(0,123,255,0.1) 15px,
                    rgba(0,123,255,0.1) 30px
                );
        }

        /* OS detekcia styling */
        .platform-card {
            transition: all 0.3s ease;
            border-radius: 10px;
            padding: 20px;
            margin: 10px 0;
            background: rgba(128, 128, 128, 0.5);
            border: 2px solid transparent;
            color: white;
        }

        .download-card {
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .current-os-windows {
            border: 3px solid #007bff !important;
            box-shadow: 0 0 20px rgba(0, 123, 255, 0.4) !important;
            transform: scale(1.02) !important;
        }

        .current-os-macos {
            border: 3px solid #28a745 !important;
            box-shadow: 0 0 20px rgba(40, 167, 69, 0.4) !important;
            transform: scale(1.02) !important;
        }

        .current-os-linux {
            border: 3px solid #ffc107 !important;
            box-shadow: 0 0 20px rgba(255, 193, 7, 0.4) !important;
            transform: scale(1.02) !important;
        }

        /* Platform cards zvýraznenie - OS farby */
        .platform-card.current-os-windows {
            background: rgba(0, 123, 255, 0.7) !important;
            color: white !important;
        }

        .platform-card.current-os-macos {
            background: rgba(40, 167, 69, 0.7) !important;
            color: white !important;
        }

        .platform-card.current-os-linux {
            background: rgba(255, 193, 7, 0.7) !important;
            color: white !important;
        }

        /* Download cards zvýraznenie - 70% priehľadnosť + biele texty */
        .download-card.current-os-windows {
            background: rgba(0, 123, 255, 0.7) !important;
        }

        .download-card.current-os-windows .card-body,
        .download-card.current-os-windows .card-body h4,
        .download-card.current-os-windows .card-body p,
        .download-card.current-os-windows .card-body small {
            color: white !important;
        }

        .download-card.current-os-macos {
            background: rgba(40, 167, 69, 0.7) !important;
        }

        .download-card.current-os-macos .card-body,
        .download-card.current-os-macos .card-body h4,
        .download-card.current-os-macos .card-body p,
        .download-card.current-os-macos .card-body small {
            color: white !important;
        }

        .download-card.current-os-linux {
            background: rgba(255, 193, 7, 0.7) !important;
        }

        .download-card.current-os-linux .card-body,
        .download-card.current-os-linux .card-body h4,
        .download-card.current-os-linux .card-body p,
        .download-card.current-os-linux .card-body small {
            color: white !important;
        }

        /* OS-specific indicator colors */
        .os-indicator {
            position: absolute;
            top: -10px;
            right: -10px;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .current-os-windows .os-indicator {
            background: #007bff;
        }

        .current-os-macos .os-indicator {
            background: #28a745;
        }

        .current-os-linux .os-indicator {
            background: #ffc107;
        }

        /* OS-specific alert colors */
        .os-alert-windows {
            background-color: #007bff !important;
            border-color: #0056b3 !important;
            color: white !important;
        }

        .os-alert-macos {
            background-color: #28a745 !important;
            border-color: #1e7e34 !important;
            color: white !important;
        }

        .os-alert-linux {
            background-color: #ffc107 !important;
            border-color: #e0a800 !important;
            color: white !important;
        }
        
        /* Animácie */
        .main-headline {
            animation: fadeInUp 1.2s ease-out;
        }
        
        .main-headline h1 {
            animation: slideInLeft 1s ease-out 0.3s both;
            line-height: 1.1;
        }
        
        .main-headline h2 {
            animation: slideInRight 1s ease-out 0.6s both;
            line-height: 1.3;
        }
        
        .brand-badge {
            animation: bounceIn 1s ease-out 0.9s both;
        }
        
        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-100px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
        
        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(100px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
        
        @keyframes bounceIn {
            0% {
                opacity: 0;
                transform: scale(0.3);
            }
            50% {
                opacity: 1;
                transform: scale(1.05);
            }
            70% {
                transform: scale(0.9);
            }
            100% {
                opacity: 1;
                transform: scale(1);
            }
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        /* Responzívne úpravy */
        @media (max-width: 768px) {
            .hero-section {
                padding: 80px 0 !important;
            }
            
            .main-headline h1 {
                font-size: 2.5rem !important;
            }
            
            .main-headline h2 {
                font-size: 1.8rem !important;
            }
            
            .faq-section .container {
                padding: 20px !important;
            }
        }
        
        /* Demo sekcie */
        .demo-section {
            padding: 50px 0;
            background: #f8f9fa;
        }
        
        .demo-card {
            background: white;
            border-radius: 10px;
            padding: 30px;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-success { background: #28a745; }
        .status-warning { background: #ffc107; }
        .status-error { background: #dc3545; }
    </style>
</head>
<body>
    <!-- Hero Section s hero.png pozadím -->
    <section class="hero-section" id="heroSection">
        <div class="container text-center">
            <!-- Hlavná hláška -->
            <div class="main-headline mb-5">
                <h1 class="display-1 fw-bold text-white mb-4" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.5); font-size: 4.5rem;">
                    <i class="fas fa-search me-4" style="color: white;"></i>
                    Už nemusíte číst titulky!
                </h1>
                <h2 class="display-3 fw-light text-white mb-5" style="font-size: 3rem;">
                    Naše čtečka titulků Vám je přečte
                </h2>
                <div class="brand-badge">
                    <span class="badge bg-light text-dark mb-3" style="font-size: 2.5rem; padding: 15px 30px; border-radius: 25px; box-shadow: 0 4px 15px rgba(0,0,0,0.2);">
                        VOXO LOXO
                    </span>
                    <div class="mt-3">
                        <span class="text-white" style="font-size: 1.8rem; font-weight: 300;">čtečka titulků</span>
                    </div>
                </div>
            </div>

            <div class="mb-4">
                <span class="badge bg-success fs-6 px-3 py-2">20 minut denně ZDARMA</span>
            </div>
            
            <a href="#faq" class="btn btn-light btn-lg me-3">
                <i class="fas fa-play"></i> Vyzkoušet Demo
            </a>
            <a href="#" class="btn btn-outline-light btn-lg">
                <i class="fas fa-shopping-cart"></i> Koupit za 299 Kč
            </a>
        </div>
    </section>
    
    <!-- Demo sekcia -->
    <section class="demo-section">
        <div class="container">
            <h2 class="text-center mb-5">Test pozadí</h2>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="demo-card">
                        <h4><i class="fas fa-image text-primary"></i> Hero pozadie (hero.png)</h4>
                        <p><strong>Typ:</strong> Rolovací pozadie s gradientom</p>
                        <p><strong>Správanie:</strong> Pozadie sa roluje so stránkou</p>
                        <p><strong>Status:</strong> 
                            <span class="status-indicator status-success"></span>
                            <span id="heroStatus">Načítava sa...</span>
                        </p>
                        <button class="btn btn-sm btn-outline-primary" onclick="toggleHeroImage()">
                            Prepnúť fallback
                        </button>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="demo-card">
                        <h4><i class="fas fa-question-circle text-warning"></i> FAQ pozadie (faq.png)</h4>
                        <p><strong>Typ:</strong> Statické pozadie (fixed)</p>
                        <p><strong>Správanie:</strong> Pozadie zostáva statické pri rolovaní</p>
                        <p><strong>Priehľadnosť:</strong> 50% pre FAQ kontajner</p>
                        <p><strong>Status:</strong>
                            <span class="status-indicator status-success"></span>
                            <span id="faqStatus">Načítava sa...</span>
                        </p>
                        <button class="btn btn-sm btn-outline-warning" onclick="toggleFaqImage()">
                            Prepnúť fallback
                        </button>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="demo-card">
                        <h4><i class="fas fa-desktop text-info"></i> Platformy pozadie (overlay.png)</h4>
                        <p><strong>Typ:</strong> Rolovací pozadie s overlay</p>
                        <p><strong>Správanie:</strong> Pozadie sa roluje so stránkou</p>
                        <p><strong>OS detekcia:</strong> Zvýrazňuje aktuálny systém</p>
                        <p><strong>Status:</strong>
                            <span class="status-indicator status-success"></span>
                            <span id="overlayStatus">Načítava sa...</span>
                        </p>
                        <button class="btn btn-sm btn-outline-info" onclick="toggleOverlayImage()">
                            Prepnúť fallback
                        </button>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="demo-card">
                        <h4><i class="fas fa-download text-success"></i> Download pozadie (download.png)</h4>
                        <p><strong>Typ:</strong> Rolovací pozadie s kontajnerom</p>
                        <p><strong>Správanie:</strong> Pozadie sa roluje so stránkou</p>
                        <p><strong>OS detekcia:</strong> Zvýrazňuje odporúčané súbory</p>
                        <p><strong>Status:</strong>
                            <span class="status-indicator status-success"></span>
                            <span id="downloadStatus">Načítava sa...</span>
                        </p>
                        <button class="btn btn-sm btn-outline-success" onclick="toggleDownloadImage()">
                            Prepnúť fallback
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="text-center mt-4">
                <a href="#faq" class="btn btn-primary btn-lg">
                    <i class="fas fa-arrow-down"></i> Prejsť na FAQ sekciu
                </a>
            </div>
        </div>
    </section>
    
    <!-- Platforms Section s overlay.png pozadím -->
    <section class="platforms-section py-5">
        <div class="container text-center">
            <h2 class="mb-5">Podporované platformy</h2>
            <p class="lead">Toto je ukázka platforms sekcie s overlay.png pozadím.</p>
            <div class="row">
                <div class="col-md-4">
                    <div class="platform-card" data-os="windows">
                        <i class="fab fa-windows" style="font-size: 4rem; margin-bottom: 1rem;"></i>
                        <h4>Windows</h4>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="platform-card" data-os="macos">
                        <i class="fab fa-apple" style="font-size: 4rem; margin-bottom: 1rem;"></i>
                        <h4>macOS</h4>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="platform-card" data-os="linux">
                        <i class="fab fa-linux" style="font-size: 4rem; margin-bottom: 1rem;"></i>
                        <h4>Linux</h4>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Download Section s download.png pozadím -->
    <section class="download-section py-5">
        <div class="container text-center">
            <h2 class="mb-5">Stáhnout aplikaci</h2>
            <p class="lead">Toto je ukázka download sekcie s download.png pozadím.</p>
            <div class="row">
                <div class="col-md-4">
                    <div class="card download-card" data-os="windows">
                        <div class="card-body">
                            <h4><i class="fab fa-windows text-primary"></i> Windows</h4>
                            <button class="btn btn-primary">Stáhnout</button>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card download-card" data-os="macos">
                        <div class="card-body">
                            <h4><i class="fab fa-apple text-success"></i> macOS</h4>
                            <button class="btn btn-success">Stáhnout</button>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card download-card" data-os="linux">
                        <div class="card-body">
                            <h4><i class="fab fa-linux text-warning"></i> Linux</h4>
                            <button class="btn btn-warning">Stáhnout</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section so statickým pozadím -->
    <section class="faq-section" id="faq">
        <div class="container">
            <h2 class="text-center mb-5">Často kladené otázky</h2>
            <div class="row justify-content-center">
                <div class="col-md-8 col-lg-6">
                    <div class="accordion" id="faqAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                                    Ako funguje pozadie?
                                </button>
                            </h2>
                            <div id="faq1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    Hero pozadie (hero.png) sa roluje so stránkou, zatiaľ čo FAQ pozadie (faq.png) 
                                    zostáva statické a stránka sa po ňom "kĺže".
                                </div>
                            </div>
                        </div>
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                                    Čo ak obrázky neexistujú?
                                </button>
                            </h2>
                            <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    Implementované sú fallback riešenia - pre hero.png sa použije pruhovaný gradient,
                                    pre faq.png sa použije šachovnicový vzor.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="text-center mt-5">
                <a href="index.php" class="btn btn-success btn-lg">
                    <i class="fas fa-arrow-left"></i> Späť na hlavnú stránku
                </a>
            </div>
        </div>
    </section>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Test načítania obrázkov
        function checkImageExists(url, callback) {
            const img = new Image();
            img.onload = () => callback(true);
            img.onerror = () => callback(false);
            img.src = url;
        }
        
        // Kontrola hero.png
        checkImageExists('hero.png', function(exists) {
            const status = document.getElementById('heroStatus');
            const indicator = status.previousElementSibling;
            
            if (exists) {
                status.textContent = 'Obrázok načítaný úspešne';
                indicator.className = 'status-indicator status-success';
            } else {
                status.textContent = 'Obrázok nenájdený - použitý fallback';
                indicator.className = 'status-indicator status-warning';
                document.getElementById('heroSection').classList.add('no-image');
            }
        });
        
        // Kontrola faq.png
        checkImageExists('faq.png', function(exists) {
            const status = document.getElementById('faqStatus');
            const indicator = status.previousElementSibling;
            
            if (exists) {
                status.textContent = 'Obrázok načítaný úspešne';
                indicator.className = 'status-indicator status-success';
            } else {
                status.textContent = 'Obrázok nenájdený - použitý fallback';
                indicator.className = 'status-indicator status-warning';
                document.querySelector('.faq-section').classList.add('no-image');
            }
        });
        
        // Toggle funkcie pre testovanie
        function toggleHeroImage() {
            const heroSection = document.getElementById('heroSection');
            heroSection.classList.toggle('no-image');
            
            const status = document.getElementById('heroStatus');
            const indicator = status.previousElementSibling;
            
            if (heroSection.classList.contains('no-image')) {
                status.textContent = 'Fallback režim aktívny';
                indicator.className = 'status-indicator status-warning';
            } else {
                status.textContent = 'Normálny režim aktívny';
                indicator.className = 'status-indicator status-success';
            }
        }
        
        function toggleFaqImage() {
            const faqSection = document.querySelector('.faq-section');
            faqSection.classList.toggle('no-image');
            
            const status = document.getElementById('faqStatus');
            const indicator = status.previousElementSibling;
            
            if (faqSection.classList.contains('no-image')) {
                status.textContent = 'Fallback režim aktívny';
                indicator.className = 'status-indicator status-warning';
            } else {
                status.textContent = 'Normálny režim aktívny';
                indicator.className = 'status-indicator status-success';
            }
        }

        // Toggle funkcie pre nové pozadia
        function toggleOverlayImage() {
            const overlaySection = document.querySelector('.platforms-section');
            overlaySection.classList.toggle('no-image');

            const status = document.getElementById('overlayStatus');
            const indicator = status.previousElementSibling;

            if (overlaySection.classList.contains('no-image')) {
                status.textContent = 'Fallback režim aktivovaný';
                indicator.className = 'status-indicator status-warning';
            } else {
                status.textContent = 'Obrázok overlay.png načítaný';
                indicator.className = 'status-indicator status-success';
            }
        }

        function toggleDownloadImage() {
            const downloadSection = document.querySelector('.download-section');
            downloadSection.classList.toggle('no-image');

            const status = document.getElementById('downloadStatus');
            const indicator = status.previousElementSibling;

            if (downloadSection.classList.contains('no-image')) {
                status.textContent = 'Fallback režim aktivovaný';
                indicator.className = 'status-indicator status-warning';
            } else {
                status.textContent = 'Obrázok download.png načítaný';
                indicator.className = 'status-indicator status-success';
            }
        }

        // OS detekcia
        function detectOS() {
            const userAgent = navigator.userAgent.toLowerCase();
            const platform = navigator.platform.toLowerCase();

            if (userAgent.includes('win') || platform.includes('win')) {
                return 'windows';
            } else if (userAgent.includes('mac') || platform.includes('mac')) {
                return 'macos';
            } else if (userAgent.includes('linux') || platform.includes('linux') || userAgent.includes('x11')) {
                return 'linux';
            } else {
                return 'unknown';
            }
        }

        // Zvýraznenie aktuálneho OS
        function highlightCurrentOS() {
            const currentOS = detectOS();

            if (currentOS === 'unknown') return;

            console.log('Detekovaný OS:', currentOS);

            // Zvýrazni platform karty
            const platformCards = document.querySelectorAll('.platform-card');
            platformCards.forEach(card => {
                if (card.dataset.os === currentOS) {
                    card.classList.add(`current-os-${currentOS}`);
                    card.style.position = 'relative';

                    const indicator = document.createElement('div');
                    indicator.className = 'os-indicator';
                    indicator.innerHTML = '✓';
                    indicator.title = 'Váš operačný systém';
                    card.appendChild(indicator);
                }
            });

            // Zvýrazni download karty
            const downloadCards = document.querySelectorAll('.download-card');
            downloadCards.forEach(card => {
                if (card.dataset.os === currentOS) {
                    card.classList.add(`current-os-${currentOS}`);
                    card.style.position = 'relative';

                    const indicator = document.createElement('div');
                    indicator.className = 'os-indicator';
                    indicator.innerHTML = '✓';
                    indicator.title = 'Odporúčané pre váš OS';
                    card.appendChild(indicator);

                    const recommendation = document.createElement('div');
                    recommendation.className = `alert os-alert-${currentOS} mt-2 mb-0`;
                    recommendation.innerHTML = `
                        <i class="fas fa-check-circle me-2"></i>
                        <strong>Odporúčané pre váš systém</strong>
                    `;
                    card.querySelector('.card-body').appendChild(recommendation);
                }
            });
        }

        // Inicializácia
        document.addEventListener('DOMContentLoaded', function() {
            checkImageExists('hero.png', 'heroStatus');
            checkImageExists('faq.png', 'faqStatus');
            checkImageExists('overlay.png', 'overlayStatus');
            checkImageExists('download.png', 'downloadStatus');
            highlightCurrentOS();
        });

        // Smooth scroll
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>
