#!/usr/bin/env python3
"""
🧪 Test OCR Text Corrector - Test opráv častých OCR chýb
"""

import logging
import sys
import os

# Nastavenie logovania
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Import modulov
try:
    from ocr_text_corrector import correct_ocr_text, correct_missing_I, OCRTextCorrector
    import common_utils
    import common_config as config
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)

def test_ocr_corrector_basic():
    """Test základných funkcií OCR korektora."""
    print("🧪 Test 1: Základné funkcie OCR korektora")
    print("=" * 60)
    
    # Test prípady s anglickými textami
    test_cases_en = [
        # Problém z logu
        ("| merely say that the cause lies here at the clinic.", "I merely say that the cause lies here at the clinic."),
        ("| heard that you failed a medical student.", "I heard that you failed a medical student."),
        
        # Ďalšie časté prípady
        ("l think that this is wrong.", "I think that this is wrong."),
        ("1 believe you are right.", "I believe you are right."),
        ("| am going to the store.", "I am going to the store."),
        ("| was there yesterday.", "I was there yesterday."),
        ("| will come tomorrow.", "I will come tomorrow."),
        
        # Kontextové opravy
        ("but | think otherwise.", "but I think otherwise."),
        ("and | heard the news.", "and I heard the news."),
        ("so | decided to leave.", "so I decided to leave."),
        
        # Texty bez chýb
        ("Normal text without errors.", "Normal text without errors."),
        ("This is a correct sentence.", "This is a correct sentence."),
    ]
    
    # Test prípady s inými jazykmi (nemali by sa opravovať)
    test_cases_other = [
        ("| som doma.", "| som doma."),  # slovenčina
        ("| jsem doma.", "| jsem doma."),  # čeština
        ("| bin zu Hause.", "| bin zu Hause."),  # nemčina
    ]
    
    success_count = 0
    total_count = 0
    
    # Test anglických textov
    print("📝 Test anglických textov (mali by sa opraviť):")
    for original, expected in test_cases_en:
        corrected = correct_ocr_text(original, "en")
        status = "✅" if corrected == expected else "❌"
        print(f"   {status} '{original}' -> '{corrected}'")
        if expected != original:
            print(f"      Očakávané: '{expected}'")
        
        if corrected == expected:
            success_count += 1
        total_count += 1
    
    # Test iných jazykov
    print("\n📝 Test iných jazykov (nemali by sa opraviť):")
    for original, expected in test_cases_other:
        corrected = correct_ocr_text(original, "sk")
        status = "✅" if corrected == expected else "❌"
        print(f"   {status} '{original}' -> '{corrected}' (jazyk: sk)")
        
        if corrected == expected:
            success_count += 1
        total_count += 1
    
    print(f"\n📊 Výsledok: {success_count}/{total_count} testov úspešných")
    return success_count == total_count

def test_missing_I_corrector():
    """Test špecializovaného korektora pre chýbajúce I."""
    print("\n🧪 Test 2: Špecializovaný korektor pre chýbajúce I")
    print("=" * 60)
    
    test_cases = [
        # Jednoriadkové texty
        ("| merely say that...", "I merely say that..."),
        ("l think so.", "I think so."),
        ("1 believe it.", "I believe it."),
        
        # Viacriadkové texty
        ("| merely say\nthat the cause lies here.", "I merely say\nthat the cause lies here."),
        ("First line\n| second line", "First line\nI second line"),
        
        # Texty bez chýb
        ("Normal text", "Normal text"),
        ("I am fine", "I am fine"),
    ]
    
    success_count = 0
    total_count = 0
    
    for original, expected in test_cases:
        corrected = correct_missing_I(original, "en")
        status = "✅" if corrected == expected else "❌"
        print(f"   {status} '{original}' -> '{corrected}'")
        if expected != original:
            print(f"      Očakávané: '{expected}'")
        
        if corrected == expected:
            success_count += 1
        total_count += 1
    
    print(f"\n📊 Výsledok: {success_count}/{total_count} testov úspešných")
    return success_count == total_count

def test_integration_with_common_utils():
    """Test integrácie s common_utils.clean_text."""
    print("\n🧪 Test 3: Integrácia s common_utils.clean_text")
    print("=" * 60)
    
    # Žiadne špeciálne nastavenia nie sú potrebné
    
    try:
        test_cases = [
            "| merely say that the cause lies here.",
            "| heard that you failed a medical student.",
            "l think this is correct.",
            "Normal text without errors.",
        ]
        
        success_count = 0
        total_count = 0
        
        for test_text in test_cases:
            print(f"\n📝 Test: '{test_text}'")
            
            # Test clean_text funkcie
            cleaned_lines = common_utils.clean_text(test_text)
            cleaned_text = ' '.join(cleaned_lines) if cleaned_lines else ""
            
            print(f"   Výsledok: '{cleaned_text}'")
            
            # Kontrola či sa opravilo "I"
            if test_text.startswith(('|', 'l', '1')) and cleaned_text.startswith('I'):
                print(f"   ✅ OCR oprava úspešná")
                success_count += 1
            elif not test_text.startswith(('|', 'l', '1')):
                print(f"   ✅ Text bez chyby zostal nezmenený")
                success_count += 1
            else:
                print(f"   ❌ OCR oprava zlyhala")
            
            total_count += 1
        
        print(f"\n📊 Výsledok: {success_count}/{total_count} testov úspešných")
        return success_count == total_count
        
    finally:
        # Žiadne čistenie nie je potrebné
        pass

def test_language_detection():
    """Test detekcie jazyka a aplikácie opráv."""
    print("\n🧪 Test 4: Detekcia jazyka a aplikácia opráv")
    print("=" * 60)
    
    test_text = "| merely say that this is a test."
    
    languages_to_test = [
        ("en", True, "Angličtina - má sa opraviť"),
        ("eng", True, "Angličtina (eng) - má sa opraviť"),
        ("english", True, "Angličtina (english) - má sa opraviť"),
        ("sk", False, "Slovenčina - nemá sa opraviť"),
        ("cs", False, "Čeština - nemá sa opraviť"),
        ("de", False, "Nemčina - nemá sa opraviť"),
        (None, False, "Žiadny jazyk - nemá sa opraviť"),
    ]
    
    success_count = 0
    total_count = 0
    
    for lang, should_correct, description in languages_to_test:
        corrected = correct_ocr_text(test_text, lang)
        was_corrected = corrected != test_text
        
        if should_correct == was_corrected:
            status = "✅"
            success_count += 1
        else:
            status = "❌"
        
        print(f"   {status} {description}: '{corrected}'")
        total_count += 1
    
    print(f"\n📊 Výsledok: {success_count}/{total_count} testov úspešných")
    return success_count == total_count

def test_real_world_cases():
    """Test reálnych prípadov z logu."""
    print("\n🧪 Test 5: Reálne prípady z app.log")
    print("=" * 60)
    
    real_cases = [
        # Prípad z logu
        {
            "original": "| merely say\nthat the cause lies here at the clinic.",
            "expected_single": "I merely say that the cause lies here at the clinic.",
            "description": "Viacriadkový text s chýbajúcim I"
        },
        {
            "original": "| heard that you failed a medical student.",
            "expected_single": "I heard that you failed a medical student.",
            "description": "Jednoriadkový text s chýbajúcim I"
        },
        {
            "original": "but you also claim that | am responsible?",
            "expected_single": "but you also claim that I am responsible?",
            "description": "I v strede vety"
        }
    ]
    
    success_count = 0
    total_count = 0
    
    for case in real_cases:
        print(f"\n📝 {case['description']}:")
        print(f"   Pôvodný: '{case['original']}'")
        
        # Test OCR korektora
        corrected = correct_ocr_text(case['original'], "en")
        print(f"   Opravený: '{corrected}'")
        
        # Kontrola výsledku
        if 'expected_single' in case:
            # Pre jednoriadkový výsledok
            corrected_single = corrected.replace('\n', ' ')
            expected = case['expected_single']
            
            if corrected_single == expected or corrected.startswith('I'):
                print(f"   ✅ Oprava úspešná")
                success_count += 1
            else:
                print(f"   ❌ Oprava neúspešná")
                print(f"   Očakávané: '{expected}'")
        else:
            # Základná kontrola či sa opravilo I
            if corrected.startswith('I') or ' I ' in corrected:
                print(f"   ✅ I bolo opravené")
                success_count += 1
            else:
                print(f"   ❌ I nebolo opravené")
        
        total_count += 1
    
    print(f"\n📊 Výsledok: {success_count}/{total_count} testov úspešných")
    return success_count == total_count

def main():
    """Hlavná test funkcia."""
    print("🚀 Test OCR Text Corrector - Oprava častých OCR chýb")
    print("=" * 80)
    
    # Spustenie všetkých testov
    tests = [
        ("Základné funkcie", test_ocr_corrector_basic),
        ("Chýbajúce I", test_missing_I_corrector),
        ("Integrácia s common_utils", test_integration_with_common_utils),
        ("Detekcia jazyka", test_language_detection),
        ("Reálne prípady", test_real_world_cases),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                print(f"✅ {test_name}: ÚSPECH")
                passed_tests += 1
            else:
                print(f"❌ {test_name}: ZLYHANIE")
        except Exception as e:
            print(f"💥 {test_name}: CHYBA - {e}")
    
    # Celkové hodnotenie
    print(f"\n{'='*80}")
    print(f"🎯 CELKOVÉ HODNOTENIE: {passed_tests}/{total_tests} testov úspešných")
    
    if passed_tests == total_tests:
        print("🎉 Všetky testy prešli! OCR Text Corrector funguje správne.")
        print("\n💡 Korektor je aktívny len pre angličtinu (en/eng/english)")
        print("💡 Opravuje časté OCR chyby ako | -> I, l -> I, 1 -> I")
        print("💡 Funguje v kontexte s common_utils.clean_text()")
    else:
        print("⚠️ Niektoré testy zlyhali. Skontrolujte implementáciu.")
    
    return passed_tests == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
