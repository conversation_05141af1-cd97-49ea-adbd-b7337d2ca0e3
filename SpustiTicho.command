#!/bin/bash

# 🚀 Tichý spúšťač Subtitle Reader - bez terminálu
# Spustí aplikáciu a okamžite zatvorí terminál

# Prejdi do adresára aplikácie
cd "$( dirname "${BASH_SOURCE[0]}" )"

# Nastav pyenv ak existuje
export PATH="$HOME/.pyenv/bin:$PATH"
if command -v pyenv 1>/dev/null 2>&1; then
    eval "$(pyenv init -)"
    eval "$(pyenv virtualenv-init -)"
    # Aktivuj reader-env ak existuje
    if pyenv versions | grep -q "reader-env"; then
        pyenv shell reader-env 2>/dev/null
    fi
fi

# Aktivuj virtual environment ak existuje
[ -d "venv" ] && source venv/bin/activate
[ -d ".venv" ] && source .venv/bin/activate

# Spusti aplikáciu na pozadí
if command -v python3 &> /dev/null; then
    nohup python3 main_qt.py > /dev/null 2>&1 &
else
    nohup python main_qt.py > /dev/null 2>&1 &
fi

# Zatvor terminál okamžite
osascript -e 'tell application "Terminal" to close first window' > /dev/null 2>&1 &

exit 0
