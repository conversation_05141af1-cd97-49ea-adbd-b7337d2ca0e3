#!/usr/bin/env python3
"""
Script na vytvorenie kompletného inštalačného balíčka pre x86 Windows
"""

import os
import shutil
import zipfile
import sys
from pathlib import Path

def create_x86_package():
    """Vytvorí kompletný inštalačný balíček pre x86 Windows"""
    
    print("📦 Vytváram x86 inštalačný balíček...")
    print("=" * 50)
    
    # Názov balíčka
    package_name = "subtitle-reader-x86"
    package_dir = Path(package_name)
    
    # Vymaž existujúci adresár
    if package_dir.exists():
        print(f"🗑️ Odstraňujem existujúci adresár: {package_dir}")
        shutil.rmtree(package_dir)
    
    # Vytvor nový adresár
    package_dir.mkdir()
    print(f"📁 Vytvorený adresár: {package_dir}")
    
    # Zoznam súborov na skopírovanie
    files_to_copy = [
        # Inštalačné scripty
        "install_x86.bat",
        "run_app_x86.bat", 
        "test_x86_installation.py",
        "requirements_x86.txt",
        "README_X86_INSTALLATION.md",
        
        # Hlavná aplikácia
        "main_qt.py",
        "qt_gui.py",
        "common_config.py",
        "common_utils.py",
        
        # Platform-specific
        "platform_utils.py",
        "platform_windows.py",
        "onecore_tts_provider.py",
        
        # Core functionality
        "ocr_core.py",
        "tts_manager.py",
        "screenshot_manager.py",
        "hotkey_manager.py",
        
        # Language support
        "language_detector.py",
        "i18n_manager.py",
        "translation_manager.py",
        
        # Mode implementations
        "static_mode/static_logic.py",
        "dynamic_mode/dynamic_logic.py",
        "full_automatic_mode/full_automatic_logic.py",
        "full_automatic_mode/full_auto_stability_detector.py",
        
        # Utilities
        "settings_manager.py",
        "csv_logger.py",
        "audio_manager.py",
        
        # Translations
        "translations/cs.json",
        "translations/sk.json", 
        "translations/en.json",
        "translations/de.json",
        "translations/fr.json",
    ]
    
    # Skopíruj súbory
    copied_count = 0
    missing_count = 0
    
    for file_path in files_to_copy:
        source = Path(file_path)
        if source.exists():
            # Vytvor adresárovú štruktúru ak je potrebná
            target = package_dir / file_path
            target.parent.mkdir(parents=True, exist_ok=True)
            
            # Skopíruj súbor
            shutil.copy2(source, target)
            print(f"   ✅ {file_path}")
            copied_count += 1
        else:
            print(f"   ⚠️ {file_path} - CHÝBA")
            missing_count += 1
    
    # Skopíruj všetky .py súbory z root adresára
    print(f"\n📁 Kopírujem dodatočné .py súbory...")
    for py_file in Path(".").glob("*.py"):
        if py_file.name not in [f.split("/")[-1] for f in files_to_copy if f.endswith(".py")]:
            target = package_dir / py_file.name
            if not target.exists():
                shutil.copy2(py_file, target)
                print(f"   ✅ {py_file.name}")
                copied_count += 1
    
    # Vytvor prázdne adresáre pre módy ak neexistujú
    mode_dirs = [
        "static_mode",
        "dynamic_mode", 
        "full_automatic_mode",
        "translations"
    ]
    
    for mode_dir in mode_dirs:
        target_dir = package_dir / mode_dir
        if not target_dir.exists():
            target_dir.mkdir(parents=True)
            print(f"   📁 Vytvorený adresár: {mode_dir}")
            
            # Vytvor prázdny __init__.py
            init_file = target_dir / "__init__.py"
            init_file.write_text("# Mode module\n")
    
    # Vytvor ZIP balíček
    zip_name = f"{package_name}.zip"
    print(f"\n📦 Vytváram ZIP balíček: {zip_name}")
    
    with zipfile.ZipFile(zip_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(package_dir):
            for file in files:
                file_path = Path(root) / file
                arc_name = file_path.relative_to(package_dir.parent)
                zipf.write(file_path, arc_name)
                
    zip_size = Path(zip_name).stat().st_size / (1024 * 1024)  # MB
    
    print(f"\n" + "=" * 50)
    print(f"🎉 BALÍČEK VYTVORENÝ ÚSPEŠNE!")
    print(f"=" * 50)
    print(f"📦 Názov: {zip_name}")
    print(f"📏 Veľkosť: {zip_size:.1f} MB")
    print(f"📁 Súbory: {copied_count} skopírovaných")
    if missing_count > 0:
        print(f"⚠️ Chýbajúce: {missing_count} súborov")
    
    print(f"\n📋 INŠTRUKCIE PRE POUŽÍVATEĽA:")
    print(f"1. Skopírujte {zip_name} na x86 Windows počítač")
    print(f"2. Rozbaľte ZIP súbor")
    print(f"3. Spustite install_x86.bat ako správca")
    print(f"4. Aplikácia sa automaticky nainštaluje a spustí")
    
    print(f"\n💡 Balíček obsahuje:")
    print(f"   🔧 Automatický inštalátor (Python, Tesseract, závislosti)")
    print(f"   🚀 Spúšťací script")
    print(f"   🧪 Test script")
    print(f"   📖 Podrobnú dokumentáciu")
    print(f"   🖥️ Kompletnú aplikáciu")
    
    return zip_name

def main():
    """Hlavná funkcia"""
    print("🚀 Subtitle Reader - x86 Package Creator")
    print("=" * 50)
    
    try:
        zip_name = create_x86_package()
        
        print(f"\n✅ Balíček {zip_name} je pripravený na distribúciu!")
        
        # Zobraz veľkosť súborov
        if Path(zip_name).exists():
            size_mb = Path(zip_name).stat().st_size / (1024 * 1024)
            print(f"📊 Finálna veľkosť: {size_mb:.1f} MB")
        
    except Exception as e:
        print(f"❌ Chyba pri vytváraní balíčka: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
