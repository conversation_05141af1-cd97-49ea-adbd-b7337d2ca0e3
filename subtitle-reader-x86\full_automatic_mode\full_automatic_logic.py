import logging
import threading
import queue
import time
from collections import deque
import os
import glob

import common_config as config
import common_utils
from tts_manager import speak_text
import csv_logger

def reset_full_automatic_detection_state():
    """
    Centrálna funkcia na resetovanie všetkých premenných full automatic režimu.
    Volá sa pri každom spustení full automatic režimu.
    """
    # Nové robustné premenné
    config.full_auto_detection_history.clear()
    config.full_auto_last_analyzed_text = ""
    config.full_auto_last_detected_mode = None
    config.full_auto_mode_stable_since = None
    config.full_auto_current_sub_mode = 'static'  # Začíname so statickým režimom
    config.full_auto_first_detection_done = False  # Resetujeme stav prvej detekcie

    # Nastavíme počiatočné prahové hodnoty (rýchla detekcia)
    config.full_auto_detection_thresholds = {
        'STATIC': config.FULL_AUTO_INITIAL_STATIC_THRESHOLD,
        'DYNAMIC': config.FULL_AUTO_INITIAL_DYNAMIC_THRESHOLD,
        'MIN_SAMPLES': config.FULL_AUTO_INITIAL_MIN_SAMPLES
    }

    logging.info(f"[FULL_AUTO] Počiatočné nastavenia: STATIC={config.FULL_AUTO_INITIAL_STATIC_THRESHOLD:.0%}, DYNAMIC={config.FULL_AUTO_INITIAL_DYNAMIC_THRESHOLD:.0%}, MIN_SAMPLES={config.FULL_AUTO_INITIAL_MIN_SAMPLES} (rýchla detekcia)")

    # Staré premenné (zachované pre kompatibilitu)
    config.full_auto_detection_text_history.clear()
    config.full_auto_consecutive_static_count = 0
    config.full_auto_current_base_text = ""

    # Vyčistenie fronty full automatic detekcie
    while not config.full_automatic_detection_queue.empty():
        try:
            config.full_automatic_detection_queue.get_nowait()
        except queue.Empty:
            break

    logging.info("[FULL_AUTO] Všetky premenné full automatic režimu resetované (robustný algoritmus).")

# Import pre delegovanie na statický/dynamický režim
import static_mode.static_logic
import dynamic_mode.dynamic_logic

def is_text_growing_dynamically(new_text, base_text):
    """
    Detekuje, či nový text je dynamickým rastom základného textu.
    Vráti True ak nový text obsahuje základný text + nové slová na konci.
    """
    if not base_text or not new_text:
        return False

    # Normalizujeme texty (odstránime extra medzery)
    base_normalized = " ".join(base_text.split())
    new_normalized = " ".join(new_text.split())

    # Ak je nový text kratší, nie je to rast
    if len(new_normalized) <= len(base_normalized):
        return False

    # Ak nový text začína základným textom, je to dynamický rast
    if new_normalized.startswith(base_normalized):
        added_part = new_normalized[len(base_normalized):].strip()
        if added_part:  # Niečo sa pridalo
            logging.debug(f"[AUTO_DETECT] Dynamický rast detekovaný: '{base_normalized}' → '{new_normalized}'")
            return True

    return False

import csv

def initialize_csv_log():
    """Initialize the CSV log file with a header."""
    if config.CSV_LOGGING_ENABLED:
        with open(config.CSV_LOG_FILE, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(["Timestamp", "CycleID", "ChangeType", "Similarity", "PreviousText", "NextText", "ModeChange", "TextToRead"])

def log_text_change_to_csv(cycle_id, change_type, similarity, prev_text, next_text, text_to_read=""):
    """Log the text change to a CSV file using new CSV logger."""
    csv_logger.log_text_comparison(cycle_id, prev_text, next_text, similarity, change_type)
    if text_to_read:
        csv_logger.log_tts_event(cycle_id, text_to_read, "TTS_SENT")

def log_mode_change_to_csv(cycle_id, old_mode, new_mode):
    """Log the mode change to a CSV file using new CSV logger."""
    csv_logger.log_mode_change(old_mode, new_mode, details=f"Full Auto: {old_mode} -> {new_mode}")


def detect_dynamic_subtitle_growth(last_text, new_text):
    """
    Detekuje dynamický rast titulkov na základe skutočného správania.

    DYNAMICKÉ TITULKY:
    1. Jednoriadkové: "slovo" → "slovo druhé" → "slovo druhé tretie"
    2. Dvojriadkové: prvý riadok stabilný, druhý rastie
    3. Začiatok vždy rovnaký, pridávajú sa slová na koniec
    """
    if not last_text or not new_text:
        return False

    # === JEDNORIADKOVÉ DYNAMICKÉ TITULKY ===
    if '\n' not in last_text and '\n' not in new_text:
        # Kontrola, či nový text začína starým textom a je dlhší
        if new_text.startswith(last_text.strip()):
            added_part = new_text[len(last_text.strip()):].strip()
            if added_part:  # Niečo sa pridalo
                # Kontrola, že sa pridali len slová (nie náhodné znaky)
                added_words = added_part.split()
                if (len(added_words) >= 1 and
                    len(added_words) <= config.DYNAMIC_MAX_WORDS_PER_CYCLE and
                    all(len(word) >= config.DYNAMIC_MIN_WORD_LENGTH for word in added_words)):
                    logging.debug(f"[DYNAMIC_DETECT] Jednoriadkový rast: '{last_text}' → '{new_text}' (pridané: '{added_part}')")
                    return True

    # === DVOJRIADKOVÉ DYNAMICKÉ TITULKY ===
    elif '\n' in last_text and '\n' in new_text:
        last_lines = [line.strip() for line in last_text.split('\n') if line.strip()]
        new_lines = [line.strip() for line in new_text.split('\n') if line.strip()]

        if len(last_lines) >= 2 and len(new_lines) >= 2:
            # Scenár 1: Prvý riadok rovnaký, druhý riadok rastie
            if (last_lines[0] == new_lines[0] and
                new_lines[1].startswith(last_lines[1]) and
                len(new_lines[1]) > len(last_lines[1])):

                added_part = new_lines[1][len(last_lines[1]):].strip()
                if added_part:
                    logging.debug(f"[DYNAMIC_DETECT] Dvojriadkový rast: 1.'{new_lines[0]}' + 2.'{last_lines[1]}' → '{new_lines[1]}' (pridané: '{added_part}')")
                    return True

            # Scenár 2: Druhý riadok sa stal prvým, nový druhý riadok začína
            elif (len(last_lines) >= 2 and len(new_lines) >= 2 and
                  last_lines[1] == new_lines[0] and
                  len(new_lines[1]) > 0):

                logging.debug(f"[DYNAMIC_DETECT] Posun riadkov: '{last_lines[1]}' sa stal prvým, nový druhý: '{new_lines[1]}'")
                return True

    # === KONTROLA POSTUPNÉHO RASTU SLOV ===
    last_words = last_text.split()
    new_words = new_text.split()

    if (len(new_words) > len(last_words) and
        len(new_words) - len(last_words) <= config.DYNAMIC_MAX_WORDS_PER_CYCLE and  # Konfigurovateľný limit
        new_words[:len(last_words)] == last_words):  # Začiatok rovnaký

        added_words = new_words[len(last_words):]
        logging.debug(f"[DYNAMIC_DETECT] Postupný rast slov: pridané {added_words}")
        return True

    return False

def detect_static_subtitle_behavior(last_text, new_text, similarity):
    """
    Detekuje statické správanie titulkov.

    STATICKÉ TITULKY:
    1. Text je od začiatku do konca nemenný (STABLE)
    2. Úplná výmena na iný text (REPLACE)
    3. Žiadny postupný rast slov
    """
    if not last_text or not new_text:
        return None

    # Vysoká podobnosť = stabilný statický text
    if similarity >= config.STATIC_STABILITY_THRESHOLD:
        logging.debug(f"[STATIC_DETECT] Stabilný text: podobnosť {similarity:.2f}")
        return 'STABLE'

    # Kontrola úplnej výmeny (typické pre statické titulky)
    last_words = set(last_text.lower().split())
    new_words = set(new_text.lower().split())
    common_words = last_words.intersection(new_words)

    # Ak majú menej ako konfigurovateľný prah spoločných slov = úplná výmena
    word_overlap_ratio = len(common_words) / max(len(last_words), len(new_words), 1)
    if word_overlap_ratio < config.STATIC_REPLACEMENT_THRESHOLD:
        logging.debug(f"[STATIC_DETECT] Úplná výmena: spoločné slová {word_overlap_ratio:.2f}")
        return 'REPLACE'

    # Stredná podobnosť môže byť OCR chyba v statickom texte
    if similarity >= config.STATIC_OCR_ERROR_THRESHOLD:
        logging.debug(f"[STATIC_DETECT] Pravdepodobne stabilný (OCR chyba): podobnosť {similarity:.2f}")
        return 'STABLE'

    # Nízka podobnosť = výmena
    logging.debug(f"[STATIC_DETECT] Výmena textu: podobnosť {similarity:.2f}")
    return 'REPLACE'

def analyze_text_change(new_text, cycle_id):
    """
    NOVÝ DETEKČNÝ ALGORITMUS založený na skutočnom správaní titulkov.

    STATICKÉ TITULKY:
    - Text je od začiatku do konca nemenný (STABLE)
    - Zmení sa na úplne iný text (REPLACE)
    - Zmizne a objaví sa nový (CLEAR → nový text)

    DYNAMICKÉ TITULKY:
    - Text začína malý a postupne rastie (INCREMENTAL)
    - Začiatok zostáva rovnaký, pridávajú sa slová na koniec
    - Dvojriadkový systém: prvý riadok sa "zamrazí", druhý rastie

    Vráti typ zmeny: 'INCREMENTAL', 'REPLACE', 'CLEAR', 'STABLE' alebo None
    """
    if not new_text:
        new_text = ""

    last_text = config.full_auto_last_analyzed_text

    # Ak je to prvý text, uložíme ho a pokračujeme
    if not last_text:
        config.full_auto_last_analyzed_text = new_text
        log_text_change_to_csv(cycle_id, 'INITIAL', 1.0, ' ', new_text)
        return None

    # Ak sú oba texty prázdne, ignorujeme
    if not new_text and not last_text:
        log_text_change_to_csv(cycle_id, 'EMPTY_STABLE', 1.0, last_text, new_text)
        return None

    # CLEAR: Bol text, teraz nie je (silný indikátor statických titulkov)
    if last_text and not new_text:
        config.full_auto_last_analyzed_text = new_text
        logging.info(f"[FULL_AUTO] CLEAR: '{last_text}' → '' (silný indikátor statických titulkov)")
        log_text_change_to_csv(cycle_id, 'CLEAR', 0.0, last_text, new_text)
        return 'CLEAR'

    # Ak nebol text a teraz je, považujeme za REPLACE
    if not last_text and new_text:
        config.full_auto_last_analyzed_text = new_text
        logging.debug(f"[FULL_AUTO] REPLACE: '' → '{new_text}'")
        log_text_change_to_csv(cycle_id, 'REPLACE', 0.0, last_text, new_text)
        return 'REPLACE'

    # Porovnanie podobnosti
    similarity = common_utils.calculate_similarity(last_text, new_text)
    logging.debug(f"[FULL_AUTO] Analýza: '{last_text}' → '{new_text}' (podobnosť: {similarity:.3f})")

    # STABLE: Texty sú takmer identické
    if similarity > config.FULL_AUTO_STABLE_THRESHOLD:
        # Neaktualizujeme last_analyzed_text, lebo je to rovnaký text
        logging.debug(f"[FULL_AUTO] STABLE: podobnosť {similarity:.2f}")
        log_text_change_to_csv(cycle_id, 'STABLE', similarity, last_text, new_text)
        return 'STABLE'

    # === NOVÁ DETEKCIA DYNAMICKÝCH TITULKOV ===
    is_incremental = detect_dynamic_subtitle_growth(last_text, new_text)

    if is_incremental:
        config.full_auto_last_analyzed_text = new_text
        log_text_change_to_csv(cycle_id, 'INCREMENTAL', similarity, last_text, new_text)
        return 'INCREMENTAL'

    if is_incremental:
        config.full_auto_last_analyzed_text = new_text
        csv_logger.log_text_comparison(cycle_id, last_text, new_text, similarity, 'INCREMENTAL', f"Full Auto - INCREMENTAL detekcia")
        return 'INCREMENTAL'

    # === NOVÁ DETEKCIA STATICKÝCH TITULKOV ===
    static_behavior = detect_static_subtitle_behavior(last_text, new_text, similarity)

    if static_behavior:
        config.full_auto_last_analyzed_text = new_text
        logging.debug(f"[FULL_AUTO] {static_behavior}: '{last_text}' → '{new_text}' (podobnosť: {similarity:.2f})")
        log_text_change_to_csv(cycle_id, static_behavior, similarity, last_text, new_text)
        return static_behavior

    # Fallback - ak sa nič nedetekuje, považujeme za STABLE
    config.full_auto_last_analyzed_text = new_text
    logging.debug(f"[FULL_AUTO] STABLE (fallback): podobnosť {similarity:.2f}")
    log_text_change_to_csv(cycle_id, 'STABLE', similarity, last_text, new_text)
    return 'STABLE'


def decide_mode():
    """
    Rozhodovací mechanizmus na základe histórie zmien.
    Vráti: 'static', 'dynamic' alebo None
    """
    history = config.full_auto_detection_history
    thresholds = config.full_auto_detection_thresholds

    # Kontrola dostatok dát
    if len(history) < thresholds['MIN_SAMPLES']:
        logging.debug(f"[FULL_AUTO] Nedostatok vzoriek: {len(history)}/{thresholds['MIN_SAMPLES']}")
        return None

    # Počítanie typov zmien
    incremental_count = history.count('INCREMENTAL')
    static_count = history.count('REPLACE') + history.count('CLEAR')
    total_relevant = incremental_count + static_count

    if total_relevant == 0:
        logging.debug("[FULL_AUTO] Žiadne relevantné zmeny v histórii")
        return None

    # Výpočet percent
    incremental_percent = incremental_count / total_relevant
    static_percent = static_count / total_relevant

    logging.debug(f"[FULL_AUTO] Štatistika: INCREMENTAL={incremental_percent:.1%}, STATIC={static_percent:.1%} (z {total_relevant} relevantných)")

    # Logovanie detekčných štatistík do CSV
    detection_stats = {
        'incremental_percent': incremental_percent,
        'static_percent': static_percent,
        'total_samples': len(history),
        'total_relevant': total_relevant
    }

    # Rozhodovanie s hysteréziou pre stabilnejšie prepínanie
    current_mode = config.full_auto_current_sub_mode
    hysteresis = config.FULL_AUTO_MODE_SWITCH_HYSTERESIS

    # Ak ešte nebola vykonaná prvá detekcia, používame základné prahy (bez hysterézy)
    if not config.full_auto_first_detection_done:
        if incremental_percent >= thresholds['DYNAMIC']:
            logging.info(f"[FULL_AUTO] PRVÁ DETEKCIA - DYNAMICKÝ režim: {incremental_percent:.1%} INCREMENTAL zmien")
            csv_logger.log_full_auto_detection('', 'dynamic', detection_stats, "Prvá detekcia - dynamický režim")
            switch_to_stable_settings()
            return 'dynamic'
        elif static_percent >= thresholds['STATIC']:
            logging.info(f"[FULL_AUTO] PRVÁ DETEKCIA - STATICKÝ režim: {static_percent:.1%} STATIC zmien")
            csv_logger.log_full_auto_detection('', 'static', detection_stats, "Prvá detekcia - statický režim")
            switch_to_stable_settings()
            return 'static'
        else:
            logging.debug(f"[FULL_AUTO] Prvá detekcia - nedostatok jasných signálov")
            csv_logger.log_full_auto_detection('', None, detection_stats, "Prvá detekcia - nedostatok signálov")
            return None

    # Po prvej detekcii používame hysterézu
    # Ak už sme v dynamickom režime, potrebujeme vyšší prah pre prepnutie na statický
    if current_mode == 'dynamic':
        if static_percent >= (thresholds['STATIC'] + hysteresis):
            logging.info(f"[FULL_AUTO] Detekovaný STATICKÝ režim: {static_percent:.1%} STATIC zmien (s hysteréziou)")
            csv_logger.log_full_auto_detection('', 'static', detection_stats, f"Prepnutie z dynamic na static (hysteréza)")
            return 'static'
        elif incremental_percent >= thresholds['DYNAMIC']:
            logging.debug(f"[FULL_AUTO] Zostávam v DYNAMICKOM režime: {incremental_percent:.1%} INCREMENTAL zmien")
            csv_logger.log_full_auto_detection('', 'dynamic', detection_stats, "Zostávam v dynamickom režime")
            return 'dynamic'

    # Ak už sme v statickom režime, potrebujeme vyšší prah pre prepnutie na dynamický
    elif current_mode == 'static':
        if incremental_percent >= (thresholds['DYNAMIC'] + hysteresis):
            logging.info(f"[FULL_AUTO] Detekovaný DYNAMICKÝ režim: {incremental_percent:.1%} INCREMENTAL zmien (s hysteréziou)")
            csv_logger.log_full_auto_detection('', 'dynamic', detection_stats, f"Prepnutie z static na dynamic (hysteréza)")
            return 'dynamic'
        elif static_percent >= thresholds['STATIC']:
            logging.debug(f"[FULL_AUTO] Zostávam v STATICKOM režime: {static_percent:.1%} STATIC zmien")
            csv_logger.log_full_auto_detection('', 'static', detection_stats, "Zostávam v statickom režime")
            return 'static'

    logging.debug(f"[FULL_AUTO] Nejednoznačný stav - žiadna zmena režimu")
    csv_logger.log_full_auto_detection('', None, detection_stats, "Nejednoznačný stav")
    return None

def switch_to_stable_settings():
    """
    Prepne z počiatočných na stabilné nastavenia detekcie po prvej úspešnej detekcii.
    """
    config.full_auto_first_detection_done = True
    config.full_auto_detection_thresholds = {
        'STATIC': config.FULL_AUTO_STABLE_STATIC_THRESHOLD,
        'DYNAMIC': config.FULL_AUTO_STABLE_DYNAMIC_THRESHOLD,
        'MIN_SAMPLES': config.FULL_AUTO_STABLE_MIN_SAMPLES
    }
    logging.info(f"[FULL_AUTO] Prepnuté na stabilné nastavenia: STATIC={config.FULL_AUTO_STABLE_STATIC_THRESHOLD:.0%}, DYNAMIC={config.FULL_AUTO_STABLE_DYNAMIC_THRESHOLD:.0%}, MIN_SAMPLES={config.FULL_AUTO_STABLE_MIN_SAMPLES}, HYSTERÉZA={config.FULL_AUTO_MODE_SWITCH_HYSTERESIS:.0%}")

def analyze_text_for_full_auto_detection(current_text, cycle_id):
    """
    Hlavná funkcia pre robustnú detekciu režimu.
    Vráti: ('static', 'dynamic', alebo None)
    """
    # Analyzujeme zmenu textu
    change_type = analyze_text_change(current_text, cycle_id)

    # Ak je zmena relevantná, pridáme ju do histórie
    if change_type and change_type != 'STABLE':
        config.full_auto_detection_history.append(change_type)
        logging.debug(f"[FULL_AUTO] Pridaná zmena do histórie: {change_type} (história: {len(config.full_auto_detection_history)}/20)")

    # Rozhodneme o režime na základe histórie
    return decide_mode()

def process_text(raw_ocr_data):
    """
    Centrálna funkcia pre full automatic režim.
    1. Pošle text na analýzu do detekčného cyklu
    2. Na základe aktuálneho pod-režimu deleguje spracovanie na statický/dynamický režim
    3. Pred prvou detekciou režimu nečíta text
    """
    raw_text = raw_ocr_data.get('text', '')
    cycle_id = raw_ocr_data.get('cycle_id', -1)

    # Vyčistenie textu pre správne spracovanie
    text = common_utils.clean_text_to_string(raw_text)

    logging.info(f"[FULL_AUTO] *** PROCESS_TEXT VOLANÁ *** text='{text}', cycle_id={cycle_id}")
    logging.debug(f"[FULL_AUTO] process_text volaná s textom: '{text}' (pôvodný: '{raw_text}'), CycleID={cycle_id}, pod-režim: {config.full_auto_current_sub_mode}")

    # OPRAVA: Aj pri prázdnom texte musíme volať dynamický režim
    # Dôvod: Dynamický režim obsahuje logiku pre čítanie druhého riadku pri zmiznutí textu
    if not text:
        logging.debug(f"[FULL_AUTO] Prázdny text po vyčistení, ale volám dynamický režim pre logiku zmiznutia textu")
        # Stále voláme dynamický režim s prázdnym textom
        try:
            logging.info(f"[FULL_AUTO] Volám handle_text_comparison_dynamic s prázdnym textom")
            import dynamic_mode.dynamic_logic
            dynamic_mode.dynamic_logic.handle_text_comparison_dynamic(raw_text, cycle_id)
        except Exception as e:
            logging.error(f"[FULL_AUTO] Chyba pri volaní dynamického režimu s prázdnym textom: {e}")
        return

    # NOVÁ LOGIKA: Full auto = dynamický režim + detekcia stability
    logging.info(f"[FULL_AUTO] Začínam spracovanie textu: '{text}', cycle_id={cycle_id}")

    # 1. Pošleme RAW text do dynamického režimu na spracovanie a čítanie
    # DÔLEŽITÉ: Dynamický režim potrebuje RAW text s \n separátormi pre správnu detekciu riadkov!
    try:
        logging.info(f"[FULL_AUTO] Volám handle_text_comparison_dynamic s RAW textom: '{raw_text}'")
        import dynamic_mode.dynamic_logic
        dynamic_mode.dynamic_logic.handle_text_comparison_dynamic(raw_text, cycle_id)
        logging.debug(f"[FULL_AUTO] RAW text poslaný do dynamického režimu pre spracovanie")
    except Exception as e:
        logging.error(f"[FULL_AUTO] Chyba pri posielaní textu do dynamického režimu: {e}")

    # 2. Analýza stability riadkov (len pri dvojriadkových titulkoch)
    try:
        import full_automatic_mode.full_auto_stability_detector as stability_detector
        stability_detector.analyze_two_line_stability()
    except Exception as e:
        logging.error(f"[FULL_AUTO] Chyba pri analýze stability: {e}")

def full_automatic_detection_worker():
    """
    Kontinuálna detekcia typu titulkov pre full automatic režim:
    1. Priebežne analyzuje každý text
    2. Rozhoduje o pod-režime na základe vzorcov
    3. Prepína pod-režim pri detekcii (ale nikdy sa nezastaví)
    """
    logging.info("Full automatic detection worker started.")
    if config.gui_instance:
        config.gui_instance.update_status("Full Automatic: Kontinuálna detekcia aktívna...")

    # Hlavný cyklus kontinuálnej detekcie
    while config.is_reading and config.reading_mode == 'full_automatic':
        try:
            raw_ocr_data = config.full_automatic_detection_queue.get(timeout=1.0)
            text = raw_ocr_data.get('text', '')
            cycle_id = raw_ocr_data.get('cycle_id', -1)
            logging.debug(f"[FULL_AUTO] Prijatý text pre detekciu: '{text}', CycleID={cycle_id}")

            # Vyčistenie textu pre správne porovnávanie
            current_text_line = common_utils.clean_text_to_string(text)

            # Ak je text prázdny po vyčistení, preskočíme ho
            if not current_text_line:
                logging.debug(f"[FULL_AUTO] Prázdny text po vyčistení, preskakujem. Pôvodný: '{text}'")
                config.full_automatic_detection_queue.task_done()
                continue

            # Analyzujeme text pre detekciu režimu
            detected_mode = analyze_text_for_full_auto_detection(current_text_line, cycle_id)

            logging.debug(f"[FULL_AUTO] Detekcia na pôvodnom texte: '{current_text_line}'")

            if detected_mode:
                # Režim bol detekovaný
                current_time = time.time()

                if config.full_auto_last_detected_mode != detected_mode:
                    # Nový pod-režim detekovaný
                    old_mode = config.full_auto_current_sub_mode
                    config.full_auto_last_detected_mode = detected_mode
                    config.full_auto_mode_stable_since = current_time
                    logging.info(f"[FULL_AUTO] Detekovaný nový pod-režim: {detected_mode.upper()}")
                    log_mode_change_to_csv(cycle_id, old_mode, detected_mode)

                    # Okamžite prepneme pod-režim (ale zostávame v full_automatic)
                    if detected_mode == 'dynamic':
                        logging.info("[FULL_AUTO] Prepnutie na DYNAMICKÉ spracovanie.")
                        if config.gui_instance:
                            config.gui_instance.update_status("Full Auto: Prepnuté na dynamické spracovanie")

                        config.full_auto_current_sub_mode = 'dynamic'
                        from i18n_manager import get_tts_message
                        tts_msg = get_tts_message("tts_switched_dynamic")
                        speak_text(tts_msg)
                        log_text_change_to_csv(None, 'TTS_SPEAK', 1.0, '', tts_msg, tts_msg)

                    elif detected_mode == 'static':
                        logging.info("[FULL_AUTO] Prepnutie na STATICKÉ spracovanie.")
                        if config.gui_instance:
                            config.gui_instance.update_status("Full Auto: Prepnuté na statické spracovanie")

                        config.full_auto_current_sub_mode = 'static'
                        from i18n_manager import get_tts_message
                        tts_msg = get_tts_message("tts_switched_static")
                        speak_text(tts_msg)
                        log_text_change_to_csv(None, 'TTS_SPEAK', 1.0, '', tts_msg, tts_msg)

                    # Pokračujeme v detekcii (žiadny break!)

            config.full_automatic_detection_queue.task_done()

        except queue.Empty:
            continue
        except Exception as e:
            logging.error(f"Chyba v full_automatic_detection_worker: {e}")
            time.sleep(0.1)

    logging.info("Full automatic detection worker ukončený.")




def start_full_automatic_detection_processing():
    """
    Spustí full automatic mode = dynamický režim + detekcia stability na pozadí.
    NOVÁ IMPLEMENTÁCIA: Vždy beží dynamický režim, detekcia prebieha počas čítania.
    """
    logging.info("Spúšťam full automatic mode (dynamic + stability detection).")

    # Inicializácia CSV logu
    initialize_csv_log()

    # Vyčistenie stavu pri štarte
    reset_full_automatic_detection_state()

    # Reset detekcie stability
    import full_automatic_mode.full_auto_stability_detector as stability_detector
    stability_detector.reset_stability_detection()

    # Spustenie dynamického režimu (číta hneď)
    import dynamic_mode.dynamic_logic
    dynamic_mode.dynamic_logic.start_dynamic_mode_processing()

    logging.info("Full automatic mode started (dynamic mode + stability detection).")


def stop_full_automatic_detection_processing():
    """
    Zastaví full automatic mode (dynamický režim + detekciu stability).
    """
    logging.info("Zastavujem full automatic mode.")

    # Zastavenie dynamického režimu
    try:
        import dynamic_mode.dynamic_logic
        dynamic_mode.dynamic_logic.stop_dynamic_mode_processing()
    except Exception as e:
        logging.error(f"Chyba pri zastavovaní dynamického režimu: {e}")

    # Resetovanie stavu detekcie stability
    try:
        import full_automatic_mode.full_auto_stability_detector as stability_detector
        stability_detector.reset_stability_detection()
    except Exception as e:
        logging.error(f"Chyba pri resetovaní stability detector: {e}")

    # Resetovanie stavu pri zastavení pre čistý štart pri ďalšom spustení
    reset_full_automatic_detection_state()
    logging.info("Full automatic mode stopped.")