#!/usr/bin/env python3
"""
Cross-platform utility module for Subtitle Reader application.
Provides abstraction layer for platform-specific functionality.

Supports: macOS (Darwin), Windows (win32), Linux
"""

import sys
import platform
import logging
from typing import Optional, Dict, List, Any
from pathlib import Path
from abc import ABC, abstractmethod

# Platform detection constants
IS_MACOS = platform.system() == "Darwin"
IS_WINDOWS = platform.system() == "Windows"
IS_LINUX = platform.system() == "Linux"

PLATFORM_NAME = platform.system()
PLATFORM_VERSION = platform.version()
PLATFORM_MACHINE = platform.machine()

logging.info(f"🔍 Platform detected: {PLATFORM_NAME} {PLATFORM_VERSION} ({PLATFORM_MACHINE})")

class PlatformError(Exception):
    """Exception raised for platform-specific errors."""
    pass

class CrossPlatformBase(ABC):
    """Base class for cross-platform implementations."""
    
    def __init__(self):
        self.platform = PLATFORM_NAME
        self.is_supported = self._check_platform_support()
        if not self.is_supported:
            logging.warning(f"⚠️ Platform {self.platform} may not be fully supported")
    
    @abstractmethod
    def _check_platform_support(self) -> bool:
        """Check if current platform is supported."""
        pass

def get_platform_info() -> Dict[str, str]:
    """Get comprehensive platform information."""
    return {
        "system": platform.system(),
        "release": platform.release(),
        "version": platform.version(),
        "machine": platform.machine(),
        "processor": platform.processor(),
        "python_version": platform.python_version(),
        "is_macos": IS_MACOS,
        "is_windows": IS_WINDOWS,
        "is_linux": IS_LINUX
    }

def get_user_home_dir() -> Path:
    """Get user home directory in cross-platform way."""
    return Path.home()

def get_app_data_dir(app_name: str = "SubtitleReader") -> Path:
    """Get application data directory for current platform."""
    if IS_WINDOWS:
        # Windows: %APPDATA%\SubtitleReader
        return Path.home() / "AppData" / "Roaming" / app_name
    elif IS_MACOS:
        # macOS: ~/Library/Application Support/SubtitleReader
        return Path.home() / "Library" / "Application Support" / app_name
    else:
        # Linux: ~/.config/SubtitleReader
        return Path.home() / ".config" / app_name

def get_temp_dir() -> Path:
    """Get temporary directory for current platform."""
    if IS_WINDOWS:
        import tempfile
        return Path(tempfile.gettempdir())
    else:
        import os
        return Path(os.getenv('TMPDIR', '/tmp'))

def get_documents_dir() -> Path:
    """Get user documents directory."""
    if IS_WINDOWS:
        return Path.home() / "Documents"
    elif IS_MACOS:
        return Path.home() / "Documents"
    else:
        return Path.home() / "Documents"

def ensure_directory_exists(path: Path) -> bool:
    """Ensure directory exists, create if necessary."""
    try:
        path.mkdir(parents=True, exist_ok=True)
        return True
    except Exception as e:
        logging.error(f"❌ Failed to create directory {path}: {e}")
        return False

def get_default_hotkey_modifier() -> str:
    """Get default hotkey modifier for current platform."""
    if IS_MACOS:
        return "cmd"
    else:
        return "ctrl"

def normalize_hotkey_combination(hotkey: str) -> str:
    """Normalize hotkey combination for current platform."""
    if IS_MACOS:
        # Keep cmd for macOS
        return hotkey
    else:
        # Replace cmd with ctrl for Windows/Linux
        return hotkey.replace("<cmd>", "<ctrl>").replace("cmd", "ctrl")

def log_platform_info():
    """Log detailed platform information."""
    info = get_platform_info()
    logging.info("🔍 Platform Information:")
    for key, value in info.items():
        logging.info(f"   {key}: {value}")

# Abstract classes for platform-specific implementations
class TTSProvider(CrossPlatformBase):
    """Abstract base class for TTS providers."""
    
    @abstractmethod
    def speak(self, text: str, voice: str = None, rate: int = 200, volume: float = 1.0) -> bool:
        """Speak text using platform-specific TTS."""
        pass
    
    @abstractmethod
    def stop(self) -> bool:
        """Stop current TTS playback."""
        pass
    
    @abstractmethod
    def get_available_voices(self) -> List[Dict[str, Any]]:
        """Get list of available voices."""
        pass
    
    @abstractmethod
    def is_speaking(self) -> bool:
        """Check if TTS is currently speaking."""
        pass

class ActiveAppDetector(CrossPlatformBase):
    """Abstract base class for active application detection."""
    
    @abstractmethod
    def get_active_app_name(self) -> str:
        """Get name of currently active application."""
        pass
    
    @abstractmethod
    def get_active_window_title(self) -> str:
        """Get title of currently active window."""
        pass

class SystemInfoProvider(CrossPlatformBase):
    """Abstract base class for system information."""
    
    @abstractmethod
    def get_system_language(self) -> str:
        """Get system default language."""
        pass
    
    @abstractmethod
    def get_hardware_fingerprint(self) -> str:
        """Get unique hardware fingerprint."""
        pass

class BrowserURLDetector(CrossPlatformBase):
    """Abstract base class for browser URL detection."""
    
    @abstractmethod
    def get_browser_url(self) -> str:
        """Get current browser URL."""
        pass
    
    @abstractmethod
    def get_youtube_video_time(self, url: str) -> Optional[float]:
        """Get current YouTube video time."""
        pass

def get_tts_provider() -> TTSProvider:
    """Get appropriate TTS provider for current platform."""
    if IS_MACOS:
        from platform_macos import MacOSTTSProvider
        return MacOSTTSProvider()
    elif IS_WINDOWS:
        from platform_windows import WindowsTTSProvider
        return WindowsTTSProvider()
    else:
        from platform_linux import LinuxTTSProvider
        return LinuxTTSProvider()

def get_active_app_detector() -> ActiveAppDetector:
    """Get appropriate active app detector for current platform."""
    if IS_MACOS:
        from platform_macos import MacOSActiveAppDetector
        return MacOSActiveAppDetector()
    elif IS_WINDOWS:
        from platform_windows import WindowsActiveAppDetector
        return WindowsActiveAppDetector()
    else:
        from platform_linux import LinuxActiveAppDetector
        return LinuxActiveAppDetector()

def get_system_info_provider() -> SystemInfoProvider:
    """Get appropriate system info provider for current platform."""
    if IS_MACOS:
        from platform_macos import MacOSSystemInfoProvider
        return MacOSSystemInfoProvider()
    elif IS_WINDOWS:
        from platform_windows import WindowsSystemInfoProvider
        return WindowsSystemInfoProvider()
    else:
        from platform_linux import LinuxSystemInfoProvider
        return LinuxSystemInfoProvider()

def get_browser_url_detector() -> BrowserURLDetector:
    """Get appropriate browser URL detector for current platform."""
    if IS_MACOS:
        from platform_macos import MacOSBrowserURLDetector
        return MacOSBrowserURLDetector()
    elif IS_WINDOWS:
        from platform_windows import WindowsBrowserURLDetector
        return WindowsBrowserURLDetector()
    else:
        from platform_linux import LinuxBrowserURLDetector
        return LinuxBrowserURLDetector()
