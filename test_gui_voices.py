#!/usr/bin/env python3
"""
Test načítania hlasov v GUI aplikácii
"""

import sys
import logging
from PyQt6 import QtWidgets, QtCore
from typing import List, Dict, Any

def setup_logging():
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('gui_voices_test.log', encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )

def test_gui_voice_loading():
    """Test načítania hlasov v GUI"""
    print("🔍 Testovanie načítania hlasov v GUI...")
    
    try:
        # Vytvoríme QApplication
        app = QtWidgets.QApplication(sys.argv)
        
        # Importujeme GUI
        from qt_gui import SubtitleReaderQtGUI
        
        # Vytvoríme GUI inštanciu
        gui = SubtitleReaderQtGUI()
        
        print("✅ GUI vytvorené úspešne")
        
        # Testujeme načítanie hlasov cez TTS manager
        from tts_manager import get_available_voices
        
        voices = get_available_voices()
        print(f"🎤 Nájdených {len(voices)} hlasov cez TTS manager:")
        
        for i, voice in enumerate(voices):
            print(f"   {i+1}. {voice.get('name', 'Unknown')} ({voice.get('language', 'Unknown')}) - {voice.get('quality', 'standard')}")
        
        # Testujeme, či GUI má prístup k hlasom
        if hasattr(gui, 'language_manager'):
            print("✅ GUI má language_manager")
            
            # Skúsime získať hlasy cez language manager
            try:
                # Spustíme detekciu jazykov
                gui.language_manager.detect_languages()
                
                available_langs = gui.language_manager.get_available_languages()
                print(f"🌍 Dostupné jazyky: {available_langs}")
                
                # Test hlasov pre angličtinu
                en_voices = gui.language_manager.get_voices_for_language('en')
                print(f"🎤 Hlasy pre angličtinu: {len(en_voices)}")
                
                for voice in en_voices:
                    print(f"   📢 {voice.get('name', 'Unknown')} ({voice.get('language', 'Unknown')})")
                    
            except Exception as e:
                print(f"❌ Chyba pri testovaní language_manager: {e}")
        else:
            print("❌ GUI nemá language_manager")
        
        # Testujeme combo boxy pre hlasy
        if hasattr(gui, 'reading_voice_combo'):
            print("✅ GUI má reading_voice_combo")
            
            # Získame počet položiek v combo boxe
            count = gui.reading_voice_combo.count()
            print(f"🎤 Počet hlasov v reading_voice_combo: {count}")
            
            # Vypíšeme všetky hlasy v combo boxe
            for i in range(count):
                voice_text = gui.reading_voice_combo.itemText(i)
                voice_data = gui.reading_voice_combo.itemData(i)
                print(f"   {i+1}. {voice_text} (data: {voice_data})")
        else:
            print("❌ GUI nemá reading_voice_combo")
        
        # Testujeme translation voice combo
        if hasattr(gui, 'translation_voice_combo'):
            print("✅ GUI má translation_voice_combo")
            
            count = gui.translation_voice_combo.count()
            print(f"🎤 Počet hlasov v translation_voice_combo: {count}")
            
            for i in range(count):
                voice_text = gui.translation_voice_combo.itemText(i)
                voice_data = gui.translation_voice_combo.itemData(i)
                print(f"   {i+1}. {voice_text} (data: {voice_data})")
        else:
            print("❌ GUI nemá translation_voice_combo")
        
        print("✅ GUI voice test dokončený")
        
        # Zatvoríme aplikáciu
        app.quit()
        
    except Exception as e:
        print(f"❌ GUI voice test zlyhal: {e}")
        import traceback
        traceback.print_exc()

def test_voice_combo_population():
    """Test populácie combo boxov s hlasmi"""
    print("\n" + "="*50)
    print("🔍 Testovanie populácie combo boxov...")
    
    try:
        # Vytvoríme QApplication
        app = QtWidgets.QApplication(sys.argv)
        
        # Vytvoríme jednoduchý combo box test
        combo = QtWidgets.QComboBox()
        
        # Získame hlasy
        from tts_manager import get_available_voices
        voices = get_available_voices()
        
        # Naplníme combo box
        for voice in voices:
            voice_name = voice.get('name', 'Unknown')
            voice_lang = voice.get('language', 'Unknown')
            display_text = f"{voice_name} ({voice_lang})"
            
            combo.addItem(display_text, voice)
        
        print(f"✅ Combo box naplnený s {combo.count()} hlasmi")
        
        # Test výberu hlasu
        if combo.count() > 0:
            combo.setCurrentIndex(0)
            selected_voice = combo.currentData()
            selected_text = combo.currentText()
            
            print(f"🎤 Vybraný hlas: {selected_text}")
            print(f"   📊 Data: {selected_voice}")
        
        app.quit()
        
    except Exception as e:
        print(f"❌ Combo box test zlyhal: {e}")
        import traceback
        traceback.print_exc()

def main():
    setup_logging()
    
    print("🚀 GUI Voices Test")
    print("=" * 60)
    
    # Test načítania hlasov v GUI
    test_gui_voice_loading()
    test_voice_combo_population()
    
    print("\n" + "=" * 60)
    print("🎯 Test dokončený - skontrolujte gui_voices_test.log")

if __name__ == "__main__":
    main()
