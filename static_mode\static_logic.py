import logging
import re

import common_config as config
import common_utils
import csv_logger
from platform_loader import get_tts_function

def speak_text(*args, **kwargs):
    return get_tts_function('speak_text')(*args, **kwargs)

def validate_text_for_reading(text):
    """
    Validu<PERSON>, či je text vhodný na čítanie.
    Filtruje OCR chyby a nezmyselné texty.
    """
    if not text or len(text.strip()) < 3:
        return False

    text = text.strip()

    # Filtre pre nevalidné texty
    invalid_patterns = [
        r'^[0-9\s\-\|]+$',  # Len čísla a špeciálne znaky
        r'^[^\w\s]+$',  # Len špeciálne znaky
        r'[0-9]{5,}',  # Dlhé sekvencie čísel
        r'[\|\-\+]{3,}',  # Opakujúce sa špeciálne znaky
    ]

    # Pridaj filtre pre veľké písmená len ak nie sú povolené
    if not getattr(config, 'ALLOW_UPPERCASE_TEXT', False):
        invalid_patterns.extend([
            r'^[A-Z]{3,}$',  # Len veľké písmená (napr. "WAYMO", "VUZ", "HIC")
            r'[A-Z]{5,}',  # Dlhé sekvencie veľkých písmen
        ])

    # Kontrola nevalidných vzorov
    for pattern in invalid_patterns:
        if re.search(pattern, text):
            logging.debug(f"[VALIDATION] Text '{text}' neprešiel validáciou - vzor: {pattern}")
            return False

    # Kontrola pomeru písmen k špeciálnym znakom
    letters = len(re.findall(r'[a-zA-ZáčďéěíňóřšťúůýžÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ]', text))
    total_chars = len(text.replace(' ', ''))

    if total_chars > 0 and letters / total_chars < 0.6:  # Menej ako 60% písmen
        logging.debug(f"[VALIDATION] Text '{text}' má príliš málo písmen ({letters}/{total_chars})")
        return False

    # Kontrola rozumnej dĺžky
    if len(text) > 200:  # Príliš dlhý text
        logging.debug(f"[VALIDATION] Text '{text}' je príliš dlhý ({len(text)} znakov)")
        return False

    logging.debug(f"[VALIDATION] Text '{text}' prešiel validáciou")
    return True

# Premenné pre statický režim
_last_text_in_que = ""

# Funkcia process_ocr_text_static bola odstránená - text sa teraz spracováva centrálne

def handle_text_comparison_static(new_text, cycle_id: int):
    global _last_text_in_que
    SIMILARITY_STATIC_THRESHOLD = 0.70
    similarity = common_utils.calculate_similarity(_last_text_in_que, new_text)

    # Logovanie porovnania textov
    if similarity < SIMILARITY_STATIC_THRESHOLD:
        change_type = "REPLACE"
        csv_logger.log_text_comparison(cycle_id, _last_text_in_que, new_text, similarity, change_type, "Static mode - nový text")

        cleaned_new_text_list = common_utils.clean_text(new_text)

        # 🔧 MULTILINE FIX: Zachovaj newlines pre správny preklad viacriadkových textov
        if len(cleaned_new_text_list) > 1:
            # Viacriadkový text (2-riadkové, 3-riadkové, atď.) - zachovaj newlines
            cleaned_new_text = "\n".join(cleaned_new_text_list)
            if len(cleaned_new_text_list) == 2:
                logging.debug(f"OCR Vyčistený DVOJRIADKOVÝ text: '{cleaned_new_text}' ({len(cleaned_new_text_list)} riadkov)")
            elif len(cleaned_new_text_list) == 3:
                logging.debug(f"OCR Vyčistený TROJRIADKOVÝ text: '{cleaned_new_text}' ({len(cleaned_new_text_list)} riadkov)")
            else:
                logging.debug(f"OCR Vyčistený MULTILINE text: '{cleaned_new_text}' ({len(cleaned_new_text_list)} riadkov)")
        else:
            # Jednoriadkový text - ako predtým
            cleaned_new_text = " ".join(cleaned_new_text_list)
            logging.debug(f"OCR Vyčistený JEDNORIADKOVÝ text: '{cleaned_new_text}' (Type: {type(cleaned_new_text)})")

        # Validácia textu pred čítaním
        if validate_text_for_reading(cleaned_new_text) and common_utils.is_new_subtitle(cleaned_new_text, config.TTS_HISTORY_SIMILARITY_THRESHOLD):
            logging.info(f"QUEUEING FOR TTS: {cleaned_new_text}")
            config.tts_history.append(cleaned_new_text)
            config.unified_speech_queue.put({'text': cleaned_new_text, 'cycle_id': cycle_id})
            csv_logger.log_tts_event(cycle_id, cleaned_new_text, "TTS_QUEUED", "Static mode - text do fronty")
        elif not validate_text_for_reading(cleaned_new_text):
            csv_logger.log_tts_event(cycle_id, cleaned_new_text, "TTS_SKIPPED", "Static mode - nevalidný text")
        else:
            csv_logger.log_tts_event(cycle_id, cleaned_new_text, "TTS_SKIPPED", "Static mode - duplicitný text")

        _last_text_in_que = new_text
    else:
        change_type = "STABLE"
        csv_logger.log_text_comparison(cycle_id, _last_text_in_que, new_text, similarity, change_type, "Static mode - stabilný text")

def start_static_mode_processing():
    """Reset premenných pre statický režim - spracovanie textu je teraz centrálne."""
    global _last_text_in_que
    _last_text_in_que = ""
    logging.info("Static mode variables reset.")

def stop_static_mode_processing():
    """Reset premenných pre statický režim - spracovanie textu je teraz centrálne."""
    global _last_text_in_que
    _last_text_in_que = ""
    logging.info("Static mode variables reset.")