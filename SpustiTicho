#!/bin/bash

# 🔇 Tichý spúšťač Subtitle Reader - bez dialógov a termin<PERSON>lov
# Tento súbor sa spustí úplne na pozadí a zatvorí terminál

# Získaj adresár aplikácie
APP_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$APP_DIR"

# Nájdi Python
PYTHON_CMD="/Library/Frameworks/Python.framework/Versions/3.10/bin/python3"

if [ ! -f "$PYTHON_CMD" ]; then
    if command -v python3 &> /dev/null; then
        PYTHON_CMD="python3"
    elif command -v python &> /dev/null; then
        PYTHON_CMD="python"
    else
        exit 1
    fi
fi

# Kontrola hlavného súboru
if [ ! -f "main_qt.py" ]; then
    exit 1
fi

# Spusti aplikáciu úplne na pozadí (bez výstupu)
nohup $PYTHON_CMD main_qt.py >/dev/null 2>&1 &

# Zatvor terminálové okno
osascript -e 'tell application "Terminal" to close first window' > /dev/null 2>&1 &

# Ukončenie bez čakania
exit 0
