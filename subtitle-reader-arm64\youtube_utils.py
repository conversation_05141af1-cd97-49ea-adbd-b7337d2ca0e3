import logging
import threading
import time
import subprocess
import re
import datetime
from pathlib import Path
from typing import List, Optional
from urllib.parse import urlparse, parse_qs

import common_config as config
import ocr_processing
from tts_manager import stop_current_tts, speak_text

# --- <PERSON><PERSON> pre YouTube ---

def run_applescript(script: str) -> str:
    """Legacy macOS AppleScript runner."""
    try:
        process = subprocess.run(
            ['/usr/bin/osascript', '-e', script],
            capture_output=True, text=True, check=False, timeout=3
        )
        if process.returncode == 0:
            return process.stdout.strip()
        return ""
    except Exception as e:
        logging.error(f"CHYBA [AppleScript]: Chyba pri spúšťaní: {e}")
        return ""

def get_safari_url() -> str:
    """Legacy macOS Safari URL detection."""
    script = """
    tell application "Safari"
        try
            if (count of windows) > 0 then
                if exists (current tab of window 1) then
                    return URL of current tab of window 1
                end if
            end if
        end try
        return ""
    end tell
    """
    return run_applescript(script)

def get_chrome_url() -> str:
    script = """
    tell application "Google Chrome"
        try
            if (count of windows) > 0 then
                if exists (active tab of window 1) then
                    return URL of active tab of window 1
                end if
            end if
        end try
        return ""
    end tell
    """
    return run_applescript(script)

def get_browser_url() -> str:
    """Get current browser URL cross-platform."""
    try:
        # Try cross-platform implementation first
        from platform_utils import get_browser_url_detector
        detector = get_browser_url_detector()
        url = detector.get_browser_url()
        if url:
            return url
    except ImportError:
        logging.warning("⚠️ Cross-platform browser URL detection not available")
    except Exception as e:
        logging.debug(f"⚠️ Cross-platform browser URL detection failed: {e}")

    # Fallback to legacy macOS implementation
    url = ""
    url = get_safari_url()
    if not url:
        url = get_chrome_url()
    return url

def get_youtube_video_id(url: str) -> Optional[str]:
    """
    Pokúsi sa extrahovať YouTube Video ID z rôznych formátov URL.
    """
    if not url:
        return None
    
    patterns = [
        r'(?:https?://)?(?:www\.)?youtube\.com/watch\?(?:.*&)?v=([^&]+)',
        r'(?:https?://)?(?:www\.)?youtu\.be/([^?&]+)',
        r'(?:https?://)?(?:www\.)?youtube\.com/embed/([^?&]+)',
        r'(?:https?://)?(?:www\.)?youtube\.com/v/([^?&]+)',
    ]
    
    for pattern in patterns:
        match = re.search(pattern, url)
        if match:
            return match.group(1)
            
    if "youtube.com/watch?v=" in url:
        try:
            potential_id = url.split("youtube.com/watch?v=")[1].split("?")[0].split("&")[0]
            if len(potential_id) == 11:
                return potential_id
        except:
            pass

    return None

def get_youtube_video_url_from_tab() -> str:
    return get_browser_url()

def is_youtube_video_page(url: str) -> bool:
    video_id = get_youtube_video_id(url)
    return bool(video_id and ("youtube.com/watch" in url or "youtu.be/" in url))

def is_youtube_shorts_page(url: str) -> bool:
    return "youtube.com/shorts/" in url

def is_youtube_no_video_page_check(url: str) -> bool:
    return ("youtube.com" in url or "youtu.be" in url) and not (is_youtube_video_page(url) or is_youtube_shorts_page(url))

def get_youtube_video_time(url: str) -> Optional[float]:
    try:
        script = """
        tell application "Google Chrome"
            try
                tell active tab of front window
                    if URL contains "youtube.com/watch" or URL contains "youtu.be/" then
                        execute javascript "document.getElementsByTagName('video')[0].currentTime"
                    else
                        return ""
                    end if
                end tell
            on error
                return ""
            try:
        end tell
        """
        chrome_time = run_applescript(script)
        if chrome_time:
            try:
                return float(chrome_time)
            except ValueError:
                pass

        script = """
        tell application "Safari"
            try
                tell current tab of front window
                    if URL contains "youtube.com/watch" or URL contains "youtu.be/" then
                        execute javascript "document.getElementsByTagName('video')[0].currentTime"
                    else:
                        return ""
                    end if
                end tell
            on error
                return ""
            try:
        end tell
        """
        safari_time = run_applescript(script)
        if safari_time:
            try:
                return float(safari_time)
            except ValueError:
                pass
        
        return None
    except Exception as e:
        logging.error(f"Chyba pri získavaní času YouTube videa: {e}")
        return None

def download_youtube_subtitles(url: str, yt_dlp_cmd: str, ffmpeg_cmd: str, lang: str = 'cs', temp_dir: Path = config.TEMP_DIR) -> bool:
    output_template = temp_dir / "%(title)s.%(ext)s"
    args = [
        yt_dlp_cmd, "--skip-download", "--write-auto-subs", "--sub-lang", lang,
        "--convert-subs", "srt", "--output", str(output_template), url
    ]
    if ffmpeg_cmd != 'ffmpeg' and Path(ffmpeg_cmd).exists():
         args.insert(1, "--ffmpeg-location")
         args.insert(2, str(Path(ffmpeg_cmd).parent if Path(ffmpeg_cmd).is_file() else ffmpeg_cmd))
    try:
        process = subprocess.run(args, capture_output=True, text=True, check=False, timeout=60)
        if process.returncode == 0:
            if "subtitles not available" in process.stderr.lower() or \
               "unable to download" in process.stderr.lower():
                logging.warning(f"yt-dlp pravdepodobne nenašiel/nestiahol titulky pre jazyk '{lang}'.")
                return False
            return True
        return False
    except Exception as e:
        logging.error(f"CHYBA: Chyba pri spúšťaní yt-dlp: {e}")
        return False

def find_downloaded_srt(temp_dir: Path = config.TEMP_DIR) -> str | None:
    try:
        for item in temp_dir.glob('*.srt'):
            if item.is_file(): return str(item)
    except Exception as e:
        logging.error(f"Chyba pri hľadaní SRT súboru: {e}")
    return None

def clear_subtitle_files(temp_dir: Path = config.TEMP_DIR):
    for ext in ['*.srt', '*.vtt']:
        for item in temp_dir.glob(ext):
            if item.is_file():
                try: item.unlink()
                except Exception as e: logging.error(f"Chyba pri mazaní titulkového súboru {item}: {e}")

def parse_srt_time(time_str: str) -> float:
    try:
        parts = time_str.replace(',', ':').split(':')
        if len(parts) == 4:
            h, m, s, ms = map(int, parts)
            return h * 3600 + m * 60 + s + ms / 1000.0
    except ValueError as e:
        logging.error(f"Chyba pri parsovaní času SRT: {e}")
    return 0.0

def longest_suffix_prefix_overlap(cumulative: str, new_text: str) -> int:
    max_overlap = min(len(cumulative), len(new_text))
    for length in range(max_overlap, 0, -1):
        if cumulative.endswith(new_text[:length]): return length
    return 0

def parse_srt(srt_path: str) -> List[config.Subtitle]:
    final_subtitles: List[config.Subtitle] = []
    try:
        with open(srt_path, 'r', encoding='utf-8') as f: content = f.read()
    except Exception as e:
        logging.error(f"Chyba pri čítaní SRT súboru {srt_path}: {e}")
        return final_subtitles
    
    blocks = content.strip().split('\n\n')
    cumulative_text = ""
    for block in blocks:
        lines = block.strip().splitlines()
        if len(lines) < 3: continue
        time_line = lines[1].strip()
        time_parts = time_line.split(' --> ')
        if len(time_parts) != 2: continue
        start_time = parse_srt_time(time_parts[0])
        end_time = parse_srt_time(time_parts[1])
        current_text = " ".join([line.strip() for line in lines[2:]])
        cleaned_current_text = ocr_processing.clean_text(current_text)
        if not cleaned_current_text: continue
        overlap = longest_suffix_prefix_overlap(cumulative_text, cleaned_current_text)
        if overlap >= len(cleaned_current_text): continue
        new_part = cleaned_current_text[overlap:]
        if not ocr_processing.clean_text(new_part): continue
        final_subtitles.append(config.Subtitle(start_time, end_time, ocr_processing.clean_text(new_part)))
        if cumulative_text and not ocr_processing.clean_text(new_part).startswith(' '): cumulative_text += " "
        cumulative_text += ocr_processing.clean_text(new_part)
    return final_subtitles

def initialize_youtube_sync():
    if config.subtitles:
        first_sub_time = config.subtitles[0].start_time
        config.video_time = first_sub_time
        config.last_subtitle_index = 0
        config.last_synced_real_time = datetime.datetime.now()
        config.last_synced_video_time = first_sub_time
        config.has_ocr_sync = True
        with config.youtube_tts_queue.mutex: config.youtube_tts_queue.queue.clear()
    else:
        config.has_ocr_sync = False

def handle_youtube_subtitle_ocr(recognized_text: str):
    if not config.subtitles or config.expected_ocr_video_url != config.current_youtube_url: return

    ocr_line = ocr_processing.clean_text(recognized_text.splitlines()[0] if recognized_text else "")
    if not ocr_line: return

    best_match_index, best_match_score = -1, 0.0
    for i, sub in enumerate(config.subtitles):
        similarity = ocr_processing.calculate_similarity(ocr_line, sub.text)
        if similarity > best_match_score:
            best_match_score, best_match_index = similarity, i
    
    SYNC_THRESHOLD, TIME_DIFF_THRESHOLD = 0.80, 5.0

    if best_match_index != -1 and best_match_score >= SYNC_THRESHOLD:
        matched_sub = config.subtitles[best_match_index]
        matched_time = matched_sub.start_time
        time_difference = abs(matched_time - config.video_time)
        if not config.has_ocr_sync or time_difference > TIME_DIFF_THRESHOLD:
            with config.youtube_tts_queue.mutex: config.youtube_tts_queue.queue.clear()
            stop_current_tts()
            config.video_time = matched_time
            config.last_synced_real_time = datetime.datetime.now()
            config.last_synced_video_time = matched_time
            config.last_subtitle_index = best_match_index
            config.has_ocr_sync = True

def update_video_time_if_needed():
    if not config.has_ocr_sync or not config.last_synced_real_time: return
    now = datetime.datetime.now()
    delta_real_time = (now - config.last_synced_real_time).total_seconds()
    new_video_time = config.last_synced_video_time + delta_real_time
    if new_video_time < config.video_time - 1.0:
        recalc_last_subtitle_index(new_video_time)
    config.video_time = new_video_time

def recalc_last_subtitle_index(current_video_time: float):
    if not config.subtitles: config.last_subtitle_index = 0; return
    new_index = 0
    for i, sub in enumerate(config.subtitles):
        if sub.end_time > current_video_time: new_index = i; break
    else: new_index = len(config.subtitles)
    if new_index != config.last_subtitle_index:
        config.last_subtitle_index = new_index
        with config.youtube_tts_queue.mutex: config.youtube_tts_queue.queue.clear()
        stop_current_tts()

def check_and_enqueue_subtitles_for_tts():
    if not config.has_ocr_sync or not config.subtitles: return
    START_BUFFER = 0.5
    for i in range(config.last_subtitle_index, len(config.subtitles)):
        sub = config.subtitles[i]
        if (sub.start_time - START_BUFFER) <= config.video_time < sub.end_time:
            text_to_read = sub.text
            if ocr_processing.calculate_similarity(config.last_enqueued_youtube_text, text_to_read) < 0.95:
                config.youtube_tts_queue.put(text_to_read)
                config.last_enqueued_youtube_text = text_to_read
            config.last_subtitle_index = i + 1
        elif config.video_time >= sub.end_time:
            if i == config.last_subtitle_index: config.last_subtitle_index += 1
        else:
            break

def perform_youtube_ocr_and_sync():
    """
    Vykoná OCR na snímke obrazovky a použije rozpoznaný text na synchronizáciu YouTube videa.
    Táto funkcia je volaná periodicky časovačom, keď je potrebná OCR synchronizácia.
    """
    if not config.is_reading or config.reading_mode != 'youtube' or not config.has_ocr_sync:
        return

    sct_img = ocr_processing.capture_screen_region()
    if sct_img:
        cycle_id = ocr_processing.get_next_cycle_id()
        processed_img = ocr_processing.preprocess_image_for_ocr(sct_img)
        if processed_img:
            recognized_text = ocr_processing.perform_ocr(processed_img)
            if recognized_text:
                cleaned_text = ocr_processing.clean_text(recognized_text)

                # Uloženie dát do histórie
                ocr_data = config.OCR_Cycle_Data(
                    cycle_id=cycle_id,
                    raw_screenshot=sct_img,
                    processed_image=processed_img,
                    raw_ocr_text=recognized_text,
                    cleaned_ocr_text=cleaned_text,
                    timestamp=datetime.datetime.now()
                )
                config.ocr_history.append(ocr_data)

                handle_youtube_subtitle_ocr(recognized_text, cycle_id)

    # Naplánovanie ďalšieho cyklu OCR synchronizácie
    if config.is_reading and config.reading_mode == 'youtube' and config.has_ocr_sync:
        config.youtube_ocr_timer = threading.Timer(config.OCR_INTERVAL, perform_youtube_ocr_and_sync)
        config.youtube_ocr_timer.daemon = True
        config.youtube_ocr_timer.start()

def process_youtube_tts_queue():
    if not config.youtube_tts_queue.empty():
        try:
            item = config.youtube_tts_queue.get_nowait()
            speak_text(item['text'], item['cycle_id'])
            config.last_enqueued_youtube_text = item['text'] # Aktualizácia pre porovnávanie
            config.youtube_tts_queue.task_done()
        except queue.Empty: pass
        except Exception as e: logging.error(f"CHYBA [YT Proc]: {e}")

def reset_youtube_state():
    logging.info("Resetujem YouTube stav, zastavujem reč a čistím textové fronty.")
    
    stop_current_tts()

    config.subtitles = []
    config.current_youtube_url = "" 
    config.expected_ocr_video_url = ""
    config.video_time = 0.0
    config.last_synced_real_time = None
    config.last_synced_video_time = 0.0
    config.last_subtitle_index = 0
    config.has_ocr_sync = False
    config.last_enqueued_youtube_text = ""
    config.is_youtube_no_video_page = False
    
    with config.youtube_tts_queue.mutex: 
        config.youtube_tts_queue.queue.clear()
    with config.ocr_text_queue.mutex: 
        config.ocr_text_queue.queue.clear()
        
    clear_subtitle_files()
    if config.youtube_ocr_timer:
        config.youtube_ocr_timer.cancel()
        config.youtube_ocr_timer = None

def get_youtube_subtitles(url: str, yt_dlp_cmd: str, ffmpeg_cmd: str) -> List[config.Subtitle]:
    subtitles_data = []
    clear_subtitle_files() # Clear old SRTs before downloading new ones
    if download_youtube_subtitles(url, yt_dlp_cmd, ffmpeg_cmd, lang=config.TTS_LANGUAGE): # Use TTS_LANGUAGE for subtitles
        srt_path = find_downloaded_srt()
        if srt_path:
            subtitles_data = parse_srt(srt_path)
            clear_subtitle_files() # Clean up after parsing
    return subtitles_data

def get_youtube_video_info(url: str) -> Optional[dict]:
    # Táto funkcia nebola priamo použitá v pôvodnom kóde, takže ju nechávam ako placeholder.
    return None

def check_youtube_context():
    """
    Kontroluje kontext YouTube videa a aktualizuje stav aplikácie.
    Spúšťa sa periodicky v YouTube režime.
    """
    if not config.is_reading or config.reading_mode != "youtube":
        return

    try:
        current_url = get_youtube_video_url_from_tab()
        if not current_url:
            logging.warning("Nepodarilo sa získať URL z aktívnej karty.")
            config.is_youtube_no_video_page = True
            config.gui_instance.update_status("Chyba: Nepodarilo sa získať URL z aktívnej karty.")
            config.context_timer = threading.Timer(config.CONTEXT_CHECK_INTERVAL, check_youtube_context)
            config.context_timer.daemon = True
            config.context_timer.start()
            return

        is_video_page = is_youtube_video_page(current_url) or is_youtube_shorts_page(current_url)
        config.is_youtube_no_video_page = is_youtube_no_video_page_check(current_url)

        if not is_video_page or config.is_youtube_no_video_page:
            if config.current_youtube_url: # Ak sme boli na videu a už nie sme
                logging.info(f"Opustil som YouTube video stránku alebo som na stránke bez videa. Predchádzajúca URL: {config.current_youtube_url}")
                config.current_youtube_url = ""
                config.subtitles = []
                config.last_subtitle_index = 0
                config.has_ocr_sync = False
                config.last_enqueued_youtube_text = ""
                config.gui_instance.update_status("Opustil som YouTube video stránku alebo som na stránke bez videa.")
                stop_current_tts()
                if config.youtube_ocr_timer:
                    config.youtube_ocr_timer.cancel()
                    config.youtube_ocr_timer = None
            config.context_timer = threading.Timer(config.CONTEXT_CHECK_INTERVAL, check_youtube_context)
            config.context_timer.daemon = True
            config.context_timer.start()
            return

        video_id = get_youtube_video_id(current_url)
        if not video_id:
            logging.warning(f"Nepodarilo sa získať ID videa z URL: {current_url}")
            config.gui_instance.update_status("Chyba: Nepodarilo sa získať ID videa.")
            config.context_timer = threading.Timer(config.CONTEXT_CHECK_INTERVAL, check_youtube_context)
            config.context_timer.daemon = True
            config.context_timer.start()
            return

        if video_id != get_youtube_video_id(config.current_youtube_url):
            logging.info(f"Nové YouTube video detekované: {current_url}")
            config.current_youtube_url = current_url
            config.subtitles = []
            config.last_subtitle_index = 0
            config.has_ocr_sync = False
            config.last_enqueued_youtube_text = ""
            stop_current_tts()
            if config.youtube_ocr_timer:
                config.youtube_ocr_timer.cancel()
                config.youtube_ocr_timer = None
            config.gui_instance.update_status("Načítavam titulky pre nové video...")

            # Pokus o stiahnutie titulkov
            if current_url in config.failed_subtitle_urls:
                logging.warning(f"Titulky pre {current_url} už predtým zlyhali, preskakujem sťahovanie.")
                config.gui_instance.update_status("Titulky pre toto video nie sú dostupné alebo sa nepodarilo stiahnuť.")
                # Pokračujeme v OCR synchronizácii, ak titulky nie sú dostupné
                config.has_ocr_sync = True
                config.expected_ocr_video_url = current_url
                # Spustíme OCR timer, ak je potrebná OCR synchronizácia
                if not config.youtube_ocr_timer:
                    config.youtube_ocr_timer = threading.Timer(config.OCR_INTERVAL, perform_youtube_ocr_and_sync)
                    config.youtube_ocr_timer.daemon = True
                    config.youtube_ocr_timer.start()
            else:
                subtitles_data = get_youtube_subtitles(current_url, config.YT_DLP_CMD, config.FFMPEG_CMD)
                if subtitles_data:
                    config.subtitles = [config.Subtitle(s['start'], s['end'], s['text']) for s in subtitles_data]
                    logging.info(f"Načítaných {len(config.subtitles)} titulkov.")
                    config.gui_instance.update_status(f"Načítaných {len(config.subtitles)} titulkov.")
                    config.has_ocr_sync = False # Ak máme titulky, nebudeme robiť OCR sync
                    if config.youtube_ocr_timer:
                        config.youtube_ocr_timer.cancel()
                        config.youtube_ocr_timer = None
                else:
                    logging.warning(f"Nepodarilo sa stiahnuť titulky pre {current_url}. Prechádzam na OCR synchronizáciu.")
                    config.gui_instance.update_status("Titulky nie sú dostupné. Prechádzam na OCR synchronizáciu.")
                    config.has_ocr_sync = True
                    config.expected_ocr_video_url = current_url
                    # Spustíme OCR timer, ak je potrebná OCR synchronizácia
                    if not config.youtube_ocr_timer:
                        config.youtube_ocr_timer = threading.Timer(config.OCR_INTERVAL, perform_youtube_ocr_and_sync)
                        config.youtube_ocr_timer.daemon = True
                        config.youtube_ocr_timer.start()

        # Získanie aktuálneho času videa
        current_video_time = get_youtube_video_time(config.current_youtube_url)
        if current_video_time is not None:
            config.video_time = current_video_time
            config.last_synced_real_time = time.time()
            config.last_synced_video_time = current_video_time
            # logging.debug(f"Aktuálny čas videa: {config.video_time:.2f}s")
        else:
            # Ak sa nepodarí získať čas, predpokladáme, že video je pozastavené alebo došlo k chybe
            config.is_paused_video = True
            logging.warning("Nepodarilo sa získať čas videa. Predpokladám pozastavenie alebo chybu.")
            config.gui_instance.update_status("Video je pozastavené alebo došlo k chybe pri získavaní času.")
            # Ak je video pozastavené, nebudeme ďalej spracovávať titulky/OCR
            if config.youtube_ocr_timer:
                config.youtube_ocr_timer.cancel()
                config.youtube_ocr_timer = None
            config.context_timer = threading.Timer(config.CONTEXT_CHECK_INTERVAL, check_youtube_context)
            config.context_timer.daemon = True
            config.context_timer.start()
            return

        # Ak máme titulky, spracujeme ich
        if config.subtitles:
            ocr_processing.process_ocr_text_youtube(config.video_time)
            if config.youtube_ocr_timer:
                config.youtube_ocr_timer.cancel()
                config.youtube_ocr_timer = None
        elif config.has_ocr_sync and config.expected_ocr_video_url == current_url:
            # Ak nemáme titulky a prešli sme na OCR synchronizáciu
            # OCR sa spúšťa cez perform_youtube_ocr_and_sync, ktorá sa sama preplánuje
            if not config.youtube_ocr_timer:
                config.youtube_ocr_timer = threading.Timer(config.OCR_INTERVAL, perform_youtube_ocr_and_sync)
                config.youtube_ocr_timer.daemon = True
                config.youtube_ocr_timer.start()
        else:
            # Ak nemáme titulky a ani OCR synchronizáciu (napr. ešte sa nenačítali titulky)
            pass

    except Exception as e:
        logging.error(f"Chyba v check_youtube_context: {e}", exc_info=True)
        config.gui_instance.update_status(f"Chyba v YouTube kontexte: {e}")
    finally:
        if config.is_reading and config.reading_mode == "youtube":
            config.context_timer = threading.Timer(config.CONTEXT_CHECK_INTERVAL, check_youtube_context)
            config.context_timer.daemon = True
            config.context_timer.start()