#!/usr/bin/env python3
"""
Test script pre overenie globálnych hotkeys na macOS.
Tento script testuje, či pynput funguje s aktuálnymi povoleniami.
"""

import logging
import time
import sys

# Nastavenie logovania
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_pynput_permissions():
    """Testuje, či pynput má potrebné povolenia na macOS."""
    try:
        from pynput import keyboard
        
        logging.info("🔍 Testujem pynput povolenia...")
        
        # Test 1: Môž<PERSON>e vytvoriť GlobalHotKeys objekt?
        try:
            test_hotkeys = {
                '<ctrl>+<alt>+<cmd>+<space>': lambda: logging.info("✅ Test hotkey funguje!")
            }
            
            listener = keyboard.GlobalHotKeys(test_hotkeys)
            logging.info("✅ GlobalHotKeys objekt vytvorený úspešne")
            
            # Test 2: <PERSON><PERSON><PERSON><PERSON><PERSON> spustiť listener?
            listener.start()
            logging.info("✅ Hotkey listener spustený úspešne")
            
            logging.info("🎯 TESTOVANIE: Stlačte Ctrl+Alt+Cmd+Space v nasledujúcich 10 sekundách...")
            logging.info("   Ak sa zobrazí '✅ Test hotkey funguje!', hotkeys fungujú globálne")
            logging.info("   Ak sa nič nestane, potrebujete povoliť povolenia v System Preferences")
            
            # Čakáme 10 sekúnd na test
            time.sleep(10)
            
            listener.stop()
            logging.info("✅ Test dokončený")
            
            return True
            
        except Exception as e:
            logging.error(f"❌ Chyba pri testovaní hotkeys: {e}")
            return False
            
    except ImportError:
        logging.error("❌ pynput nie je nainštalovaný: pip install pynput")
        return False

def show_macos_instructions():
    """Zobrazí inštrukcie pre nastavenie povolení na macOS."""
    print("\n" + "="*60)
    print("🔧 NASTAVENIE POVOLENÍ PRE GLOBÁLNE HOTKEYS NA macOS")
    print("="*60)
    print()
    print("1. Otvorte System Preferences (Systémové nastavenia)")
    print("2. Kliknite na Security & Privacy (Bezpečnosť a súkromie)")
    print("3. Kliknite na záložku Privacy (Súkromie)")
    print()
    print("4. V ľavom paneli nájdite a kliknite na:")
    print("   📋 Input Monitoring")
    print("   ✅ Zaškrtnite Python alebo Terminal")
    print()
    print("5. V ľavom paneli nájdite a kliknite na:")
    print("   ♿ Accessibility")  
    print("   ✅ Zaškrtnite Python alebo Terminal")
    print()
    print("6. Reštartujte aplikáciu")
    print()
    print("💡 TIP: Ak nevidíte Python/Terminal v zozname,")
    print("   kliknite na '+' a nájdite:")
    print("   /usr/bin/python3 alebo /Applications/Utilities/Terminal.app")
    print()
    print("="*60)

if __name__ == "__main__":
    print("🧪 Test globálnych hotkeys pre macOS")
    print("-" * 40)
    
    if test_pynput_permissions():
        print("\n✅ Hotkeys by mali fungovať!")
        print("   Ak test hotkey nefungoval, pozrite si inštrukcie nižšie.")
    else:
        print("\n❌ Hotkeys nefungujú - potrebujete nastaviť povolenia")
    
    show_macos_instructions()
