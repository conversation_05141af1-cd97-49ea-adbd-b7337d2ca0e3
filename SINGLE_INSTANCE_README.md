# Single Instance Funkcionalita

## Popis
Aplikácia teraz podporuje **single instance** funkcionalitu, k<PERSON><PERSON>, že bež<PERSON> vždy len jedna inštancia aplikácie. Ak sa pokúsite spustiť novú inštanciu, stará sa automaticky ukončí a zostane len nová.

## Ako to funguje

### 1. **Cross-platform riešenie**
Implementácia funguje na všetkých platformách:
- **macOS** - používa Unix signály (`SIGTERM`)
- **Linux** - používa Unix signály (`SIGTERM`) 
- **Windows** - používa Windows API (`TerminateProcess`)

### 2. **PID súbor**
- Pri spustení aplikácie sa vytvorí súbor `ocr_reader.pid`
- Súbor obsahuje PID (Process ID) aktuálne bežiacej inštancie
- Pri ukončení aplikácie sa súbor automaticky vymaže

### 3. **Proces ukončenia starej inštancie**
1. <PERSON><PERSON> inštancia kontroluje, či existuje `ocr_reader.pid`
2. Ak existuje, prečíta PID starej inštancie
3. Skontroluje, či proces s týmto PID stále beží
4. Ak beží, pošle mu signál na ukončenie (`SIGTERM` na Unix/macOS, `TerminateProcess` na Windows)
5. Počká 1 sekundu, aby sa proces stihol ukončiť
6. Zapíše svoj vlastný PID do súboru

## Implementované funkcie

### `is_running(pid)`
```python
def is_running(pid):
    """Kontroluje, či proces s daným PID beží."""
    try:
        if platform.system() == "Windows":
            # Windows implementácia
            import ctypes
            PROCESS_QUERY_INFORMATION = 0x0400
            handle = ctypes.windll.kernel32.OpenProcess(PROCESS_QUERY_INFORMATION, False, pid)
            if handle:
                ctypes.windll.kernel32.CloseHandle(handle)
                return True
            return False
        else:
            # Unix/Linux/macOS implementácia
            os.kill(pid, 0)  # len otestuje, či proces beží
            return True
    except (OSError, Exception):
        return False
```

### `terminate_process(pid)`
```python
def terminate_process(pid):
    """Ukončí proces s daným PID."""
    try:
        if platform.system() == "Windows":
            # Windows implementácia
            import ctypes
            PROCESS_TERMINATE = 1
            handle = ctypes.windll.kernel32.OpenProcess(PROCESS_TERMINATE, False, pid)
            if handle:
                result = ctypes.windll.kernel32.TerminateProcess(handle, -1)
                ctypes.windll.kernel32.CloseHandle(handle)
                return result != 0
            return False
        else:
            # Unix/Linux/macOS implementácia
            os.kill(pid, signal.SIGTERM)
            return True
    except (OSError, Exception) as e:
        logging.warning(f"⚠️ Nepodarilo sa ukončiť proces {pid}: {e}")
        return False
```

### `ensure_single_instance()`
```python
def ensure_single_instance():
    """Zabezpečí, že beží len jedna inštancia aplikácie."""
    if os.path.exists(PID_FILE):
        try:
            with open(PID_FILE, "r") as f:
                old_pid = int(f.read().strip())
            
            if is_running(old_pid):
                logging.info(f"🔄 Ukončujem starú inštanciu aplikácie (PID {old_pid})...")
                if terminate_process(old_pid):
                    logging.info(f"✅ Stará inštancia úspešne ukončená")
                    time.sleep(1)  # Počkaj, aby sa proces stihol ukončiť
                else:
                    logging.warning(f"⚠️ Nepodarilo sa ukončiť starú inštanciu")
            else:
                logging.info("🔍 Starý PID súbor existuje, ale proces už nebeží")
        except (ValueError, IOError) as e:
            logging.warning(f"⚠️ Chyba pri čítaní PID súboru: {e}")
    
    # Zapíš aktuálny PID
    try:
        with open(PID_FILE, "w") as f:
            f.write(str(os.getpid()))
        logging.info(f"📝 Nová inštancia zaregistrovaná (PID {os.getpid()})")
    except IOError as e:
        logging.error(f"❌ Nepodarilo sa zapísať PID súbor: {e}")
```

## Signal Handlers

### Graceful Shutdown
```python
def setup_signal_handlers():
    """Nastavenie signal handlerov pre graceful shutdown."""
    def signal_handler(signum, frame):
        logging.info(f"📡 Prijatý signál {signum}, ukončujem aplikáciu...")
        cleanup_pid_file()
        sys.exit(0)
    
    if platform.system() != "Windows":
        # Unix/Linux/macOS signály
        signal.signal(signal.SIGTERM, signal_handler)
        signal.signal(signal.SIGINT, signal_handler)
```

### Cleanup
```python
def cleanup_pid_file():
    """Vymaže PID súbor pri ukončení aplikácie."""
    try:
        if os.path.exists(PID_FILE):
            os.remove(PID_FILE)
            logging.info("🗑️ PID súbor vymazaný")
    except OSError as e:
        logging.warning(f"⚠️ Nepodarilo sa vymazať PID súbor: {e}")
```

## Testovanie

### Test 1: Spustenie prvej inštancie
```bash
python main_qt.py
```
**Výsledok**: 
- Aplikácia sa spustí normálne
- Vytvorí sa `ocr_reader.pid` súbor
- V logoch: `📝 Nová inštancia zaregistrovaná (PID xxxxx)`

### Test 2: Spustenie druhej inštancie
```bash
python main_qt.py  # v novom termináli
```
**Výsledok**:
- Nová inštancia detekuje starú
- Pošle signál na ukončenie starej inštancie
- Stará inštancia sa ukončí gracefully
- Nová inštancia sa spustí a prepíše PID súbor
- V logoch starej inštancie: `📡 Prijatý signál 15, ukončujem aplikáciu...`
- V logoch novej inštancie: `🔄 Ukončujem starú inštanciu aplikácie (PID xxxxx)...`

## Výhody

1. **Jednoduchosť**: Používateľ nemusí manuálne ukončovať staré inštancie
2. **Cross-platform**: Funguje na Windows, macOS aj Linux
3. **Graceful shutdown**: Stará inštancia sa ukončí čisto s cleanup
4. **Spoľahlivosť**: Ak sa PID súbor poškodí, aplikácia sa stále spustí
5. **Logovanie**: Všetky akcie sú zalogované pre debugging

## Súbory

- **PID súbor**: `ocr_reader.pid` - obsahuje PID aktuálne bežiacej inštancie
- **Implementácia**: `main_qt.py` - obsahuje všetky single instance funkcie

## Poznámky

- PID súbor sa automaticky vymaže pri normálnom ukončení aplikácie
- Ak aplikácia crashne, PID súbor môže zostať, ale nová inštancia ho správne spracuje
- Na Windows sa používa Windows API pre maximálnu kompatibilitu
- Na Unix systémoch sa používajú štandardné signály
