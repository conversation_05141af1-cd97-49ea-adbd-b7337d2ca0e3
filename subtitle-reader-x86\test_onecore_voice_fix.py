#!/usr/bin/env python3
"""
Test OneCore voice name fix
"""

import time
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_voice_name_fix():
    """Test OneCore voice name matching fix."""
    print("🚀 OneCore Voice Name Fix Test")
    print("=" * 60)
    
    try:
        from onecore_tts_provider import get_onecore_provider
        
        # Get provider
        provider = get_onecore_provider()
        
        if not provider.is_available():
            print("❌ OneCore provider nie je dostupný")
            return False
        
        print("✅ OneCore provider je dostupný")
        
        # Get voices
        voices = provider.get_available_voices()
        print(f"🎤 Nájdené hlasy: {len(voices)}")
        
        for voice in voices:
            print(f"   - {voice['name']} ({voice['language']}) - {voice['gender']}")
        
        # Test voice name variations
        test_names = [
            "Microsoft Filip",
            "Microsoft Filip (Premium)",
            "Microsoft David",
            "Microsoft David (Premium)",
            "Microsoft Zira",
            "Microsoft Zira (Premium)"
        ]
        
        print("\n🔍 Test nastavenia hlasov s rôznymi názvami:")
        
        for test_name in test_names:
            success = provider.set_voice(test_name)
            status = "✅ ÚSPEŠNÉ" if success else "❌ ZLYHALO"
            print(f"   {test_name}: {status}")
        
        # Test Slovak voice specifically
        print("\n🇸🇰 Test slovenského hlasu:")
        
        slovak_tests = [
            "Microsoft Filip",
            "Microsoft Filip (Premium)",
            "Filip"
        ]
        
        for slovak_name in slovak_tests:
            success = provider.set_voice(slovak_name)
            if success:
                print(f"✅ Slovenský hlas nastavený: {slovak_name}")
                
                # Test speech
                print("🔊 Test slovenského TTS...")
                speech_success = provider.speak("Ahoj, toto je test slovenského OneCore hlasu.", slovak_name)
                if speech_success:
                    print("✅ Slovenská reč spustená")
                    time.sleep(3)  # Wait for speech
                else:
                    print("❌ Slovenská reč zlyhala")
                
                break
        else:
            print("❌ Žiadny slovenský hlas sa nepodarilo nastaviť")
        
        print("\n" + "=" * 60)
        print("🎉 VOICE NAME FIX TEST DOKONČENÝ!")
        
        return True
        
    except Exception as e:
        print(f"❌ Test zlyhal: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_voice_name_fix()
    
    print("\n" + "=" * 60)
    print("📊 FINÁLNY VÝSLEDOK:")
    if success:
        print("🎉 VOICE NAME FIX FUNGUJE!")
        print("🚀 OneCore hlasy sú pripravené na použitie")
    else:
        print("❌ Voice name fix test zlyhal")
    
    exit(0 if success else 1)
