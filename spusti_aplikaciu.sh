#!/bin/bash

# 🚀 Spúšťač Subtitle Reader aplikácie pre macOS
# Vytvorené: 2025-09-24

echo "🍎 Spúšťam Subtitle Reader aplikáciu..."

# Získaj adresár kde sa nachádza tento script
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
APP_DIR="$SCRIPT_DIR"

echo "📁 Adresár aplikácie: $APP_DIR"

# Prejdi do adresára aplikácie
cd "$APP_DIR"

# Kontrola či existuje main_qt.py
if [ ! -f "main_qt.py" ]; then
    echo "❌ Chyba: main_qt.py sa nenašiel v adresári $APP_DIR"
    echo "💡 Skontroluj, či si v správnom adresári"
    read -p "Stlač Enter pre ukončenie..."
    exit 1
fi

# Kontrola Python
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "❌ Chyba: Python nie je nainštalovaný alebo nie je v PATH"
        echo "💡 Nainštaluj Python z https://python.org"
        read -p "Stlač Enter pre ukončenie..."
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

echo "🐍 Používam Python: $PYTHON_CMD"

# Kontrola virtual environment
if [ -d "venv" ]; then
    echo "🔧 Aktivujem virtual environment..."
    source venv/bin/activate
    echo "✅ Virtual environment aktivovaný"
elif [ -d ".venv" ]; then
    echo "🔧 Aktivujem virtual environment (.venv)..."
    source .venv/bin/activate
    echo "✅ Virtual environment aktivovaný"
else
    echo "ℹ️ Virtual environment sa nenašiel, používam systémový Python"
fi

# Kontrola závislostí
echo "📦 Kontrolujem závislosti..."
if ! $PYTHON_CMD -c "import PyQt6" 2>/dev/null; then
    echo "⚠️ Chýbajú závislosti. Pokúšam sa ich nainštalovať..."
    if [ -f "requirements.txt" ]; then
        pip install -r requirements.txt
    else
        echo "❌ requirements.txt sa nenašiel"
        echo "💡 Nainštaluj závislosti manuálne: pip install PyQt6 pytesseract pillow pynput"
        read -p "Stlač Enter pre pokračovanie aj tak..."
    fi
fi

echo "🚀 Spúšťam aplikáciu..."
echo "----------------------------------------"

# Spusti aplikáciu
$PYTHON_CMD main_qt.py

# Kontrola exit kódu
EXIT_CODE=$?
echo "----------------------------------------"

if [ $EXIT_CODE -eq 0 ]; then
    echo "✅ Aplikácia sa ukončila úspešne"
else
    echo "❌ Aplikácia sa ukončila s chybou (kód: $EXIT_CODE)"
    echo "💡 Skontroluj logy vyššie pre viac informácií"
    read -p "Stlač Enter pre ukončenie..."
fi

echo "👋 Ďakujem za používanie Subtitle Reader!"
