#!/usr/bin/env python3
"""
Windows-specific implementations for Subtitle Reader application.
"""

import sys
import logging
import subprocess
import hashlib
import locale
from typing import Optional, Dict, List, Any
from pathlib import Path

from platform_utils import (
    TTSProvider, ActiveAppDetector, SystemInfoProvider, 
    BrowserURLDetector, PlatformError, IS_WINDOWS
)

if not IS_WINDOWS:
    raise ImportError("This module is only for Windows platform")

# Windows-specific imports
try:
    import pyttsx3
    PYTTSX3_AVAILABLE = True
except ImportError:
    PYTTSX3_AVAILABLE = False
    logging.warning("⚠️ pyttsx3 not available - TTS will not work")

try:
    import win32gui
    import win32process
    import win32api
    import win32con
    PYWIN32_AVAILABLE = True
except ImportError:
    PYWIN32_AVAILABLE = False
    logging.warning("⚠️ pywin32 not available - some features will not work")

try:
    import wmi
    WMI_AVAILABLE = True
except ImportError:
    WMI_AVAILABLE = False
    logging.warning("⚠️ WMI not available - hardware fingerprinting may be limited")

class WindowsTTSProvider(TTSProvider):
    """Windows TTS implementation using pyttsx3."""
    
    def __init__(self):
        super().__init__()
        self.engine = None
        self.current_process = None
        self._initialize_engine()
    
    def _check_platform_support(self) -> bool:
        return IS_WINDOWS and PYTTSX3_AVAILABLE
    
    def _initialize_engine(self):
        """Initialize pyttsx3 engine."""
        if not PYTTSX3_AVAILABLE:
            logging.error("❌ pyttsx3 not available")
            return
        
        try:
            self.engine = pyttsx3.init()
            # Set default properties
            self.engine.setProperty('rate', 200)
            self.engine.setProperty('volume', 1.0)
            logging.info("✅ Windows TTS engine initialized")
        except Exception as e:
            logging.error(f"❌ Failed to initialize TTS engine: {e}")
            self.engine = None
    
    def speak(self, text: str, voice: str = None, rate: int = 200, volume: float = 1.0) -> bool:
        """Speak text using Windows TTS (OneCore or SAPI)."""

        # Try OneCore first if voice is OneCore
        if voice and self._is_onecore_voice(voice):
            return self._speak_onecore(text, voice, rate, volume)

        # Fallback to SAPI/pyttsx3
        return self._speak_sapi(text, voice, rate, volume)

    def _is_onecore_voice(self, voice_name: str) -> bool:
        """Check if voice is OneCore voice."""
        try:
            from winrt.windows.media.speechsynthesis import SpeechSynthesizer
            winrt_voices = SpeechSynthesizer.all_voices

            for voice_info in winrt_voices:
                if voice_name in voice_info.display_name or voice_info.display_name in voice_name:
                    return True
            return False
        except:
            return False

    def _speak_onecore(self, text: str, voice: str, rate: int, volume: float = 1.0) -> bool:
        """Speak using OneCore TTS."""
        try:
            from onecore_tts_provider import get_onecore_provider

            provider = get_onecore_provider()
            if not provider.is_available():
                logging.warning("⚠️ OneCore provider not available, falling back to SAPI")
                return self._speak_sapi(text, voice, rate, volume)

            return provider.speak(text, voice, rate, volume)

        except ImportError:
            logging.debug("⚠️ OneCore provider not available, using SAPI")
            return self._speak_sapi(text, voice, rate)
        except Exception as e:
            logging.error(f"❌ OneCore speak error: {e}")
            return self._speak_sapi(text, voice, rate, volume)

    def _speak_sapi(self, text: str, voice: str = None, rate: int = 200, volume: float = 1.0) -> bool:
        """Speak using SAPI/pyttsx3."""
        if not self.engine:
            logging.error("❌ TTS engine not available")
            return False

        try:
            # Set rate and volume
            self.engine.setProperty('rate', rate)
            self.engine.setProperty('volume', volume)

            # Set voice if specified
            if voice:
                voices = self.engine.getProperty('voices')
                for v in voices:
                    if voice.lower() in v.name.lower():
                        self.engine.setProperty('voice', v.id)
                        break

            # Speak text
            self.engine.say(text)
            self.engine.runAndWait()
            return True

        except Exception as e:
            logging.error(f"❌ SAPI speak error: {e}")
            return False
    
    def stop(self) -> bool:
        """Stop current TTS playback (OneCore or SAPI)."""
        stopped = False
        # 1) Stop OneCore provider, ak je k dispozcii
        try:
            from onecore_tts_provider import get_onecore_provider
            onecore = get_onecore_provider()
            if onecore and onecore.is_available():
                if onecore.stop():
                    stopped = True
        except Exception:
            pass

        # 2) Stop SAPI/pyttsx3 engine
        if self.engine:
            try:
                self.engine.stop()
                stopped = True or stopped
            except Exception as e:
                logging.error(f"❌ TTS stop error: {e}")
        return stopped
    
    def get_available_voices(self) -> List[Dict[str, Any]]:
        """Get list of available Windows TTS voices (SAPI + OneCore)."""
        voices = []

        try:
            # 1. Try OneCore voices first (highest quality)
            onecore_voices = self._get_voices_via_onecore()
            voices.extend(onecore_voices)

            # 2. Try Windows SAPI directly (like macOS AppKit approach)
            sapi_voices = self._get_voices_via_sapi()
            voices.extend(sapi_voices)

            # 3. Fallback to pyttsx3 if both fail
            if not voices and self.engine:
                pyttsx3_voices = self._get_voices_via_pyttsx3()
                voices.extend(pyttsx3_voices)

        except Exception as e:
            logging.error(f"❌ Error getting Windows voices: {e}")

        logging.info(f"🎤 Found {len(voices)} Windows TTS voices ({len([v for v in voices if v.get('quality') == 'premium'])} OneCore, {len([v for v in voices if v.get('quality') != 'premium'])} SAPI)")
        return voices

    def _get_voices_via_onecore(self) -> List[Dict[str, Any]]:
        """Get voices using Windows OneCore (highest quality)."""
        voices = []

        try:
            from winrt.windows.media.speechsynthesis import SpeechSynthesizer

            # Get all OneCore voices
            winrt_voices = SpeechSynthesizer.all_voices

            for voice_info in winrt_voices:
                # Parse gender (0 = Male, 1 = Female)
                gender = 'male' if voice_info.gender == 0 else 'female' if voice_info.gender == 1 else 'unknown'

                # Extract language code for mapping
                language = voice_info.language
                lang_code = self._extract_language_from_name(language) or language.split('-')[0].lower()

                voice_data = {
                    'id': voice_info.id,
                    'name': voice_info.display_name,
                    'language': language,
                    'gender': gender,
                    'quality': 'premium'  # OneCore hlasy sú vždy premium
                }

                voices.append(voice_data)
                logging.debug(f"🎤 OneCore Voice: {voice_info.display_name} ({language}) - {gender}")

            logging.info(f"🎤 Found {len(voices)} OneCore voices")

        except ImportError:
            logging.debug("⚠️ OneCore WinRT not available - skipping OneCore voices")
        except Exception as e:
            logging.error(f"❌ OneCore voice detection error: {e}")

        return voices

    def _get_voices_via_sapi(self) -> List[Dict[str, Any]]:
        """Get voices using Windows SAPI directly (like macOS AppKit)."""
        voices = []

        try:
            import win32com.client

            # Create SAPI voice object
            sapi_voices = win32com.client.Dispatch("SAPI.SpVoice")
            voice_tokens = sapi_voices.GetVoices()

            for i in range(voice_tokens.Count):
                voice_token = voice_tokens.Item(i)

                # Get voice attributes
                voice_name = voice_token.GetDescription()
                voice_id = voice_token.Id

                # Get language from registry attributes
                language = self._get_voice_language_from_token(voice_token)

                # Determine gender from name (basic heuristic)
                gender = self._determine_gender_from_name(voice_name)

                # Determine quality from name
                quality = 'standard'
                if 'Premium' in voice_name or 'Neural' in voice_name:
                    quality = 'premium'
                elif 'Enhanced' in voice_name or 'HD' in voice_name:
                    quality = 'enhanced'

                voice_info = {
                    'id': voice_id,
                    'name': voice_name,
                    'language': language,
                    'gender': gender,
                    'quality': quality
                }
                voices.append(voice_info)

                logging.debug(f"🎤 SAPI Voice: {voice_name} ({language}) - {quality}")

        except Exception as e:
            logging.error(f"❌ SAPI voice detection error: {e}")

        return voices

    def _get_voices_via_pyttsx3(self) -> List[Dict[str, Any]]:
        """Fallback: Get voices using pyttsx3."""
        voices = []

        try:
            pyttsx3_voices = self.engine.getProperty('voices')

            for voice in pyttsx3_voices:
                # Bezpečné získanie jazyka
                languages = getattr(voice, 'languages', ['en-US']) if hasattr(voice, 'languages') else ['en-US']
                language = languages[0] if languages and len(languages) > 0 else 'en-US'

                voice_info = {
                    'id': voice.id,
                    'name': voice.name,
                    'language': language,
                    'gender': getattr(voice, 'gender', 'unknown'),
                    'quality': 'standard'
                }
                voices.append(voice_info)

        except Exception as e:
            logging.error(f"❌ pyttsx3 voice detection error: {e}")

        return voices

    def _get_voice_language_from_token(self, voice_token) -> str:
        """Extract language from Windows voice token."""
        try:
            # Try to get language from token attributes
            attributes = voice_token.GetAttribute("Language")
            if attributes:
                # Convert Windows LCID to language code
                lcid = int(attributes, 16) if isinstance(attributes, str) else attributes
                return self._lcid_to_language(lcid)
        except:
            pass

        # Fallback: try to extract from voice name
        voice_name = voice_token.GetDescription()
        return self._extract_language_from_name(voice_name)

    def _lcid_to_language(self, lcid: int) -> str:
        """Convert Windows LCID to language code."""
        # Common Windows LCID mappings
        lcid_map = {
            0x0409: 'en-US',  # English (United States)
            0x0809: 'en-GB',  # English (United Kingdom)
            0x0c09: 'en-AU',  # English (Australia)
            0x040c: 'fr-FR',  # French (France)
            0x0407: 'de-DE',  # German (Germany)
            0x040a: 'es-ES',  # Spanish (Spain)
            0x0410: 'it-IT',  # Italian (Italy)
            0x0411: 'ja-JP',  # Japanese (Japan)
            0x0412: 'ko-KR',  # Korean (Korea)
            0x0804: 'zh-CN',  # Chinese (Simplified)
            0x0404: 'zh-TW',  # Chinese (Traditional)
            0x0416: 'pt-BR',  # Portuguese (Brazil)
            0x0816: 'pt-PT',  # Portuguese (Portugal)
            0x0413: 'nl-NL',  # Dutch (Netherlands)
            0x041d: 'sv-SE',  # Swedish (Sweden)
            0x0414: 'nb-NO',  # Norwegian (Bokmål)
            0x040b: 'fi-FI',  # Finnish (Finland)
            0x0406: 'da-DK',  # Danish (Denmark)
            0x0405: 'cs-CZ',  # Czech (Czech Republic)
            0x041b: 'sk-SK',  # Slovak (Slovakia)
            0x0415: 'pl-PL',  # Polish (Poland)
            0x040e: 'hu-HU',  # Hungarian (Hungary)
            0x0419: 'ru-RU',  # Russian (Russia)
        }

        return lcid_map.get(lcid, 'en-US')

    def _extract_language_from_name(self, voice_name: str) -> str:
        """Extract language from voice name."""
        voice_name_lower = voice_name.lower()

        # Language patterns in voice names
        if 'english' in voice_name_lower or 'david' in voice_name_lower or 'zira' in voice_name_lower:
            return 'en-US'
        elif 'french' in voice_name_lower or 'hortense' in voice_name_lower:
            return 'fr-FR'
        elif 'german' in voice_name_lower or 'hedda' in voice_name_lower or 'stefan' in voice_name_lower:
            return 'de-DE'
        elif 'spanish' in voice_name_lower or 'helena' in voice_name_lower or 'pablo' in voice_name_lower:
            return 'es-ES'
        elif 'italian' in voice_name_lower or 'elsa' in voice_name_lower or 'cosimo' in voice_name_lower:
            return 'it-IT'
        elif 'japanese' in voice_name_lower or 'haruka' in voice_name_lower or 'ichiro' in voice_name_lower:
            return 'ja-JP'
        elif 'chinese' in voice_name_lower or 'huihui' in voice_name_lower or 'kangkang' in voice_name_lower:
            return 'zh-CN'
        elif 'portuguese' in voice_name_lower or 'maria' in voice_name_lower or 'daniel' in voice_name_lower:
            return 'pt-BR'
        elif 'czech' in voice_name_lower or 'jakub' in voice_name_lower:
            return 'cs-CZ'
        elif 'slovak' in voice_name_lower or 'filip' in voice_name_lower:
            return 'sk-SK'
        elif 'polish' in voice_name_lower or 'paulina' in voice_name_lower:
            return 'pl-PL'

        return 'en-US'  # Default fallback

    def _determine_gender_from_name(self, voice_name: str) -> str:
        """Determine gender from voice name (basic heuristic)."""
        voice_name_lower = voice_name.lower()

        # Female names/patterns
        female_patterns = [
            'zira', 'cortana', 'helena', 'hortense', 'hedda', 'elsa',
            'haruka', 'huihui', 'maria', 'paulina', 'female', 'woman'
        ]

        # Male names/patterns
        male_patterns = [
            'david', 'mark', 'pablo', 'stefan', 'cosimo', 'ichiro',
            'kangkang', 'daniel', 'jakub', 'filip', 'male', 'man'
        ]

        for pattern in female_patterns:
            if pattern in voice_name_lower:
                return 'female'

        for pattern in male_patterns:
            if pattern in voice_name_lower:
                return 'male'

        return 'unknown'

    def is_speaking(self) -> bool:
        """Check if TTS is currently speaking (OneCore or SAPI)."""
        # 1) OneCore poskytovateľ môže prehrávať mimo pyttsx3
        try:
            from onecore_tts_provider import get_onecore_provider
            onecore = get_onecore_provider()
            if onecore and onecore.is_available() and onecore.is_speaking():
                return True
        except Exception:
            pass

        # 2) SAPI/pyttsx3 engine stav
        if not self.engine:
            return False
        try:
            return self.engine.isBusy()
        except Exception:
            return False

class WindowsActiveAppDetector(ActiveAppDetector):
    """Windows active application detection using win32gui."""
    
    def _check_platform_support(self) -> bool:
        return IS_WINDOWS and PYWIN32_AVAILABLE
    
    def get_active_app_name(self) -> str:
        """Get name of currently active application on Windows."""
        if not PYWIN32_AVAILABLE:
            logging.warning("⚠️ pywin32 not available for active app detection")
            return ""
        
        try:
            # Get foreground window
            hwnd = win32gui.GetForegroundWindow()
            if not hwnd:
                return ""
            
            # Get process ID
            _, pid = win32process.GetWindowThreadProcessId(hwnd)
            
            # Get process handle
            handle = win32api.OpenProcess(win32con.PROCESS_QUERY_INFORMATION | win32con.PROCESS_VM_READ, False, pid)
            
            # Get executable name
            exe_name = win32process.GetModuleFileNameEx(handle, 0)
            app_name = Path(exe_name).stem
            
            win32api.CloseHandle(handle)
            return app_name
            
        except Exception as e:
            logging.debug(f"⚠️ Error getting active app: {e}")
            return ""
    
    def get_active_window_title(self) -> str:
        """Get title of currently active window."""
        if not PYWIN32_AVAILABLE:
            return ""
        
        try:
            hwnd = win32gui.GetForegroundWindow()
            if hwnd:
                return win32gui.GetWindowText(hwnd)
        except Exception as e:
            logging.debug(f"⚠️ Error getting window title: {e}")
        
        return ""

class WindowsSystemInfoProvider(SystemInfoProvider):
    """Windows system information provider."""
    
    def _check_platform_support(self) -> bool:
        return IS_WINDOWS
    
    def get_system_language(self) -> str:
        """Get Windows system default language."""
        try:
            # Try to get system locale
            lang_code = locale.getdefaultlocale()[0]
            if lang_code:
                # Convert from locale format (e.g., 'en_US') to standard format
                if '_' in lang_code:
                    lang, region = lang_code.split('_', 1)
                    return f"{lang}-{region}"
                return lang_code
        except Exception as e:
            logging.debug(f"⚠️ Error getting system language: {e}")
        
        # Fallback to English
        return "en-US"
    
    def get_hardware_fingerprint(self) -> str:
        """Get Windows hardware fingerprint."""
        components = []
        
        try:
            # Try WMI first
            if WMI_AVAILABLE:
                c = wmi.WMI()
                
                # Get motherboard info
                for board in c.Win32_BaseBoard():
                    if board.SerialNumber:
                        components.append(f"mb:{board.SerialNumber}")
                
                # Get CPU info
                for cpu in c.Win32_Processor():
                    if cpu.ProcessorId:
                        components.append(f"cpu:{cpu.ProcessorId}")
                
                # Get BIOS info
                for bios in c.Win32_BIOS():
                    if bios.SerialNumber:
                        components.append(f"bios:{bios.SerialNumber}")
            
            # Fallback methods
            if not components:
                # Use computer name as fallback
                import socket
                hostname = socket.gethostname()
                components.append(f"hostname:{hostname}")
                
                # Try to get volume serial number
                try:
                    import win32api
                    volume_info = win32api.GetVolumeInformation("C:\\")
                    if volume_info[1]:  # Serial number
                        components.append(f"volume:{volume_info[1]}")
                except:
                    pass
            
            if not components:
                # Ultimate fallback
                import platform
                components.append(f"fallback:{platform.node()}")
            
            # Create hash
            combined = "|".join(sorted(components))
            fingerprint = hashlib.sha256(combined.encode('utf-8')).hexdigest()[:32]
            
            logging.info(f"🔐 Windows hardware fingerprint created: {fingerprint[:8]}...")
            return fingerprint
            
        except Exception as e:
            logging.error(f"❌ Error creating hardware fingerprint: {e}")
            # Emergency fallback
            import platform
            fallback = hashlib.sha256(platform.node().encode('utf-8')).hexdigest()[:32]
            return fallback

class WindowsBrowserURLDetector(BrowserURLDetector):
    """Windows browser URL detection (limited functionality)."""
    
    def _check_platform_support(self) -> bool:
        return IS_WINDOWS
    
    def get_browser_url(self) -> str:
        """Get current browser URL (Windows implementation limited)."""
        logging.warning("⚠️ Browser URL detection not fully implemented for Windows")
        # TODO: Implement using UI Automation or browser-specific APIs
        return ""
    
    def get_youtube_video_time(self, url: str) -> Optional[float]:
        """Get current YouTube video time (Windows implementation limited)."""
        logging.warning("⚠️ YouTube video time detection not implemented for Windows")
        # TODO: Implement using browser automation
        return None
