@echo off
REM Spú<PERSON><PERSON><PERSON><PERSON> script pre Subtitle Reader na Windows ARM64
echo ========================================
echo   Subtitle Reader - Spúšťanie aplikácie
echo ========================================
echo.

REM Nastavenie premenných prostredia
set TESSERACT_CMD=C:\Program Files\Tesseract-OCR\tesseract.exe
set PYTHONIOENCODING=utf-8

REM Kontrola virtuálneho prostredia
if not exist .venv (
    echo ERROR: Virtuálne prostredie .venv neexistuje
    echo Spustite najprv setup_windows.bat
    pause
    exit /b 1
)

REM Kontrola Python
if not exist .venv\Scripts\python.exe (
    echo ERROR: Python nie je nájdený vo virtuálnom prostredí
    echo Spustite najprv setup_windows.bat
    pause
    exit /b 1
)

echo ✅ Spúšťam Subtitle Reader...
echo.

REM Aktivácia virtuálneho prostredia a spustenie aplikácie
call .venv\Scripts\activate.bat
if errorlevel 1 (
    echo ERROR: Nepodarilo sa aktivovať virtuálne prostredie
    pause
    exit /b 1
)

echo 🖥️ Spúšťam GUI aplikáciu...
python main_qt.py

REM Ak sa aplikácia ukončí s chybou, zobraz chybovú správu
if errorlevel 1 (
    echo.
    echo ❌ Aplikácia sa ukončila s chybou
    echo Skontrolujte app.log pre viac informácií
    pause
)
