<?php
/**
 * Import databázy cez PHP skript
 * Spustite: php import_database.php
 */

// Databázové nastavenia
$host = 'sql20.hostcreators.sk';
$port = '3325';
$dbname = 'd52810_voxoloxo';
$username = 'u52810_radis';
$password = '<PERSON><PERSON><PERSON><PERSON>sky1975?';

echo "🔧 Importujem databázu pre Subtitle Reader...\n";
echo "📊 Databáza: {$dbname}\n";
echo "👤 Užívateľ: {$username}\n\n";

try {
    // Pripojenie k databáze
    echo "🔗 Pripájam sa k databáze...\n";
    $pdo = new PDO("mysql:host={$host};port={$port};dbname={$dbname};charset=utf8mb4", $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    echo "✅ Pripojenie úspešné!\n\n";

    // Načítanie SQL súboru
    $sqlFile = 'www/database/schema.sql';
    if (!file_exists($sqlFile)) {
        throw new Exception("SQL súbor nenájdený: {$sqlFile}");
    }
    
    echo "📄 Načítavam SQL súbor: {$sqlFile}\n";
    $sql = file_get_contents($sqlFile);
    
    // Odstránenie USE príkazu (už sme pripojení k databáze)
    $sql = preg_replace('/^USE\s+[^;]+;/m', '', $sql);
    
    // Rozdelenie na jednotlivé príkazy
    $statements = array_filter(
        array_map('trim', explode(';', $sql)),
        function($stmt) {
            return !empty($stmt) && !preg_match('/^--/', $stmt);
        }
    );
    
    echo "🔧 Našiel som " . count($statements) . " SQL príkazov\n\n";
    
    // Vykonanie príkazov
    $pdo->beginTransaction();
    
    $success = 0;
    $errors = 0;
    
    foreach ($statements as $i => $statement) {
        try {
            if (trim($statement)) {
                $pdo->exec($statement);
                $success++;
                
                // Zobrazenie progressu
                if (preg_match('/CREATE TABLE\s+(\w+)/i', $statement, $matches)) {
                    echo "✅ Vytvorená tabuľka: {$matches[1]}\n";
                } elseif (preg_match('/INSERT INTO\s+(\w+)/i', $statement, $matches)) {
                    echo "📝 Vložené dáta do: {$matches[1]}\n";
                } elseif (preg_match('/CREATE\s+(VIEW|INDEX|TRIGGER|PROCEDURE|EVENT)/i', $statement, $matches)) {
                    echo "🔧 Vytvorené: {$matches[1]}\n";
                }
            }
        } catch (PDOException $e) {
            $errors++;
            echo "❌ Chyba v príkaze " . ($i + 1) . ": " . $e->getMessage() . "\n";
            
            // Pokračujeme aj pri chybách (niektoré môžu byť neškodné)
            if (strpos($e->getMessage(), 'already exists') === false && 
                strpos($e->getMessage(), 'Duplicate') === false) {
                // Len vážne chyby
                echo "⚠️  Vážna chyba, prerušujem...\n";
                throw $e;
            }
        }
    }
    
    $pdo->commit();
    
    echo "\n🎉 Import dokončený!\n";
    echo "✅ Úspešných príkazov: {$success}\n";
    echo "⚠️  Chýb: {$errors}\n\n";
    
    // Kontrola vytvorených tabuliek
    echo "📊 Kontrola vytvorených tabuliek:\n";
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    
    $expectedTables = [
        'users', 'licenses', 'license_logs', 'stripe_payments', 
        'email_verification_tokens', 'password_reset_tokens', 
        'usage_stats', 'app_config'
    ];
    
    foreach ($expectedTables as $table) {
        if (in_array($table, $tables)) {
            echo "✅ {$table}\n";
        } else {
            echo "❌ {$table} - CHÝBA!\n";
        }
    }
    
    // Kontrola views
    echo "\n📋 Kontrola views:\n";
    $views = $pdo->query("SHOW FULL TABLES WHERE Table_type = 'VIEW'")->fetchAll(PDO::FETCH_COLUMN);
    $expectedViews = ['active_licenses', 'license_usage_summary'];
    
    foreach ($expectedViews as $view) {
        if (in_array($view, $views)) {
            echo "✅ {$view}\n";
        } else {
            echo "❌ {$view} - CHÝBA!\n";
        }
    }
    
    // Kontrola konfigurácie
    echo "\n⚙️  Kontrola konfigurácie:\n";
    $configCount = $pdo->query("SELECT COUNT(*) FROM app_config")->fetchColumn();
    echo "📝 Konfiguračných záznamov: {$configCount}\n";
    
    if ($configCount > 0) {
        echo "✅ Základná konfigurácia načítaná\n";
    } else {
        echo "⚠️  Žiadna konfigurácia - možno chyba pri importe\n";
    }
    
    echo "\n🚀 Databáza je pripravená na použitie!\n";
    echo "🔗 Teraz môžete spustiť: https://vasa-domena.com/install/setup.php\n";
    
} catch (Exception $e) {
    if (isset($pdo)) {
        $pdo->rollBack();
    }
    echo "\n❌ CHYBA: " . $e->getMessage() . "\n";
    echo "\n🔧 Možné riešenia:\n";
    echo "1. Skontrolujte pripojenie k databáze\n";
    echo "2. Overte oprávnenia užívateľa\n";
    echo "3. Skontrolujte, či databáza existuje\n";
    echo "4. Skúste import cez phpMyAdmin\n";
    exit(1);
}
?>
