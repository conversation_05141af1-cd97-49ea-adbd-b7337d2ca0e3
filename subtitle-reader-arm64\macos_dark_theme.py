#!/usr/bin/env python3
"""
macOS-inšpirovaná tmavá téma pre Subtitle Reader
"""

# macOS Dark Mode Color Palette
MACOS_DARK_COLORS = {
    # Základné farby pozadia
    'window_bg': '#1c1c1e',           # <PERSON><PERSON><PERSON><PERSON> pozadie okna
    'content_bg': '#2c2c2e',         # Pozadie obsahu/panelu
    'sidebar_bg': '#28282a',         # Postranný panel
    'card_bg': '#3a3a3c',            # Karty/skupiny
    'input_bg': '#48484a',           # Input polia
    
    # Textové farby
    'text_primary': '#ffffff',        # Hlavný text
    'text_secondary': '#ebebf5',      # Sekundárny text
    'text_tertiary': 'rgba(235,235,245,153)',     # Terci<PERSON>rny text (60% opacity)
    'text_disabled': '#ebebf54d',     # <PERSON><PERSON><PERSON><PERSON><PERSON> text (30% opacity)
    'text_placeholder': '#ebebf54d',  # Placeholder text
    
    # Systé<PERSON><PERSON><PERSON> farby
    'accent_blue': '#007aff',         # <PERSON><PERSON><PERSON> mod<PERSON> (tlačidlá)
    'accent_blue_hover': '#0051d5',   # Modrá hover
    'accent_blue_pressed': '#003d99', # Modrá pressed
    
    'success_green': '#30d158',       # Zelená (úspech)
    'warning_orange': '#ff9f0a',      # Oranžová (varovanie)
    'error_red': '#ff453a',           # Červená (chyba)
    'info_purple': '#bf5af2',         # Fialová (info)
    
    # Špeciálne farby
    'separator': '#38383a',           # Oddeľovače
    'border': '#48484a',              # Okraje
    'shadow': '#00000040',            # Tiene (25% opacity)
    'overlay': '#00000080',           # Overlay (50% opacity)
    
    # Slider farby
    'slider_track': '#48484a',        # Dráha slidera
    'slider_handle': '#ffffff',       # Rukoväť slidera
    'slider_fill': '#007aff',         # Vyplnená časť
    
    # Tab farby
    'tab_active': '#007aff',          # Aktívny tab
    'tab_inactive': '#8e8e93',        # Neaktívny tab
    'tab_bg': '#2c2c2e',              # Pozadie tabu
}

def get_main_window_style():
    """Hlavný štýl pre okno aplikácie"""
    return f"""
    QMainWindow {{
        background-color: {MACOS_DARK_COLORS['window_bg']};
        color: {MACOS_DARK_COLORS['text_primary']};
    }}
    
    QWidget {{
        background-color: {MACOS_DARK_COLORS['window_bg']};
        color: {MACOS_DARK_COLORS['text_primary']};
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }}
    
    /* Tabuľky */
    QTabWidget::pane {{
        border: 1px solid {MACOS_DARK_COLORS['separator']};
        background-color: {MACOS_DARK_COLORS['content_bg']};
        border-radius: 8px;
    }}
    
    QTabBar::tab {{
        background-color: {MACOS_DARK_COLORS['tab_bg']};
        color: {MACOS_DARK_COLORS['tab_inactive']};
        padding: 8px 16px;
        margin-right: 2px;
        border-top-left-radius: 6px;
        border-top-right-radius: 6px;
        font-weight: 500;
    }}
    
    QTabBar::tab:selected {{
        background-color: {MACOS_DARK_COLORS['content_bg']};
        color: {MACOS_DARK_COLORS['tab_active']};
        font-weight: 600;
    }}
    
    QTabBar::tab:hover:!selected {{
        background-color: {MACOS_DARK_COLORS['card_bg']};
        color: {MACOS_DARK_COLORS['text_secondary']};
    }}
    
    /* Labely */
    QLabel {{
        color: {MACOS_DARK_COLORS['text_primary']};
        background-color: transparent;
    }}
    
    /* Tlačidlá */
    QPushButton {{
        background-color: {MACOS_DARK_COLORS['accent_blue']};
        color: {MACOS_DARK_COLORS['text_primary']};
        border: none;
        border-radius: 6px;
        padding: 8px 16px;
        font-weight: 500;
        font-size: 13px;
    }}
    
    QPushButton:hover {{
        background-color: {MACOS_DARK_COLORS['accent_blue_hover']};
    }}
    
    QPushButton:pressed {{
        background-color: {MACOS_DARK_COLORS['accent_blue_pressed']};
    }}
    
    QPushButton:disabled {{
        background-color: {MACOS_DARK_COLORS['card_bg']};
        color: {MACOS_DARK_COLORS['text_disabled']};
    }}
    
    /* Slidery */
    QSlider::groove:horizontal {{
        border: none;
        height: 4px;
        background-color: {MACOS_DARK_COLORS['slider_track']};
        border-radius: 2px;
    }}
    
    QSlider::handle:horizontal {{
        background-color: {MACOS_DARK_COLORS['slider_handle']};
        border: none;
        width: 16px;
        height: 16px;
        margin: -6px 0;
        border-radius: 8px;
    }}
    
    QSlider::handle:horizontal:hover {{
        background-color: {MACOS_DARK_COLORS['text_secondary']};
    }}
    
    QSlider::sub-page:horizontal {{
        background-color: {MACOS_DARK_COLORS['slider_fill']};
        border-radius: 2px;
    }}
    
    /* Combo boxy */
    QComboBox {{
        background-color: {MACOS_DARK_COLORS['input_bg']};
        color: {MACOS_DARK_COLORS['text_primary']};
        border: 1px solid {MACOS_DARK_COLORS['border']};
        border-radius: 6px;
        padding: 6px 12px;
        font-size: 13px;
    }}
    
    QComboBox:hover {{
        border-color: {MACOS_DARK_COLORS['accent_blue']};
    }}
    
    QComboBox::drop-down {{
        border: none;
        width: 20px;
    }}
    
    QComboBox::down-arrow {{
        image: none;
        border-left: 4px solid transparent;
        border-right: 4px solid transparent;
        border-top: 4px solid {MACOS_DARK_COLORS['text_secondary']};
        margin-right: 8px;
    }}
    
    QComboBox QAbstractItemView {{
        background-color: {MACOS_DARK_COLORS['content_bg']};
        color: {MACOS_DARK_COLORS['text_primary']};
        border: 1px solid {MACOS_DARK_COLORS['border']};
        border-radius: 6px;
        selection-background-color: {MACOS_DARK_COLORS['accent_blue']};
    }}
    
    /* Scroll oblasti */
    QScrollArea {{
        background-color: {MACOS_DARK_COLORS['content_bg']};
        border: none;
        border-radius: 8px;
    }}
    
    QScrollBar:vertical {{
        background-color: transparent;
        width: 8px;
        border-radius: 4px;
    }}
    
    QScrollBar::handle:vertical {{
        background-color: {MACOS_DARK_COLORS['border']};
        border-radius: 4px;
        min-height: 20px;
    }}
    
    QScrollBar::handle:vertical:hover {{
        background-color: {MACOS_DARK_COLORS['text_tertiary']};
    }}
    
    QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
        height: 0px;
    }}
    
    /* Skupiny */
    QGroupBox {{
        color: {MACOS_DARK_COLORS['text_primary']};
        border: 1px solid {MACOS_DARK_COLORS['separator']};
        border-radius: 8px;
        margin-top: 8px;
        padding-top: 8px;
        font-weight: 600;
        font-size: 13px;
    }}
    
    QGroupBox::title {{
        subcontrol-origin: margin;
        left: 12px;
        padding: 0 8px 0 8px;
        color: {MACOS_DARK_COLORS['text_primary']};
        background-color: {MACOS_DARK_COLORS['content_bg']};
    }}
    """

def get_button_styles():
    """Špecifické štýly pre rôzne typy tlačidiel"""
    return {
        'primary': f"""
            QPushButton {{
                background-color: {MACOS_DARK_COLORS['accent_blue']};
                color: {MACOS_DARK_COLORS['text_primary']};
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: 600;
                font-size: 14px;
            }}
            QPushButton:hover {{
                background-color: {MACOS_DARK_COLORS['accent_blue_hover']};
            }}
            QPushButton:pressed {{
                background-color: {MACOS_DARK_COLORS['accent_blue_pressed']};
            }}
        """,
        
        'success': f"""
            QPushButton {{
                background-color: {MACOS_DARK_COLORS['success_green']};
                color: {MACOS_DARK_COLORS['text_primary']};
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: 600;
                font-size: 14px;
            }}
            QPushButton:hover {{
                background-color: #28cd4c;
            }}
            QPushButton:pressed {{
                background-color: #20b142;
            }}
        """,
        
        'danger': f"""
            QPushButton {{
                background-color: {MACOS_DARK_COLORS['error_red']};
                color: {MACOS_DARK_COLORS['text_primary']};
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: 600;
                font-size: 14px;
            }}
            QPushButton:hover {{
                background-color: #ff3b30;
            }}
            QPushButton:pressed {{
                background-color: #d70015;
            }}
        """,
        
        'secondary': f"""
            QPushButton {{
                background-color: {MACOS_DARK_COLORS['card_bg']};
                color: {MACOS_DARK_COLORS['text_primary']};
                border: 1px solid {MACOS_DARK_COLORS['border']};
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: 500;
                font-size: 13px;
            }}
            QPushButton:hover {{
                background-color: {MACOS_DARK_COLORS['input_bg']};
                border-color: {MACOS_DARK_COLORS['accent_blue']};
            }}
            QPushButton:pressed {{
                background-color: {MACOS_DARK_COLORS['border']};
            }}
        """
    }

def get_text_styles():
    """Štýly pre rôzne typy textu"""
    return {
        'title': f"color: {MACOS_DARK_COLORS['text_primary']}; font-size: 18px; font-weight: 700; margin: 12px 0;",
        'subtitle': f"color: {MACOS_DARK_COLORS['text_secondary']}; font-size: 15px; font-weight: 600; margin: 8px 0;",
        'body': f"color: {MACOS_DARK_COLORS['text_primary']}; font-size: 13px; font-weight: 400;",
        'caption': f"color: {MACOS_DARK_COLORS['text_tertiary']}; font-size: 11px; font-weight: 400; font-style: italic;",
        'value': f"color: {MACOS_DARK_COLORS['text_primary']}; font-size: 13px; font-weight: 600; min-width: 40px;",
        'success': f"color: {MACOS_DARK_COLORS['success_green']}; font-weight: 600;",
        'warning': f"color: {MACOS_DARK_COLORS['text_tertiary']}; font-weight: 600;",
        'error': f"color: {MACOS_DARK_COLORS['error_red']}; font-weight: 600;",
    }

def get_overlay_style():
    """Štýl pre overlay elementy (ako hotkey hint)"""
    return f"""
        color: {MACOS_DARK_COLORS['text_primary']};
        background: rgba(28, 28, 30, 0.95);
        border: 1px solid {MACOS_DARK_COLORS['separator']};
        border-radius: 6px;
        padding: 6px 12px;
        font-size: 11px;
        font-weight: 500;
    """
