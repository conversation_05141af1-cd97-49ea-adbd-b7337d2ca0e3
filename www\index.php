<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Už nemusíte číst titulky! Naše čtečka titulků Vám je přečte. VOXO LOXO - Multiplatformní řešení pro Windows, macOS a Linux.">
    <meta name="keywords" content="čtečka titulků, OCR, TTS, Windows, macOS, Linux, multiplatformní, titulky, čtečka">
    <title>Už nemusíte číst titulky! VOXO LOXO čtečka je přečte | Windows, macOS, Linux</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .hero-section {
            background:
                linear-gradient(rgba(25, 42, 86, 0.4), rgba(13, 27, 62, 0.4)),
                url('hero.png') center/cover no-repeat fixed;
            color: white;
            padding: 120px 0;
            position: relative;
            min-height: 100vh;
            display: flex;
            align-items: center;
        }

        .hero-section .container {
            width: 100%;
            max-width: 1400px;
        }

        .main-headline {
            margin-bottom: 3rem !important;
        }

        .demo-badge {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            padding: 12px 25px;
            border-radius: 30px;
            font-size: 1.3rem;
            font-weight: 600;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
            display: inline-block;
            margin-bottom: 2rem;
        }

        .hero-section .btn {
            font-size: 1.2rem;
            padding: 15px 35px;
            margin: 10px;
            border-radius: 30px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .feature-icon {
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 1rem;
        }
        .price-card {
            border: 3px solid #667eea;
            border-radius: 15px;
            transition: transform 0.3s;
        }
        .price-card:hover {
            transform: translateY(-10px);
        }





        .main-headline {
            animation: fadeInUp 1.2s ease-out;
        }

        .main-headline h1 {
            animation: slideInLeft 1s ease-out 0.3s both;
            line-height: 1.1;
        }

        .main-headline h2 {
            animation: slideInRight 1s ease-out 0.6s both;
            line-height: 1.3;
        }

        .brand-badge {
            animation: bounceIn 1s ease-out 0.9s both;
        }

        .brand-badge .mt-2 {
            animation: fadeIn 1s ease-out 1.2s both;
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-100px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(100px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes bounceIn {
            0% {
                opacity: 0;
                transform: scale(0.3);
            }
            50% {
                opacity: 1;
                transform: scale(1.05);
            }
            70% {
                transform: scale(0.9);
            }
            100% {
                opacity: 1;
                transform: scale(1);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }

        /* FAQ sekcia so statickým pozadím */
        .faq-section {
            background:
                rgba(248, 249, 250, 0.9),
                url('faq.png') center/cover no-repeat fixed;
            position: relative;
            min-height: 100vh;
        }

        .faq-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('faq.png') center/cover no-repeat fixed;
            z-index: -1;
        }

        .faq-section .container {
            position: relative;
            z-index: 1;
            background: rgba(255, 255, 255, 0.5);
            border-radius: 15px;
            padding: 40px;
            margin-top: 50px;
            margin-bottom: 50px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }

        /* Podporované platformy sekcia s overlay.png */
        .platforms-section {
            background: url('overlay.png') center/cover no-repeat;
            position: relative;
            min-height: 500px;
        }

        /* Download sekcia s download.png */
        .download-section {
            background:
                linear-gradient(rgba(248, 249, 250, 0.1), rgba(248, 249, 250, 0.1)),
                url('download.png') center/cover no-repeat;
            position: relative;
            min-height: 600px;
            color: white;
        }

        .download-section h2,
        .download-section p {
            text-shadow: 2px 2px 4px rgba(0,0,0,0.7);
        }

        /* OS detekcia a zvýraznenie */
        .platform-card {
            transition: all 0.3s ease;
            border-radius: 10px;
            padding: 20px;
            margin: 10px 0;
            background: rgba(128, 128, 128, 0.5);
            border: 2px solid transparent;
            color: white;
        }

        .download-card {
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        /* Konzistentné šírky download tlačidiel */
        .download-btn {
            width: 180px;
            white-space: nowrap;
            display: block;
            margin: 0 auto;
            text-align: center;
        }

        /* Windows zvýraznenie */
        .current-os-windows {
            border: 3px solid #007bff !important;
            box-shadow: 0 0 20px rgba(0, 123, 255, 0.4) !important;
            transform: scale(1.02) !important;
        }

        /* macOS zvýraznenie */
        .current-os-macos {
            border: 3px solid #28a745 !important;
            box-shadow: 0 0 20px rgba(40, 167, 69, 0.4) !important;
            transform: scale(1.02) !important;
        }

        /* Linux zvýraznenie */
        .current-os-linux {
            border: 3px solid #ffc107 !important;
            box-shadow: 0 0 20px rgba(255, 193, 7, 0.4) !important;
            transform: scale(1.02) !important;
        }

        /* Platform cards zvýraznenie - OS farby */
        .platform-card.current-os-windows {
            background: rgba(0, 123, 255, 0.7) !important;
            color: white !important;
        }

        .platform-card.current-os-macos {
            background: rgba(40, 167, 69, 0.7) !important;
            color: white !important;
        }

        .platform-card.current-os-linux {
            background: rgba(255, 193, 7, 0.7) !important;
            color: white !important;
        }

        /* Download cards zvýraznenie - 70% priehľadnosť + biele texty */
        .download-card.current-os-windows {
            background: rgba(0, 123, 255, 0.7) !important;
        }

        .download-card.current-os-windows .card-body,
        .download-card.current-os-windows .card-body h4,
        .download-card.current-os-windows .card-body p,
        .download-card.current-os-windows .card-body small {
            color: white !important;
        }

        .download-card.current-os-macos {
            background: rgba(40, 167, 69, 0.7) !important;
        }

        .download-card.current-os-macos .card-body,
        .download-card.current-os-macos .card-body h4,
        .download-card.current-os-macos .card-body p,
        .download-card.current-os-macos .card-body small {
            color: white !important;
        }

        .download-card.current-os-linux {
            background: rgba(255, 193, 7, 0.7) !important;
        }

        .download-card.current-os-linux .card-body,
        .download-card.current-os-linux .card-body h4,
        .download-card.current-os-linux .card-body p,
        .download-card.current-os-linux .card-body small {
            color: white !important;
        }

        /* Farebné názvy OS pre neaktívne karty */
        .download-card[data-os="windows"]:not(.current-os-windows) h4 {
            color: #007bff !important;
        }

        .download-card[data-os="macos"]:not(.current-os-macos) h4 {
            color: #28a745 !important;
        }

        .download-card[data-os="linux"]:not(.current-os-linux) h4 {
            color: #ffc107 !important;
        }

        .os-indicator {
            position: absolute;
            top: -10px;
            right: -10px;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        /* OS-specific indicator colors */
        .current-os-windows .os-indicator {
            background: #007bff;
        }

        .current-os-macos .os-indicator {
            background: #28a745;
        }

        .current-os-linux .os-indicator {
            background: #ffc107;
        }

        /* OS-specific alert colors */
        .os-alert-windows {
            background-color: #007bff !important;
            border-color: #0056b3 !important;
            color: white !important;
        }

        .os-alert-macos {
            background-color: #28a745 !important;
            border-color: #1e7e34 !important;
            color: white !important;
        }

        .os-alert-linux {
            background-color: #ffc107 !important;
            border-color: #e0a800 !important;
            color: white !important;
        }

        /* Responzívne úpravy pre hero sekciu */
        @media (max-width: 768px) {
            .hero-section {
                padding: 80px 0;
                min-height: 90vh;
                /* Mobilné zariadenia - scroll attachment */
                background-attachment: scroll !important;
            }

            .faq-section,
            .faq-section::before {
                /* Mobilné zariadenia - scroll attachment */
                background-attachment: scroll !important;
            }

            .hero-section h1 {
                font-size: 2.5rem !important;
            }

            .hero-section h2 {
                font-size: 1.8rem !important;
            }

            .badge {
                font-size: 1.8rem !important;
                padding: 10px 20px !important;
            }

            .hero-section .text-white {
                font-size: 1.3rem !important;
            }

            .demo-badge {
                font-size: 1.1rem;
                padding: 10px 20px;
            }

            .hero-section .btn {
                font-size: 1rem;
                padding: 12px 25px;
                margin: 8px;
            }
        }

        @media (max-width: 576px) {
            .hero-section h1 {
                font-size: 2rem !important;
            }

            .hero-section h2 {
                font-size: 1.4rem !important;
            }

            .badge {
                font-size: 1.5rem !important;
                padding: 8px 16px !important;
            }

            .hero-section .text-white {
                font-size: 1.1rem !important;
            }
        }

        /* iOS Safari špecifické opravy */
        @supports (-webkit-touch-callout: none) {
            .hero-section,
            .faq-section,
            .faq-section::before {
                background-attachment: scroll !important;
            }
        }

        /* Detekcia iOS zariadení */
        @media screen and (-webkit-min-device-pixel-ratio: 2) and (max-device-width: 1024px) {
            .hero-section,
            .faq-section,
            .faq-section::before {
                background-attachment: scroll !important;
            }
        }

        /* Responzívne úpravy pre hlavnú hlášku */
        @media (max-width: 768px) {
            .main-headline h1 {
                font-size: 2.5rem !important;
                line-height: 1.2;
            }

            .main-headline h2 {
                font-size: 1.5rem !important;
                line-height: 1.4;
            }

            .brand-badge .badge {
                font-size: 1.2rem !important;
                padding: 10px 20px !important;
            }

            .brand-badge .fs-5 {
                font-size: 1rem !important;
            }

            .hero-section {
                padding: 80px 0 !important;
            }
        }

        @media (max-width: 576px) {
            .main-headline h1 {
                font-size: 2rem !important;
            }

            .main-headline h2 {
                font-size: 1.2rem !important;
            }

            .brand-badge .badge {
                font-size: 1rem !important;
                padding: 8px 16px !important;
            }

            .brand-badge .fs-5 {
                font-size: 0.9rem !important;
            }

            .faq-section .container {
                margin-top: 20px !important;
                margin-bottom: 20px !important;
                padding: 20px !important;
            }
        }

        .os-recommendation {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            margin: 10px 0;
            font-weight: bold;
            display: none;
        }

        .os-recommendation.show {
            display: block;
            animation: slideInDown 0.5s ease-out;
        }

        @keyframes slideInDown {
            from { opacity: 0; transform: translateY(-20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container">
            <a class="navbar-brand" href="#"><i class="fas fa-eye"></i> VOXO LOXO</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item"><a class="nav-link" href="#features">Funkce</a></li>
                    <li class="nav-item"><a class="nav-link" href="#pricing">Ceny</a></li>
                    <li class="nav-item"><a class="nav-link" href="#download">Stahování</a></li>
                    <li class="nav-item"><a class="nav-link" href="purchase/register.php">Koupit</a></li>
                    <li class="nav-item"><a class="nav-link" href="create-placeholder-images.html" title="Vytvořit obrázky">🎨</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Úvodní sekce -->
    <section class="hero-section">
        <div class="container text-center">
            <!-- Hlavní hláška -->
            <div class="main-headline mb-5">
                <h1 class="display-1 fw-bold text-white mb-4" style="text-shadow: 3px 3px 6px rgba(0,0,0,0.8); font-size: 4.5rem;">
                    <i class="fas fa-search me-4" style="color: white;"></i>
                    Už nemusíte číst titulky!
                </h1>
                <h2 class="display-3 fw-bold text-white mb-5" style="font-size: 3rem; text-shadow: 3px 3px 6px rgba(0,0,0,0.8);">
                    Naše čtečka titulků Vám je přečte
                </h2>
                <div class="brand-badge">
                    <span class="badge bg-light text-dark mb-3" style="font-size: 2.5rem; padding: 15px 30px; border-radius: 25px; box-shadow: 0 4px 15px rgba(0,0,0,0.2);">
                        VOXO LOXO
                    </span>
                </div>
            </div>
            <div class="mb-4">
                <span class="demo-badge">20 minut denně ZDARMA</span>
            </div>
            <a href="#download" class="btn btn-light btn-lg me-3">
                <i class="fas fa-download"></i> Stáhnout Demo
            </a>
            <a href="purchase/register.php" class="btn btn-outline-light btn-lg">
                <i class="fas fa-shopping-cart"></i> Koupit na rok
            </a>
        </div>
    </section>

    <!-- Sekce funkcí -->
    <section id="features" class="py-5">
        <div class="container">
            <h2 class="text-center mb-5">Klíčové funkce</h2>
            <div class="row">
                <div class="col-md-4 text-center mb-4">
                    <i class="fas fa-eye feature-icon"></i>
                    <h4>Pokročilé OCR</h4>
                    <p>Tesseract OCR s podporou 30+ jazyků pro přesné rozpoznávání textu z obrazovky.</p>
                </div>
                <div class="col-md-4 text-center mb-4">
                    <i class="fas fa-volume-up feature-icon"></i>
                    <h4>Multiplatformní TTS</h4>
                    <p>Apple TTS (macOS), Windows SAPI, Linux espeak - kvalitní hlasy na všech platformách.</p>
                </div>
                <div class="col-md-4 text-center mb-4">
                    <i class="fas fa-magic feature-icon"></i>
                    <h4>Inteligentní detekce</h4>
                    <p>Pokročilé algoritmy pro automatickou detekci a filtrování titulků.</p>
                </div>
                <div class="col-md-4 text-center mb-4">
                    <i class="fas fa-cogs feature-icon"></i>
                    <h4>Více režimů</h4>
                    <p>Statický, dynamický, automatický a YouTube režim pro různé typy obsahu.</p>
                </div>
                <div class="col-md-4 text-center mb-4">
                    <i class="fas fa-keyboard feature-icon"></i>
                    <h4>Chytré klávesové zkratky</h4>
                    <p>Automatické mapování klávesových zkratek - Cmd (macOS) ↔ Ctrl (Windows/Linux).</p>
                </div>
                <div class="col-md-4 text-center mb-4">
                    <i class="fas fa-desktop feature-icon"></i>
                    <h4>Nativní integrace</h4>
                    <p>Optimalizace pro každou platformu - macOS AppKit, Windows WMI, Linux X11.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Sekce podpory platforem -->
    <section class="py-5 platforms-section text-white">
        <div class="container">
            <h2 class="text-center mb-5">Podporované platformy</h2>
            <div class="row">
                <div class="col-md-4 text-center mb-4">
                    <div class="platform-card" data-os="windows">
                        <i class="fab fa-windows" style="font-size: 4rem; margin-bottom: 1rem;"></i>
                        <h4>Windows 10+</h4>
                    <ul class="list-unstyled text-center">
                        <li><i class="fas fa-check"></i> Tesseract OCR</li>
                        <li><i class="fas fa-check"></i> Windows SAPI TTS</li>
                        <li><i class="fas fa-check"></i> WMI integrace</li>
                        <li><i class="fas fa-check"></i> Ctrl+Alt+Ctrl klávesy</li>
                    </ul>
                    </div>
                </div>
                <div class="col-md-4 text-center mb-4">
                    <div class="platform-card" data-os="macos">
                        <i class="fab fa-apple" style="font-size: 4rem; margin-bottom: 1rem;"></i>
                        <h4>macOS 10.15+</h4>
                    <ul class="list-unstyled text-center">
                        <li><i class="fas fa-check"></i> Tesseract OCR</li>
                        <li><i class="fas fa-check"></i> Apple TTS (Enhanced/Premium)</li>
                        <li><i class="fas fa-check"></i> AppKit integrace</li>
                        <li><i class="fas fa-check"></i> Cmd+Alt+Ctrl klávesy</li>
                    </ul>
                    </div>
                </div>
                <div class="col-md-4 text-center mb-4">
                    <div class="platform-card" data-os="linux">
                        <i class="fab fa-linux" style="font-size: 4rem; margin-bottom: 1rem;"></i>
                        <h4>Linux Ubuntu 20.04+</h4>
                    <ul class="list-unstyled text-center">
                        <li><i class="fas fa-check"></i> Tesseract OCR</li>
                        <li><i class="fas fa-check"></i> espeak/pyttsx3 TTS</li>
                        <li><i class="fas fa-check"></i> X11 integrace</li>
                        <li><i class="fas fa-check"></i> Ctrl+Alt+Ctrl klávesy</li>
                    </ul>
                    </div>
                </div>
            </div>
            <div class="text-center mt-4">
                <p class="lead">Jedna licence funguje vždy pro jednu platformu!</p>
            </div>
        </div>
    </section>

    <!-- Sekce cen -->
    <section id="pricing" class="py-5 bg-light">
        <div class="container">
            <h2 class="text-center mb-5">Cenové plány</h2>
            <div class="row justify-content-center">
                <div class="col-md-4 mb-4">
                    <div class="card price-card h-100">
                        <div class="card-body text-center">
                            <h3 class="card-title">Demo verze</h3>
                            <div class="display-4 text-primary mb-3">Zdarma</div>
                            <p class="text-muted">20 minut denně</p>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success"></i> Všechny funkce</li>
                                <li><i class="fas fa-check text-success"></i> OCR + TTS</li>
                                <li><i class="fas fa-check text-success"></i> Inteligentní detekce</li>
                                <li><i class="fas fa-clock text-warning"></i> Časové omezení</li>
                            </ul>
                            <a href="#download" class="btn btn-outline-primary">Stáhnout</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="card price-card h-100 border-primary">
                        <div class="card-header bg-primary text-white text-center">
                            <h4>Doporučeno</h4>
                        </div>
                        <div class="card-body text-center">
                            <h3 class="card-title">Plná verze</h3>
                            <div class="display-4 text-primary mb-3">299 Kč</div>
                            <p class="text-muted">na celý rok</p>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success"></i> Všechny funkce</li>
                                <li><i class="fas fa-check text-success"></i> Neomezené používání</li>
                                <li><i class="fas fa-check text-success"></i> Premium podpora</li>
                                <li><i class="fas fa-check text-success"></i> Budoucí aktualizace</li>
                            </ul>
                            <a href="purchase/register.php" class="btn btn-primary btn-lg">Koupit nyní</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Sekce stahování -->
    <section id="download" class="py-5 download-section">
        <div class="container text-center">
            <h2 class="mb-4 text-white">Stáhnout aplikaci</h2>
            <p class="lead mb-4 text-white">Multiplatformní aplikace pro Windows, macOS a Linux</p>
            <div class="row justify-content-center">
                <div class="col-md-4 mb-4">
                    <div class="card h-100 download-card" data-os="windows">
                        <div class="card-body">
                            <h4><i class="fab fa-windows text-primary"></i> Windows</h4>
                            <p>Subtitle Reader v2.0.0</p>
                            <div class="mb-3">
                                <a href="download/install-x86.bat" class="btn btn-primary btn-lg download-btn mb-1" download>
                                    <i class="fas fa-download"></i> x86 Install
                                </a>
                                <a href="download/install-arm64.bat" class="btn btn-primary btn-lg download-btn" download>
                                    <i class="fas fa-microchip"></i> ARM64 Install
                                </a>
                            </div>
                            <p class="text-muted">
                                <small>Windows 10+<br>
                                Jednosúborové inštalátory</small>
                            </p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="card h-100 border-success download-card" data-os="macos">
                        <div class="card-body">
                            <h4><i class="fab fa-apple text-success"></i> macOS</h4>
                            <p>Subtitle Reader v2.0.0</p>
                            <a href="downloads/SubtitleReader-2.0.0-macOS.dmg" class="btn btn-success btn-lg download-btn mb-2">
                                <i class="fas fa-download"></i> Stáhnout DMG
                            </a>
                            <p class="text-muted">
                                <small>macOS 10.15+, M1/M2 optimalizace<br>
                                Plně funkční verze</small>
                            </p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="card h-100 download-card" data-os="linux">
                        <div class="card-body">
                            <h4><i class="fab fa-linux text-warning"></i> Linux</h4>
                            <p>Subtitle Reader v2.0.0</p>
                            <a href="downloads/SubtitleReader-2.0.0-Linux.tar.gz" class="btn btn-warning btn-lg download-btn mb-2">
                                <i class="fas fa-download"></i> Stáhnout TAR.GZ
                            </a>
                            <p class="text-muted">
                                <small>Ubuntu 20.04+<br>
                                Plná funkcionalita</small>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row justify-content-center mt-4">
                <div class="col-md-10">
                    <div class="alert alert-info">
                        <h5 class="text-center"><i class="fas fa-info-circle"></i> Instalační pokyny</h5>
                        <div class="row">
                            <div class="col-md-4 text-center">
                                <strong style="color: #007bff;">Windows:</strong><br>
                                Spusťte <code>install-x86.bat</code> nebo <code>install-arm64.bat</code> ako správca
                            </div>
                            <div class="col-md-4 text-center">
                                <strong style="color: #28a745;">macOS:</strong><br>
                                Otevřete DMG a přesuňte aplikaci do Applications
                            </div>
                            <div class="col-md-4 text-center">
                                <strong style="color: #ffc107;">Linux:</strong><br>
                                Rozbalte archiv a spusťte <code>pip install -r requirements_linux.txt</code>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Sekce FAQ -->
    <section class="py-5 faq-section">
        <div class="container">
            <h2 class="text-center mb-5">Časté otázky</h2>
            <div class="row justify-content-center">
                <div class="col-md-8 col-lg-6">
                    <div class="accordion" id="faqAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                                    Jak funguje demo verze?
                                </button>
                            </h2>
                            <div id="faq1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    Demo verze umožňuje používat všechny funkce aplikace po dobu 20 minut denně.
                                    Čas se resetuje každý den o půlnoci.
                                </div>
                            </div>
                        </div>
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                                    Jaké jazyky jsou podporovány?
                                </button>
                            </h2>
                            <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    OCR podporuje 30+ jazyků včetně češtiny, slovenštiny, angličtiny, němčiny a francouzštiny.
                                    TTS podporuje hlasy na všech platformách: Apple TTS (macOS), Windows SAPI, Linux espeak.
                                </div>
                            </div>
                        </div>
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
                                    Jak probíhá platba?
                                </button>
                            </h2>
                            <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    Platba probíhá bezpečně přes Stripe. Podporujeme karty Visa, Mastercard a další. 
                                    Po úspěšné platbě se aplikace automaticky odemkne.
                                </div>
                            </div>
                        </div>
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq4">
                                    Funguje na všech platformách?
                                </button>
                            </h2>
                            <div id="faq4" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    Ano! Aplikace je plně multiplatformní. Jedna licence funguje vždy pro jednu platformu.
                                    Automaticky se adaptuje na každou platformu s optimalizovanými funkcemi.
                                </div>
                            </div>
                        </div>
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq5">
                                    Mohu používat na více počítačích?
                                </button>
                            </h2>
                            <div id="faq5" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    Licence je vázána na konkrétní počítač pomocí hardware fingerprinting.
                                    Pro použití na více počítačích je potřeba zakoupit více licencí.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Patička -->
    <footer class="bg-dark text-white py-4">
        <div class="container text-center">
            <p>&copy; 2024 VOXO LOXO. Všechna práva vyhrazena.</p>
            <p class="mb-2">
                <i class="fab fa-windows"></i> Windows •
                <i class="fab fa-apple"></i> macOS •
                <i class="fab fa-linux"></i> Linux
            </p>
            <p>
                <a href="mailto:<EMAIL>" class="text-white">
                    <i class="fas fa-envelope"></i> <EMAIL>
                </a>
            </p>
            <small class="text-muted">Multiplatformní aplikace s automatickou adaptací na každý systém</small>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Detekce operačního systému
        function detectOS() {
            const userAgent = navigator.userAgent.toLowerCase();
            const platform = navigator.platform.toLowerCase();

            if (userAgent.includes('win') || platform.includes('win')) {
                return 'windows';
            } else if (userAgent.includes('mac') || platform.includes('mac')) {
                return 'macos';
            } else if (userAgent.includes('linux') || platform.includes('linux') || userAgent.includes('x11')) {
                return 'linux';
            } else {
                return 'unknown';
            }
        }

        // Zvýraznění aktuálního OS
        function highlightCurrentOS() {
            const currentOS = detectOS();

            if (currentOS === 'unknown') return;

            console.log('Detekovaný OS:', currentOS);

            // Zvýrazni platformní karty
            const platformCards = document.querySelectorAll('.platform-card');
            platformCards.forEach(card => {
                if (card.dataset.os === currentOS) {
                    card.classList.add(`current-os-${currentOS}`);
                    card.style.position = 'relative';

                    // Přidej indikátor
                    const indicator = document.createElement('div');
                    indicator.className = 'os-indicator';
                    indicator.innerHTML = '✓';
                    indicator.title = 'Váš operační systém';
                    card.appendChild(indicator);
                }
            });

            // Zvýrazni download karty
            const downloadCards = document.querySelectorAll('.download-card');
            downloadCards.forEach(card => {
                if (card.dataset.os === currentOS) {
                    card.classList.add(`current-os-${currentOS}`);
                    card.style.position = 'relative';

                    // Přidej indikátor
                    const indicator = document.createElement('div');
                    indicator.className = 'os-indicator';
                    indicator.innerHTML = '✓';
                    indicator.title = 'Doporučené pro váš OS';
                    card.appendChild(indicator);

                    // Přidej doporučení s OS-specific barvou
                    const recommendation = document.createElement('div');
                    recommendation.className = `alert os-alert-${currentOS} mt-2 mb-0`;
                    recommendation.innerHTML = `
                        <i class="fas fa-check-circle me-2"></i>
                        <strong>Doporučené pro váš systém</strong>
                    `;
                    card.querySelector('.card-body').appendChild(recommendation);
                }
            });
        }

        // Automatické zavření menu při kliknutí na link nebo scroll
        document.addEventListener('DOMContentLoaded', function() {
            // Spusť OS detekci
            highlightCurrentOS();

            // OS detekcia pre download karty (už nie je potrebné upravovať tlačidlá)
            const currentOS = detectOS();

            // Přidej smooth scroll pro download tlačítka
            document.querySelectorAll('a[href="#download"]').forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    document.querySelector('#download').scrollIntoView({
                        behavior: 'smooth'
                    });
                });
            });

            // Automatické zavření menu při kliknutí na link nebo scroll
            const navbarCollapse = document.querySelector('.navbar-collapse');
            const navbarToggler = document.querySelector('.navbar-toggler');

            // Zavři menu při kliknutí na link
            document.querySelectorAll('.navbar-nav .nav-link').forEach(link => {
                link.addEventListener('click', function() {
                    if (navbarCollapse.classList.contains('show')) {
                        navbarToggler.click();
                    }
                });
            });

            // Zavři menu při scroll
            let scrollTimeout;
            window.addEventListener('scroll', function() {
                clearTimeout(scrollTimeout);
                scrollTimeout = setTimeout(function() {
                    if (navbarCollapse.classList.contains('show')) {
                        navbarToggler.click();
                    }
                }, 100);
            });

            // Zavři menu při kliknutí mimo menu
            document.addEventListener('click', function(event) {
                const navbar = document.querySelector('.navbar');
                if (!navbar.contains(event.target) && navbarCollapse.classList.contains('show')) {
                    navbarToggler.click();
                }
            });
        });
    </script>
</body>
</html>
