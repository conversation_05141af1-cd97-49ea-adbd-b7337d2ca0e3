#!/usr/bin/env python3
"""
Test OneCore TTS Provider
"""

import time
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_onecore_provider():
    """Test OneCore provider."""
    print("🚀 OneCore Provider Test")
    print("=" * 60)
    
    try:
        from onecore_tts_provider import get_onecore_provider
        
        # Get provider
        provider = get_onecore_provider()
        
        # Check availability
        if not provider.is_available():
            print("❌ OneCore provider nie je dostupný")
            return False
        
        print("✅ OneCore provider je dostupný")
        
        # Get voices
        voices = provider.get_available_voices()
        print(f"🎤 Nájdené hlasy: {len(voices)}")
        
        for i, voice in enumerate(voices):
            print(f"   {i+1}. {voice['name']}")
            print(f"      Jazyk: {voice['language']}")
            print(f"      Pohlavie: {voice['gender']}")
            print(f"      Kvalita: {voice['quality']}")
            print()
        
        if not voices:
            print("❌ Žiadne hlasy nenájdené")
            return False
        
        # Test speech synthesis
        print("🔍 Test syntézy reči...")
        
        # Test English voice
        en_voice = None
        sk_voice = None
        
        for voice in voices:
            if 'en-US' in voice['language']:
                en_voice = voice['name']
            elif 'sk-SK' in voice['language']:
                sk_voice = voice['name']
        
        # Test English
        if en_voice:
            print(f"🎤 Testovanie anglického hlasu: {en_voice}")
            success = provider.speak("Hello, this is a test of OneCore speech synthesis.", en_voice)
            if success:
                print("✅ Anglická syntéza spustená")
                time.sleep(4)  # Wait for speech
            else:
                print("❌ Anglická syntéza zlyhala")
        
        # Test Slovak
        if sk_voice:
            print(f"🎤 Testovanie slovenského hlasu: {sk_voice}")
            success = provider.speak("Ahoj, toto je test OneCore syntézy reči.", sk_voice)
            if success:
                print("✅ Slovenská syntéza spustená")
                time.sleep(4)  # Wait for speech
            else:
                print("❌ Slovenská syntéza zlyhala")
        
        # Test stop
        print("🔍 Test zastavenia...")
        stop_success = provider.stop()
        print(f"✅ Stop: {'úspešný' if stop_success else 'zlyhal'}")
        
        # Test voice selection by language
        print("🔍 Test výberu hlasu podľa jazyka...")
        
        test_languages = ['sk', 'en', 'cs', 'de', 'fr']
        for lang in test_languages:
            voice_name = provider.get_voice_for_language(lang)
            if voice_name:
                print(f"✅ {lang.upper()}: {voice_name}")
            else:
                print(f"❌ {lang.upper()}: žiadny hlas")
        
        print("\n" + "=" * 60)
        print("🎉 ONECORE PROVIDER TEST ÚSPEŠNÝ!")
        print("✅ OneCore hlasy sú funkčné")
        print("✅ Syntéza reči funguje")
        print("✅ Výber hlasov funguje")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Test zlyhal: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_onecore_provider()
    
    print("\n" + "=" * 60)
    print("📊 FINÁLNY VÝSLEDOK:")
    if success:
        print("🎉 ONECORE PROVIDER JE FUNKČNÝ!")
        print("🚀 Pripravený na integráciu do aplikácie")
    else:
        print("❌ OneCore provider test zlyhal")
    
    exit(0 if success else 1)
