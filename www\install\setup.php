<?php
/**
 * Inštalačný skript pre Subtitle Reader
 * Spustite tento súbor v prehliadači pre nastavenie systému
 */

// Bezpečnostná kontrola
$setup_password = 'subtitle_reader_setup_2024';
$entered_password = $_POST['setup_password'] ?? $_GET['password'] ?? '';

if ($entered_password !== $setup_password) {
    ?>
    <!DOCTYPE html>
    <html lang="cs">
    <head>
        <meta charset="UTF-8">
        <title>Subtitle Reader - Inštalácia</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    </head>
    <body class="bg-light">
        <div class="container mt-5">
            <div class="row justify-content-center">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3>🔐 Subtitle Reader - Inštalácia</h3>
                        </div>
                        <div class="card-body">
                            <form method="POST">
                                <div class="mb-3">
                                    <label class="form-label">Inštalačné heslo:</label>
                                    <input type="password" name="setup_password" class="form-control" required>
                                    <div class="form-text">Heslo: <code>subtitle_reader_setup_2024</code></div>
                                </div>
                                <button type="submit" class="btn btn-primary">Pokračovať</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    <?php
    exit();
}

// Načítanie konfigurácie
require_once '../config/database.php';

$step = $_GET['step'] ?? 'check';
$errors = [];
$success = [];

?>
<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <title>Subtitle Reader - Inštalácia</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-4">
        <div class="row">
            <div class="col-md-8 mx-auto">
                <div class="card">
                    <div class="card-header">
                        <h2><i class="fas fa-cog"></i> Subtitle Reader - Inštalácia</h2>
                    </div>
                    <div class="card-body">
                        
                        <!-- Progress -->
                        <div class="progress mb-4">
                            <div class="progress-bar" style="width: <?= $step === 'check' ? '25%' : ($step === 'database' ? '50%' : ($step === 'config' ? '75%' : '100%')) ?>"></div>
                        </div>
                        
                        <?php if ($step === 'check'): ?>
                            <!-- Krok 1: Kontrola systému -->
                            <h4>1. Kontrola systému</h4>
                            
                            <?php
                            // Kontrola PHP verzie
                            if (version_compare(PHP_VERSION, '7.4.0', '>=')) {
                                echo '<div class="alert alert-success"><i class="fas fa-check"></i> PHP verzia: ' . PHP_VERSION . ' ✓</div>';
                            } else {
                                echo '<div class="alert alert-danger"><i class="fas fa-times"></i> PHP verzia: ' . PHP_VERSION . ' (vyžaduje 7.4+)</div>';
                                $errors[] = 'PHP verzia';
                            }
                            
                            // Kontrola PDO
                            if (extension_loaded('pdo') && extension_loaded('pdo_mysql')) {
                                echo '<div class="alert alert-success"><i class="fas fa-check"></i> PDO MySQL rozšírenie ✓</div>';
                            } else {
                                echo '<div class="alert alert-danger"><i class="fas fa-times"></i> PDO MySQL rozšírenie chýba</div>';
                                $errors[] = 'PDO MySQL';
                            }
                            
                            // Kontrola cURL
                            if (extension_loaded('curl')) {
                                echo '<div class="alert alert-success"><i class="fas fa-check"></i> cURL rozšírenie ✓</div>';
                            } else {
                                echo '<div class="alert alert-danger"><i class="fas fa-times"></i> cURL rozšírenie chýba</div>';
                                $errors[] = 'cURL';
                            }
                            
                            // Kontrola JSON
                            if (extension_loaded('json')) {
                                echo '<div class="alert alert-success"><i class="fas fa-check"></i> JSON rozšírenie ✓</div>';
                            } else {
                                echo '<div class="alert alert-danger"><i class="fas fa-times"></i> JSON rozšírenie chýba</div>';
                                $errors[] = 'JSON';
                            }
                            
                            // Kontrola oprávnení
                            if (is_writable('../config/')) {
                                echo '<div class="alert alert-success"><i class="fas fa-check"></i> Oprávnenia na zápis ✓</div>';
                            } else {
                                echo '<div class="alert alert-warning"><i class="fas fa-exclamation-triangle"></i> Možné problémy s oprávneniami na zápis</div>';
                            }
                            ?>
                            
                            <?php if (empty($errors)): ?>
                                <a href="?step=database&password=<?= urlencode($setup_password) ?>" class="btn btn-primary">
                                    <i class="fas fa-arrow-right"></i> Pokračovať na databázu
                                </a>
                            <?php else: ?>
                                <div class="alert alert-danger">
                                    <strong>Opravte chyby pred pokračovaním:</strong>
                                    <ul class="mb-0">
                                        <?php foreach ($errors as $error): ?>
                                            <li><?= htmlspecialchars($error) ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            <?php endif; ?>
                            
                        <?php elseif ($step === 'database'): ?>
                            <!-- Krok 2: Databáza -->
                            <h4>2. Nastavenie databázy</h4>
                            
                            <?php
                            // Test pripojenia
                            try {
                                $test_pdo = new PDO(
                                    "mysql:host={$db_config['host']};dbname={$db_config['dbname']};charset={$db_config['charset']}",
                                    $db_config['username'],
                                    $db_config['password'],
                                    [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
                                );
                                echo '<div class="alert alert-success"><i class="fas fa-check"></i> Pripojenie k databáze úspešné ✓</div>';
                                
                                // Kontrola existencie tabuliek
                                $tables = $test_pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
                                $required_tables = ['users', 'licenses', 'license_logs', 'stripe_payments'];
                                $missing_tables = array_diff($required_tables, $tables);
                                
                                if (empty($missing_tables)) {
                                    echo '<div class="alert alert-success"><i class="fas fa-check"></i> Všetky tabuľky existujú ✓</div>';
                                } else {
                                    echo '<div class="alert alert-warning"><i class="fas fa-exclamation-triangle"></i> Chýbajúce tabuľky: ' . implode(', ', $missing_tables) . '</div>';
                                    echo '<div class="alert alert-info">
                                        <strong>Importujte SQL schému:</strong><br>
                                        1. Otvorte phpMyAdmin alebo iný MySQL klient<br>
                                        2. Vyberte databázu <code>d52810_</code><br>
                                        3. Importujte súbor <code>database/schema.sql</code>
                                    </div>';
                                }
                                
                            } catch (PDOException $e) {
                                echo '<div class="alert alert-danger"><i class="fas fa-times"></i> Chyba pripojenia: ' . htmlspecialchars($e->getMessage()) . '</div>';
                                echo '<div class="alert alert-info">
                                    <strong>Skontrolujte nastavenia v súbore:</strong><br>
                                    <code>config/database.php</code><br><br>
                                    <strong>Aktuálne nastavenia:</strong><br>
                                    Host: ' . htmlspecialchars($db_config['host']) . '<br>
                                    Databáza: ' . htmlspecialchars($db_config['dbname']) . '<br>
                                    Užívateľ: ' . htmlspecialchars($db_config['username']) . '<br>
                                    Heslo: ' . (empty($db_config['password']) ? 'PRÁZDNE' : '***') . '
                                </div>';
                                $errors[] = 'Databáza';
                            }
                            ?>
                            
                            <div class="mt-3">
                                <a href="?step=check&password=<?= urlencode($setup_password) ?>" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left"></i> Späť
                                </a>
                                <?php if (empty($errors)): ?>
                                    <a href="?step=config&password=<?= urlencode($setup_password) ?>" class="btn btn-primary">
                                        <i class="fas fa-arrow-right"></i> Pokračovať na konfiguráciu
                                    </a>
                                <?php endif; ?>
                            </div>
                            
                        <?php elseif ($step === 'config'): ?>
                            <!-- Krok 3: Konfigurácia -->
                            <h4>3. Konfigurácia aplikácie</h4>
                            
                            <div class="alert alert-info">
                                <h5>Čo ešte potrebujete nastaviť:</h5>
                                <ol>
                                    <li><strong>Stripe API kľúče</strong> v <code>config/database.php</code></li>
                                    <li><strong>Doménu</strong> v <code>license_manager.py</code></li>
                                    <li><strong>Email SMTP</strong> nastavenia</li>
                                    <li><strong>SSL certifikát</strong> pre HTTPS</li>
                                </ol>
                            </div>
                            
                            <div class="card mb-3">
                                <div class="card-header">
                                    <h5>Stripe nastavenia</h5>
                                </div>
                                <div class="card-body">
                                    <p>V súbore <code>config/database.php</code> nastavte:</p>
                                    <pre><code>define('STRIPE_SECRET_KEY', 'sk_live_your_key');
define('STRIPE_PUBLISHABLE_KEY', 'pk_live_your_key');
define('STRIPE_WEBHOOK_SECRET', 'whsec_your_webhook');</code></pre>
                                </div>
                            </div>
                            
                            <div class="card mb-3">
                                <div class="card-header">
                                    <h5>Aplikácia</h5>
                                </div>
                                <div class="card-body">
                                    <p>V súbore <code>license_manager.py</code> nastavte:</p>
                                    <pre><code>self.server_url = "https://vasa-domena.com/api"</code></pre>
                                </div>
                            </div>
                            
                            <div class="mt-3">
                                <a href="?step=database&password=<?= urlencode($setup_password) ?>" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left"></i> Späť
                                </a>
                                <a href="?step=complete&password=<?= urlencode($setup_password) ?>" class="btn btn-success">
                                    <i class="fas fa-check"></i> Dokončiť inštaláciu
                                </a>
                            </div>
                            
                        <?php else: ?>
                            <!-- Krok 4: Dokončenie -->
                            <h4>🎉 Inštalácia dokončená!</h4>
                            
                            <div class="alert alert-success">
                                <h5>Systém je pripravený na použitie!</h5>
                                <p>Teraz môžete:</p>
                                <ul>
                                    <li>Otestovať registráciu na <a href="../purchase/register.php" target="_blank">registračnej stránke</a></li>
                                    <li>Skontrolovať hlavnú stránku na <a href="../index.php" target="_blank">index.php</a></li>
                                    <li>Nastaviť Stripe webhooks</li>
                                    <li>Spustiť aplikáciu a otestovať demo režim</li>
                                </ul>
                            </div>
                            
                            <div class="alert alert-warning">
                                <strong>Bezpečnosť:</strong> Odstráňte alebo premenujte priečinok <code>install/</code> po dokončení inštalácie!
                            </div>
                            
                            <div class="mt-3">
                                <a href="../index.php" class="btn btn-primary" target="_blank">
                                    <i class="fas fa-external-link-alt"></i> Otvoriť hlavnú stránku
                                </a>
                                <a href="../purchase/register.php" class="btn btn-success" target="_blank">
                                    <i class="fas fa-user-plus"></i> Otestovať registráciu
                                </a>
                            </div>
                        <?php endif; ?>
                        
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
