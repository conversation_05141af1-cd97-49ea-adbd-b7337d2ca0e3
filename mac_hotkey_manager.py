import logging
import threading
import time
import queue
import common_config as config
import platform
# Import toggle_reading dynamically to avoid circular imports
from platform_loader import get_tts_manager
from testing import report_anomaly

# TTS function wrappers for this module
def _get_tts_function(func_name):
    """Get TTS function from platform-specific module."""
    tts_manager = get_tts_manager()
    if tts_manager and hasattr(tts_manager, func_name):
        return getattr(tts_manager, func_name)
    else:
        logging.error(f"❌ TTS function '{func_name}' not available")
        return lambda *args, **kwargs: None

def set_tts_rate(*args, **kwargs):
    return _get_tts_function('set_tts_rate')(*args, **kwargs)

def set_tts_volume(*args, **kwargs):
    return _get_tts_function('set_tts_volume')(*args, **kwargs)

def is_speaking():
    return _get_tts_function('is_speaking')()

def stop_speaking():
    return _get_tts_function('stop_speaking')()

def speak_text(*args, **kwargs):
    return _get_tts_function('speak_text')(*args, **kwargs)

def stop_current_tts():
    return _get_tts_function('stop_current_tts')()

# Dynamic import functions to avoid circular imports
def _get_app_logic_function(func_name):
    """Get function from app_logic module."""
    try:
        import app_logic
        if hasattr(app_logic, func_name):
            return getattr(app_logic, func_name)
        else:
            logging.error(f"❌ App logic function '{func_name}' not available")
            return lambda *args, **kwargs: None
    except ImportError as e:
        logging.error(f"❌ Failed to import app_logic: {e}")
        return lambda *args, **kwargs: None

def toggle_reading():
    return _get_app_logic_function('toggle_reading')()

# Cross-platform utilities
try:
    from platform_utils import normalize_hotkey_combination, IS_MACOS, IS_WINDOWS, IS_LINUX
    CROSS_PLATFORM_UTILS_AVAILABLE = True
except ImportError:
    CROSS_PLATFORM_UTILS_AVAILABLE = False
    IS_MACOS = platform.system() == "Darwin"
    IS_WINDOWS = platform.system() == "Windows"
    IS_LINUX = platform.system() == "Linux"

# Globálne premenné pre hotkey management
hotkey_listener = None
hotkey_command_queue = queue.Queue()
hotkey_processor_thread = None
hotkey_stop_event = threading.Event()

# Debounce nastavenia pre toggle
_last_toggle_ts = 0.0
TOGGLE_DEBOUNCE_SECONDS = 0.35

def hotkey_processor():
    """Background thread pre spracovanie hotkey commands."""
    import threading
    thread_id = threading.current_thread().ident
    logging.info(f"🔧 Hotkey processor thread spustený (ID: {thread_id})")

    while not hotkey_stop_event.is_set():
        try:
            # Čakáme na command s timeoutom
            command = hotkey_command_queue.get(timeout=1.0)

            # Ochrana proti queue overflow - ak je queue príliš plná, vyčisti ju
            queue_size = hotkey_command_queue.qsize()
            if queue_size > 10:  # Ak je viac ako 10 príkazov v queue
                logging.warning(f"🚨 Hotkey queue overflow ({queue_size} príkazov) - čistím queue!")
                # Vyčisti všetky pending príkazy
                while not hotkey_command_queue.empty():
                    try:
                        hotkey_command_queue.get_nowait()
                    except:
                        break
                logging.info("✅ Hotkey queue vyčistená")
                continue  # Preskočíme aktuálny príkaz a začneme odznova

            logging.info(f"🔧 Thread {thread_id}: Spracovávam hotkey command: {command}")

            # Spracovanie commands
            if command == 'toggle_reading':
                # Debounce: ignorovať opakované stlačenie v krátkom čase
                import time as _time
                global _last_toggle_ts
                now = _time.time()
                if now - _last_toggle_ts < TOGGLE_DEBOUNCE_SECONDS:
                    logging.info(f"[DEBOUNCE] Ignorujem toggle_reading (interval={(now - _last_toggle_ts):.3f}s < {TOGGLE_DEBOUNCE_SECONDS}s)")
                else:
                    _last_toggle_ts = now
                    toggle_reading()
            elif command == 'volume_down':
                import common_config as config
                set_tts_volume(config.TTS_VOLUME - config.TTS_VOLUME_STEP)
            elif command == 'volume_up':
                import common_config as config
                set_tts_volume(config.TTS_VOLUME + config.TTS_VOLUME_STEP)
            elif command == 'rate_down':
                import common_config as config
                set_tts_rate(config.TTS_RATE - config.TTS_RATE_STEP)
            elif command == 'rate_up':
                import common_config as config
                set_tts_rate(config.TTS_RATE + config.TTS_RATE_STEP)
            elif command == 'report_anomaly':
                report_anomaly_func = _get_app_logic_function('report_anomaly')
                report_anomaly_func()
            elif command == 'read_selected_text':
                handle_read_selected_text()
            else:
                logging.warning(f"Neznámy hotkey command: {command}")

            hotkey_command_queue.task_done()

        except queue.Empty:
            # Timeout - pokračujeme v cykle
            continue
        except Exception as e:
            logging.error(f"Chyba v hotkey processor: {e}")

    logging.info("Hotkey processor thread ukončený")

def setup_hotkeys(root):
    """Nastaví globálne klávesové skratky pomocou bezpečnej implementácie."""
    global hotkey_listener, hotkey_processor_thread, hotkey_stop_event

    logging.info("Nastavujem globálne klávesové skratky...")

    # Windows-specific hotkeys using Win32 API
    if IS_WINDOWS:
        try:
            from windows_hotkeys import setup_windows_hotkeys

            def windows_command_handler(command):
                """Handler pre Windows hotkey commands"""
                try:
                    hotkey_command_queue.put(command)
                    logging.debug(f"Windows hotkey command pridaný do queue: {command}")
                except Exception as e:
                    logging.error(f"Chyba v windows_command_handler: {e}")

            # Spusti processor thread
            if not hotkey_processor_thread or not hotkey_processor_thread.is_alive():
                hotkey_stop_event.clear()
                hotkey_processor_thread = threading.Thread(target=hotkey_processor, daemon=True)
                hotkey_processor_thread.start()
                logging.info("Hotkey processor thread spustený pre Windows")

            # Setup Windows hotkeys
            windows_hotkey_manager = setup_windows_hotkeys(windows_command_handler)
            if windows_hotkey_manager:
                logging.info("✅ Windows globálne hotkeys úspešne nastavené pomocou Win32 API")
                return
            else:
                logging.warning("⚠️ Windows Win32 hotkeys zlyhali, skúšam pynput fallback")
        except Exception as e:
            logging.warning(f"Windows Win32 hotkeys zlyhali: {e}")

    # macOS-specific hotkeys
    if IS_MACOS:
        try:
            if setup_macos_hotkeys():
                logging.info("Globálne klávesové skratky úspešne nastavené pomocou macOS native API.")
                return
        except Exception as e:
            logging.warning(f"macOS native hotkeys zlyhali: {e}")

    # Fallback na pynput pre všetky platformy
    try:
        setup_pynput_hotkeys(root)
        logging.info("Globálne klávesové skratky úspešne nastavené pomocou pynput.")
    except Exception as e:
        logging.warning(f"Pynput hotkeys zlyhali: {e}")
        if IS_MACOS:
            logging.warning("RIEŠENIE: Prejdite do System Preferences → Security & Privacy → Privacy")
            logging.warning("1. Kliknite na 'Input Monitoring' a pridajte Python/Terminal")
            logging.warning("2. Kliknite na 'Accessibility' a pridajte Python/Terminal")
            logging.warning("3. Reštartujte aplikáciu")

        # Posledný fallback na tkinter/Qt hotkeys (len keď je okno aktívne)
        if root is not None:
            setup_tkinter_hotkeys(root)
            logging.info("Fallback: Klávesové skratky nastavené pomocou tkinter (len keď je okno aktívne).")
        else:
            logging.warning("Qt verzia: Globálne hotkeys nebudú fungovať bez povolení")

def setup_macos_hotkeys():
    """Nastaví globálne klávesové skratky pomocou jednoduchšieho riešenia."""
    if platform.system() != 'Darwin':
        return False

    try:
        # Jednoduchšie riešenie - skúsime pynput s lepším error handlingom
        logging.info("Skúšam pynput s macOS optimalizáciou...")

        # Kontrola povolení - jednoduchší test
        logging.info("Kontrolujem macOS povolenia pre globálne hotkeys...")

        # Skúsime priamo pynput bez testovania
        # Ak zlyhá, ukážeme užívateľovi ako povoliť

        # Ak máme povolenia, skúsime pynput
        return setup_pynput_with_permissions()

    except Exception as e:
        logging.error(f"Chyba pri kontrole macOS povolení: {e}")
        return False

def setup_pynput_with_permissions():
    """Nastaví pynput hotkeys s kontrolou povolení."""
    # Označ, že sa pokúšame aktivovať globálne hotkeys
    try:
        import common_config as config
        config.GLOBAL_HOTKEYS_ACTIVE = False
    except Exception:
        pass
    """Nastaví pynput hotkeys s kontrolou povolení."""
    try:
        from pynput import keyboard

        # Reset stop event
        global hotkey_stop_event, hotkey_processor_thread
        hotkey_stop_event.clear()

        # Spustenie processor threadu ak nie je spustený
        if not hotkey_processor_thread or not hotkey_processor_thread.is_alive():
            hotkey_processor_thread = threading.Thread(target=hotkey_processor, daemon=True)
            hotkey_processor_thread.start()
            logging.info("Hotkey processor thread spustený")
            try:
                import common_config as config
                config.GLOBAL_HOTKEYS_ACTIVE = True
            except Exception:
                pass

        def safe_hotkey_handler(command):
            """Thread-safe handler pre klávesové skratky."""
            try:
                hotkey_command_queue.put(command)
                logging.debug(f"Hotkey command pridaný do queue: {command}")
            except Exception as e:
                logging.error(f"Chyba v safe_hotkey_handler: {e}")

        # Cross-platform hotkey combinations
        base_combinations = {
            '<alt>+x': 'toggle_reading',  # Alt+X pre toggle čítania
            '<alt>+c': 'read_selected_text',  # Alt+C pre čítanie označeného textu
            '<alt>+<shift>+c': 'read_selected_text',  # Alt+Shift+C ako záložná skratka
            '<ctrl>+<alt>+<left>': 'volume_down',  # Opravené: bez duplikácie ctrl
            '<ctrl>+<alt>+<right>': 'volume_up',
            '<ctrl>+<alt>+<down>': 'rate_down',
            '<ctrl>+<alt>+<up>': 'rate_up',
            '<ctrl>+<alt>+i': 'report_anomaly',
            '<ctrl>+<alt>+<cmd>+v': 'toggle_reading',  # Control+Option+Command+V pre macOS
        }

        # Convert to platform-specific combinations
        hotkey_combinations = {}
        for hotkey, action in base_combinations.items():
            if CROSS_PLATFORM_UTILS_AVAILABLE:
                platform_hotkey = normalize_hotkey_combination(hotkey)
            else:
                # Manual conversion for Windows/Linux
                if not IS_MACOS:
                    platform_hotkey = hotkey.replace('<cmd>', '<ctrl>')
                else:
                    platform_hotkey = hotkey

            hotkey_combinations[platform_hotkey] = lambda a=action: safe_hotkey_handler(a)

        logging.info(f"🔧 Platform-specific hotkeys: {list(hotkey_combinations.keys())}")

        # Registrácia hotkeys s error handlingom
        global hotkey_listener
        hotkey_listener = keyboard.GlobalHotKeys(hotkey_combinations)
        hotkey_listener.start()

        logging.info("macOS pynput globálne klávesové skratky registrované:")
        for combo in hotkey_combinations.keys():
            logging.info(f"  {combo}")

        # Označiť, že globálne hotkeys bežia
        try:
            import common_config as config
            config.GLOBAL_HOTKEYS_ACTIVE = True
        except Exception:
            pass
        return True

    except Exception as e:
        logging.error(f"Chyba pri nastavovaní macOS pynput hotkeys: {e}")
        return False

def setup_pynput_hotkeys(root):
    # Označ, že sa pokúšame aktivovať globálne hotkeys
    try:
        import common_config as config
        config.GLOBAL_HOTKEYS_ACTIVE = False
    except Exception:
        pass
    """Nastaví globálne klávesové skratky pomocou pynput s bezpečnými opatreniami."""
    global hotkey_listener, hotkey_processor_thread, hotkey_stop_event

    try:
        from pynput import keyboard

        # Reset stop event
        hotkey_stop_event.clear()

        def safe_hotkey_handler(command):
            """Thread-safe handler pre klávesové skratky."""
            try:
                # Vložíme command do queue namiesto priameho volania
                hotkey_command_queue.put(command)
                logging.debug(f"Hotkey command pridaný do queue: {command}")
            except Exception as e:
                logging.error(f"Chyba v safe_hotkey_handler: {e}")

        # Použiť globálnu hotkey_processor funkciu

        # Spustenie processor threadu
        hotkey_processor_thread = threading.Thread(target=hotkey_processor, daemon=True)
        hotkey_processor_thread.start()

        # Cross-platform hotkey combinations
        base_combinations = {
            '<alt>+x': 'toggle_reading',  # Alt+X pre toggle čítania
            '<alt>+c': 'read_selected_text',  # Alt+C pre čítanie označeného textu
            '<alt>+<shift>+c': 'read_selected_text',  # Alt+Shift+C ako záložná skratka
            '<ctrl>+<alt>+<left>': 'volume_down',  # Opravené: bez duplikácie ctrl
            '<ctrl>+<alt>+<right>': 'volume_up',
            '<ctrl>+<alt>+<down>': 'rate_down',
            '<ctrl>+<alt>+<up>': 'rate_up',
            '<ctrl>+<alt>+i': 'report_anomaly',
            '<ctrl>+<alt>+<cmd>+v': 'toggle_reading',  # Control+Option+Command+V pre macOS
        }

        # Convert to platform-specific combinations
        hotkey_combinations = {}
        for hotkey, action in base_combinations.items():
            if CROSS_PLATFORM_UTILS_AVAILABLE:
                platform_hotkey = normalize_hotkey_combination(hotkey)
            else:
                # Manual conversion for Windows/Linux
                if not IS_MACOS:
                    platform_hotkey = hotkey.replace('<cmd>', '<ctrl>')
                else:
                    platform_hotkey = hotkey

            hotkey_combinations[platform_hotkey] = lambda a=action: safe_hotkey_handler(a)

        logging.info(f"🔧 Platform-specific hotkeys: {list(hotkey_combinations.keys())}")

        # Registrácia hotkeys s error handlingom
        global hotkey_listener
        hotkey_listener = keyboard.GlobalHotKeys(hotkey_combinations)
        hotkey_listener.start()

        logging.info("Pynput globálne klávesové skratky registrované:")
        for combo in hotkey_combinations.keys():
            logging.info(f"  {combo}")

        # Listener beží v background threade - NEBLOKUJEME main thread!

        # Označiť, že globálne hotkeys bežia
        try:
            import common_config as config
            config.GLOBAL_HOTKEYS_ACTIVE = True
        except Exception:
            pass

    except ImportError:
        raise Exception("pynput nie je dostupný")
    except Exception as e:
        raise Exception(f"Chyba pri nastavovaní pynput hotkeys: {e}")

def safe_toggle_reading():
    """Thread-safe wrapper pre toggle_reading."""
    try:
        logging.info("Hotkey: Toggle reading aktivovaný")
        toggle_reading()
    except Exception as e:
        logging.error(f"Chyba v safe_toggle_reading: {e}")

def safe_set_tts_volume(volume):
    """Thread-safe wrapper pre set_tts_volume."""
    try:
        logging.info(f"Hotkey: Nastavujem hlasitosť na {volume:.2f}")
        set_tts_volume(volume)
    except Exception as e:
        logging.error(f"Chyba v safe_set_tts_volume: {e}")

def safe_set_tts_rate(rate):
    """Thread-safe wrapper pre set_tts_rate."""
    try:
        logging.info(f"Hotkey: Nastavujem rýchlosť na {rate}")
        config.MANUAL_TTS_RATE_ADJUSTMENT = True
        config.USER_TTS_RATE = rate
        set_tts_rate(rate)
    except Exception as e:
        logging.error(f"Chyba v safe_set_tts_rate: {e}")

def safe_report_anomaly():
    """Thread-safe wrapper pre report_anomaly."""
    try:
        logging.info("Hotkey: Report anomaly aktivovaný")
        report_anomaly()
    except Exception as e:
        logging.error(f"Chyba v safe_report_anomaly: {e}")

def setup_tkinter_hotkeys(root):
    """Nastaví klávesové skratky pomocou tkinter (bezpečné, fungujú keď je okno aktívne)."""

    # Hodnoty pre modifikačné klávesy
    CONTROL_MASK = 4
    ALT_MASK = 8
    CMD_MASK = 1048576  # Command na macOS

    # Pre Alt+X a Alt+C používame len ALT
    ALT_ONLY_STATE = ALT_MASK
    # Pre ostatné používame Cmd+Alt+Ctrl
    FULL_REQUIRED_STATE = CMD_MASK | ALT_MASK | CONTROL_MASK

    def on_key_press(event):
        """Handler pre klávesové skratky."""
        try:
            # Kontrola Alt+X a Alt+C (Alt s/bez Shift)
            if (event.state & ALT_MASK) and not (event.state & CONTROL_MASK) and not (event.state & CMD_MASK):
                ks = event.keysym.lower()
                if ks == 'x':
                    logging.info("Hotkey: Toggle reading aktivovaný (Alt+X)")
                    toggle_reading()
                elif ks == 'c':
                    logging.info("Hotkey: Read selected text aktivovaný (Alt+C/Alt+Shift+C)")
                    handle_read_selected_text()

            # Kontrola Cmd+Alt+Ctrl kombinácií
            elif (event.state & FULL_REQUIRED_STATE) == FULL_REQUIRED_STATE:
                logging.info(f"Hotkey detekovaný: {event.keysym} s modifikátormi Cmd+Alt+Ctrl")

                if event.keysym == 'Left':
                    logging.info("Hotkey: Znižujem hlasitosť")
                    set_tts_volume(config.TTS_VOLUME - config.TTS_VOLUME_STEP)
                elif event.keysym == 'Right':
                    logging.info("Hotkey: Zvyšujem hlasitosť")
                    set_tts_volume(config.TTS_VOLUME + config.TTS_VOLUME_STEP)
                elif event.keysym == 'Down':
                    logging.info("Hotkey: Znižujem rýchlosť reči")
                    set_tts_rate(config.TTS_RATE - config.TTS_RATE_STEP)
                elif event.keysym == 'Up':
                    logging.info("Hotkey: Zvyšujem rýchlosť reči")
                    set_tts_rate(config.TTS_RATE + config.TTS_RATE_STEP)
                elif event.keysym == 'i':
                    logging.info("Hotkey: Report anomaly aktivovaný")
                    report_anomaly()

        except Exception as e:
            logging.error(f"Chyba v hotkey handleri: {e}")

    # Registrácia event handlera
    root.bind_all('<KeyPress>', on_key_press)

    # Nastavenie focus policy pre lepšie zachytávanie klávesov
    root.focus_set()

    logging.info("Tkinter klávesové skratky nastavené:")
    logging.info("  Alt+X: Toggle čítania")
    logging.info("  Alt+C / Alt+Shift+C: Čítanie označeného textu")
    logging.info("  Cmd+Alt+Ctrl+←/→: Hlasitosť")
    logging.info("  Cmd+Alt+Ctrl+↑/↓: Rýchlosť reči")
    logging.info("  Cmd+Alt+Ctrl+I: Report anomaly")
    logging.info("POZNÁMKA: Klávesové skratky fungujú len keď je aplikácia aktívna!")

def enqueue_hotkey_command(command: str):
    """Bezpečne vloží hotkey príkaz do fronty a spustí processor ak neběží."""
    try:
        global hotkey_processor_thread
        if not hotkey_processor_thread or not hotkey_processor_thread.is_alive():
            hotkey_processor_thread = threading.Thread(target=hotkey_processor, daemon=True)
            hotkey_processor_thread.start()
            logging.info("Hotkey processor thread spustený (on-demand)")
        hotkey_command_queue.put(command)
        logging.debug(f"Hotkey command pridaný do queue (GUI): {command}")
    except Exception as e:
        logging.error(f"Chyba v enqueue_hotkey_command('{command}'): {e}")


def cleanup_hotkeys():
    """Vyčistí klávesové skratky pri ukončení aplikácie."""
    global hotkey_listener, hotkey_processor_thread, hotkey_stop_event

    try:
        # Zastavenie processor threadu
        if hotkey_stop_event:
            hotkey_stop_event.set()

        # Zastavenie pynput listener
        if hotkey_listener:
            hotkey_listener.stop()
            logging.info("Pynput hotkey listener zastavený")

        # Čakáme na ukončenie processor threadu
        if hotkey_processor_thread and hotkey_processor_thread.is_alive():
            hotkey_processor_thread.join(timeout=2.0)
            if hotkey_processor_thread.is_alive():
                logging.warning("Hotkey processor thread sa neukončil včas")

        logging.info("Klávesové skratky vyčistené.")

    except Exception as e:
        logging.error(f"Chyba pri čistení hotkeys: {e}")

def get_selected_text():
    """Získa označený text z aktívneho okna."""
    try:
        if IS_WINDOWS:
            return get_selected_text_windows()
        elif IS_MACOS:
            return get_selected_text_macos()
        elif IS_LINUX:
            return get_selected_text_linux()
        else:
            logging.warning("Nepodporovaný operačný systém pre získanie označeného textu")
            return None
    except Exception as e:
        logging.error(f"Chyba pri získavaní označeného textu: {e}")
        return None

def get_selected_text_windows_uia():
    """Skúsi získať označený text cez Windows UI Automation (bez zásahu do clipboardu).
    Robustná inicializácia: CUIAutomation8 → CUIAutomation, s COM CoInitialize/CoUninitialize.
    """
    try:
        import comtypes
        import comtypes.client as cc
        logging.debug("🧭 UIA: Pokúšam sa získať označený text pomocou UI Automation...")

        coinit = False
        try:
            comtypes.CoInitialize()
            coinit = True
        except Exception:
            # už môže byť inicializované
            pass

        uia = None
        try:
            try:
                # Novšie API
                uia = cc.CreateObject('UIAutomationClient.CUIAutomation8')
            except Exception as e8:
                # Fallback na staršie ProgID
                uia = cc.CreateObject('UIAutomationClient.CUIAutomation')
        except Exception as e:
            logging.debug(f"UIA CreateObject zlyhalo: {e}")
            return None
        finally:
            # neuzatvárame COM tu; až na konci funkcie
            pass

        element = uia.GetFocusedElement()
        if not element:
            logging.debug("UIA: Žiadny fokusovaný element")
            return None

        # Pattern IDs
        UIA_TextPatternId = 10014
        UIA_TextPattern2Id = 10024
        UIA_ValuePatternId = 10002

        # TextPattern2 (preferované)
        try:
            tp2 = element.GetCurrentPattern(UIA_TextPattern2Id)
            if tp2:
                ranges = tp2.GetSelection()
                if ranges and getattr(ranges, 'Length', 0) > 0:
                    rng = ranges.GetElement(0)
                    text = rng.GetText(2**31 - 1)
                    if text and text.strip():
                        logging.info(f"✅ UIA: Získaný označený text ({len(text.strip())} znakov)")
                        return text.strip()
        except Exception as e:
            logging.debug(f"UIA TextPattern2 nie je k dispozícii: {e}")

        # TextPattern
        try:
            tp = element.GetCurrentPattern(UIA_TextPatternId)
            if tp:
                ranges = tp.GetSelection()
                if ranges and getattr(ranges, 'Length', 0) > 0:
                    rng = ranges.GetElement(0)
                    text = rng.GetText(2**31 - 1)
                    if text and text.strip():
                        logging.info(f"✅ UIA: Získaný označený text cez TextPattern ({len(text.strip())} znakov)")
                        return text.strip()
        except Exception as e:
            logging.debug(f"UIA TextPattern nie je k dispozícii: {e}")

        # ValuePattern (celý obsah poľa - len ako posledná možnosť a ak je krátky)
        try:
            vp = element.GetCurrentPattern(UIA_ValuePatternId)
            if vp:
                val = vp.CurrentValue
                if val and val.strip() and len(val) <= 2000:
                    logging.info(f"✅ UIA: Získaná hodnota z ValuePattern ({len(val.strip())} znakov)")
                    return val.strip()
        except Exception as e:
            logging.debug(f"UIA ValuePattern nie je k dispozícii: {e}")

        logging.debug("UIA: Nepodarilo sa získať označený text")
        return None

    except ImportError as e:
        logging.debug(f"UIA nie je dostupná (chýba comtypes): {e}")
        return None
    except Exception as e:
        logging.error(f"Chyba UIA pri získavaní označeného textu: {e}")
        return None
    finally:
        try:
            import comtypes
            comtypes.CoUninitialize()
        except Exception:
            pass


def get_selected_text_windows_clipboard_safe():
    """Získa označený text s dočasným použitím clipboardu a okamžitou obnovou pôvodného obsahu.
    Postup: WM_COPY → ak sa nič nezmení, skúsiť simuláciu Ctrl+C. Clipboard vždy obnovíme.
    """
    try:
        import ctypes, time
        import pyperclip
        logging.debug("📋 Bezpečný režim: pokúšam sa dočasne skopírovať označený text a obnoviť clipboard...")

        user32 = ctypes.windll.user32
        # Uložíme pôvodný clipboard a nastavíme sentinel pre spoľahlivú detekciu zmeny
        try:
            original = pyperclip.paste()
        except Exception:
            original = None

        # Pokúsime sa vložiť jedinečný sentinel do clipboardu, aby sme vedeli detegovať zmenu
        clip_baseline = original
        try:
            sentinel = f"__SR_SENTINEL__{int(time.time()*1000)}__"
            pyperclip.copy(sentinel)
            clip_baseline = sentinel
        except Exception:
            pass

        text = None

        def wait_for_clip_change(timeout_s=0.35):
            nonlocal text
            start = time.time()
            while time.time() - start < timeout_s:
                try:
                    new_val = pyperclip.paste()
                    if new_val != clip_baseline and new_val and new_val.strip():
                        text = new_val.strip()
                        return True
                except Exception:
                    pass
                time.sleep(0.01)
            return False

        # 1) Skúsime poslať WM_COPY aktívnemu oknu
        hwnd = user32.GetForegroundWindow()
        if hwnd:
            WM_COPY = 0x0301
            try:
                # Získaj názov okna pre debug
                window_title = ctypes.create_unicode_buffer(256)
                user32.GetWindowTextW(hwnd, window_title, 256)
                logging.debug(f"WM_COPY odoslané aktívnemu oknu: '{window_title.value}' (HWND: {hwnd})")

                ctypes.windll.user32.SendMessageW(hwnd, WM_COPY, 0, 0)
                if not wait_for_clip_change(0.28):
                    logging.debug("WM_COPY nezmenilo clipboard, skúšam Ctrl+C")
            except Exception as e:
                logging.debug(f"WM_COPY zlyhalo: {e}")
        else:
            logging.debug("Žiadne aktívne okno pre WM_COPY")

        # 2) Ak stále nič, simulovať Ctrl+C (s krátkymi oneskoreniami pre spoľahlivosť)
        if not text:
            VK_CONTROL = 0x11
            VK_C = 0x43
            KEYEVENTF_KEYUP = 0x0002
            try:
                logging.debug("Simulujem Ctrl+C...")
                user32.keybd_event(VK_CONTROL, 0, 0, 0)
                time.sleep(0.02)
                user32.keybd_event(VK_C, 0, 0, 0)
                time.sleep(0.02)
                user32.keybd_event(VK_C, 0, KEYEVENTF_KEYUP, 0)
                user32.keybd_event(VK_CONTROL, 0, KEYEVENTF_KEYUP, 0)
                logging.debug("Ctrl+C simulácia dokončená")
            except Exception as e:
                logging.debug(f"Simulácia Ctrl+C zlyhala: {e}")
            # čakáme trochu dlhšie, kým aplikácia spracuje kopírovanie
            if not wait_for_clip_change(0.40):
                logging.debug("Ani Ctrl+C nezmenilo clipboard")

        # 3) Posledný pokus - skús získať text priamo z clipboardu (možno tam už je)
        if not text:
            try:
                current_clip = pyperclip.paste()
                if current_clip and current_clip != clip_baseline and len(current_clip.strip()) > 0:
                    text = current_clip.strip()
                    logging.debug(f"Našiel som text priamo v clipboarde: '{text[:50]}...'")
            except Exception as e:
                logging.debug(f"Priamy prístup k clipboardu zlyhal: {e}")

        # Obnovíme pôvodný clipboard (aj keď sa nič nezmenilo)
        try:
            if original is not None:
                pyperclip.copy(original)
                logging.debug("🔁 Pôvodný clipboard obnovený")
        except Exception:
            pass

        # Kontrola získaného textu - zmiernené podmienky
        if text and len(text.strip()) > 0:
            # Odfiltruj len očividne nepoužiteľné texty
            text_clean = text.strip()
            if (len(text_clean) >= 1 and
                not text_clean.startswith('http') and
                not text_clean.startswith('__SR_SENTINEL__')):
                logging.info(f"✅ Bezpečne získaný text bez zmeny clipboardu ({len(text_clean)} znakov)")
                return text_clean
            else:
                logging.debug(f"ℹ️ Text odfiltrovaný ako nepoužiteľný: '{text_clean[:50]}...'")
        else:
            logging.debug("ℹ️ Nenašiel sa žiadny použiteľný text v bezpečnom režime")

        return None

    except Exception as e:
        logging.error(f"Chyba v clipboard-safe režime: {e}")
        return None


def get_selected_text_windows():
    """Získa označený text na Windows bez zmeny clipboardu (UIA → bezpečný clipboard fallback)."""
    # 1) Skús UIA (bez zmeny clipboardu)
    text = get_selected_text_windows_uia()
    if text:
        return text

    # 2) Fallback: dočasné skopírovanie a okamžitá obnova
    return get_selected_text_windows_clipboard_safe()

def get_selected_text_macos():
    """Získa označený text na macOS pomocí AppleScript."""
    try:
        import subprocess

        # AppleScript na získanie označeného textu
        applescript = '''
        tell application "System Events"
            keystroke "c" using command down
            delay 0.1
            return the clipboard
        end tell
        '''

        result = subprocess.run(['osascript', '-e', applescript],
                              capture_output=True, text=True, timeout=5)

        if result.returncode == 0 and result.stdout.strip():
            selected_text = result.stdout.strip()
            logging.info(f"Získaný označený text (macOS): '{selected_text[:100]}...'")
            return selected_text
        else:
            logging.debug("Žiadny označený text na macOS")
            return None

    except Exception as e:
        logging.error(f"Chyba pri získavaní označeného textu na macOS: {e}")
        return None

def get_selected_text_linux():
    """Získa označený text na Linux pomocí xclip."""
    try:
        import subprocess

        # Skúsime získať text z PRIMARY selection (označený text)
        result = subprocess.run(['xclip', '-o', '-selection', 'primary'],
                              capture_output=True, text=True, timeout=5)

        if result.returncode == 0 and result.stdout.strip():
            selected_text = result.stdout.strip()
            logging.info(f"Získaný označený text (Linux): '{selected_text[:100]}...'")
            return selected_text
        else:
            logging.debug("Žiadny označený text na Linux")
            return None

    except FileNotFoundError:
        logging.error("xclip nie je nainštalovaný - nainštalujte: sudo apt install xclip")
        return None
    except Exception as e:
        logging.error(f"Chyba pri získavaní označeného textu na Linux: {e}")
        return None

# Globálna premenná pre throttling
_last_read_selected_time = 0
_read_selected_cooldown = 0.5  # 500ms cooldown medzi pokusmi

def handle_read_selected_text():
    """Spracuje hotkey pre čítanie označeného textu s throttling ochranou."""
    global _last_read_selected_time

    try:
        import time
        current_time = time.time()

        # Throttling ochrana - ignoruj príliš časté volania
        if current_time - _last_read_selected_time < _read_selected_cooldown:
            logging.debug(f"🚫 Alt+C throttled - príliš rýchle volanie (cooldown: {_read_selected_cooldown}s)")
            return

        _last_read_selected_time = current_time
        logging.info("🎤 Alt+C: Spracovávam čítanie označeného textu...")

        # TTS functions are already available from module-level wrappers

        # Získame označený text
        selected_text = get_selected_text()

        if selected_text and selected_text.strip():
            # Máme označený text – preemptívne zastavíme predošlé čítanie a okamžite pustíme nové.
            logging.info(f"📖 Označený text nájdený: '{selected_text[:50]}...'")
            if is_speaking():
                logging.info("🛑 Preemptívne zastavujem predošlé čítanie (bez čakania)...")
                stop_current_tts()  # nečaká; pošle stop a provider hneď ukončí prehrávanie
            logging.info("🔊 Začínam čítať označený text (okamžite po preemptívnom stop)...")
            speak_text(selected_text)

        else:
            # Žiadny označený text
            logging.info("📝 Žiadny označený text nájdený")

            # Nezastavuj prebiehajúce čítanie, ak sa nenašiel výber.
            # Ponecháme existujúce čítanie (napr. OCR v Edge) bežať ďalej.
            if is_speaking():
                logging.info("ℹ️ Výber nenašli – ponechávam prebiehajúce čítanie bežať.")
            else:
                logging.info("ℹ️ Nič sa nečíta, žiadna akcia")

    except Exception as e:
        logging.error(f"Chyba v handle_read_selected_text: {e}")
        import traceback
        logging.debug(traceback.format_exc())