#!/usr/bin/env python3
"""
Test OCR filter fix - test horizontal centering tolerance
"""

import logging
import time
from PIL import Image, ImageDraw, ImageFont
import tempfile
import os

# Setup logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')

def create_test_subtitle_image(text, position="center", width=1920, height=1080):
    """Create test image with subtitle text at different positions."""
    
    # Create image
    img = Image.new('RGB', (width, height), color='black')
    draw = ImageDraw.Draw(img)
    
    # Try to use a reasonable font
    try:
        font = ImageFont.truetype("arial.ttf", 48)
    except:
        try:
            font = ImageFont.truetype("C:/Windows/Fonts/arial.ttf", 48)
        except:
            font = ImageFont.load_default()
    
    # Calculate text position
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    
    if position == "center":
        x = (width - text_width) // 2
    elif position == "left":
        x = width // 4  # 25% from left
    elif position == "right":
        x = 3 * width // 4 - text_width  # 75% from left
    elif position == "far_left":
        x = 50  # Very left
    elif position == "far_right":
        x = width - text_width - 50  # Very right
    else:
        x = (width - text_width) // 2
    
    y = height - 150  # Bottom area like subtitles
    
    # Draw white text on black background
    draw.text((x, y), text, fill='white', font=font)
    
    return img

def test_ocr_filter_tolerance():
    """Test OCR filter with different subtitle positions."""
    print("🚀 OCR Filter Tolerance Test")
    print("=" * 60)
    
    try:
        from ocr_core import analyze_static_text_layout
        import common_config as config
        
        print(f"📊 Aktuálna tolerancia: {config.MAX_DEVIATION_PERCENT_FOR_ANCHOR*100}%")
        
        # Test cases
        test_cases = [
            ("center", "Hello, this is a centered subtitle"),
            ("left", "This subtitle is on the left side"),
            ("right", "This subtitle is on the right side"),
            ("far_left", "Very left subtitle"),
            ("far_right", "Very right subtitle")
        ]
        
        results = []
        
        for position, text in test_cases:
            print(f"\n🔍 Test: {position.upper()} - '{text}'")
            
            # Create test image
            img = create_test_subtitle_image(text, position)
            
            # Test OCR filter
            is_centered, estimated_psm, recognized_text = analyze_static_text_layout(img, 'eng')
            
            status = "✅ PRIJATÝ" if is_centered else "❌ ODMIETNUTÝ"
            print(f"   Výsledok: {status}")
            print(f"   Rozpoznaný text: '{recognized_text}'")
            print(f"   Odhadovaný PSM: {estimated_psm}")
            
            results.append({
                'position': position,
                'text': text,
                'accepted': is_centered,
                'recognized': recognized_text,
                'psm': estimated_psm
            })
        
        # Summary
        print("\n" + "=" * 60)
        print("📊 SÚHRN VÝSLEDKOV:")
        
        accepted_count = sum(1 for r in results if r['accepted'])
        total_count = len(results)
        
        print(f"✅ Prijatých: {accepted_count}/{total_count}")
        print(f"❌ Odmietnutých: {total_count - accepted_count}/{total_count}")
        
        for result in results:
            status_icon = "✅" if result['accepted'] else "❌"
            print(f"   {status_icon} {result['position']}: {result['text'][:30]}...")
        
        # Test with actual subtitle from logs
        print(f"\n🎬 Test s reálnym titulkom z logov:")
        real_subtitle = "Uhm... I think we need one more thing."
        
        for position in ["center", "left", "right"]:
            img = create_test_subtitle_image(real_subtitle, position)
            is_centered, estimated_psm, recognized_text = analyze_static_text_layout(img, 'eng')
            
            status = "✅ PRIJATÝ" if is_centered else "❌ ODMIETNUTÝ"
            print(f"   {position}: {status} - '{recognized_text}'")
        
        print("\n" + "=" * 60)
        print("🎉 OCR FILTER TEST DOKONČENÝ!")
        
        return accepted_count >= 3  # At least center, left, right should be accepted
        
    except Exception as e:
        print(f"❌ Test zlyhal: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_ocr_filter_tolerance()
    
    print("\n" + "=" * 60)
    print("📊 FINÁLNY VÝSLEDOK:")
    if success:
        print("🎉 OCR FILTER FIX FUNGUJE!")
        print("🚀 Titulky by sa teraz mali čítať aj keď nie sú presne v strede")
    else:
        print("❌ OCR filter test zlyhal")
    
    exit(0 if success else 1)
