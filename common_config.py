import os
from pathlib import Path
import threading
import queue
import dataclasses
from typing import List, Optional
import subprocess # Pre Optional[subprocess.Popen]
from collections import deque # Pre deque
import datetime # Import datetime
import time # Pre time.time()

# Cross-platform utilities
try:
    from platform_utils import (
        get_app_data_dir, get_temp_dir, get_documents_dir,
        normalize_hotkey_combination, ensure_directory_exists
    )
    CROSS_PLATFORM_UTILS_AVAILABLE = True
except ImportError:
    CROSS_PLATFORM_UTILS_AVAILABLE = False

# --- Glob<PERSON>lne Konfigurácie ---
LOG_FILE = "app.log"  # Predvolený názov logovacieho súboru

TESSERACT_CMD = 'tesseract'
TESSDATA_PREFIX = None

YT_DLP_CMD = 'yt-dlp'
FFMPEG_CMD = 'ffmpeg'

OCR_LANGUAGE = 'eng'
TTS_LANGUAGE = 'cs-CZ' # Pre 'say' je relevantný skôr názov hlasu
TTS_VOICE = '<PERSON>uzana (Enhanced)' # Predvolený hlas pre 'say'

# Rozlíšenie hlasov podľa kontextu
READING_TTS_VOICE = TTS_VOICE           # Hlas pre čítanie titulkov a systémové hlášky
TRANSLATION_TTS_VOICE = TTS_VOICE       # Hlas pre preložený text (ak je preklad zapnutý)

# Konfigurácia hlasov pre GUI dropdown menu
SELECTED_READING_VOICE = TTS_VOICE      # Vybraný hlas pre čítanie v GUI
SELECTED_TRANSLATION_VOICE = TTS_VOICE  # Vybraný hlas pre preklad v GUI

# Indikátor, či sú globálne hotkeys (pynput) aktívne
GLOBAL_HOTKEYS_ACTIVE = False

SAVE_IMAGES = False  # Vypnuté pre úsporu miesta

# Cross-platform directory paths
if CROSS_PLATFORM_UTILS_AVAILABLE:
    # Use cross-platform paths
    _app_data_dir = get_app_data_dir("SubtitleReader")
    ensure_directory_exists(_app_data_dir)

    IMAGE_SAVE_DIR = _app_data_dir / "OCR_Anomaly_Reports"
    TEMP_DIR = get_temp_dir()
    HOTKEY_COMBINATION = normalize_hotkey_combination('<cmd>+<alt>+<ctrl>+v')
else:
    # Fallback to legacy paths
    IMAGE_SAVE_DIR = Path.home() / "Documents" / "SubtitleReader" / "OCR_Anomaly_Reports"
    TEMP_DIR = Path(os.getenv('TMPDIR', '/tmp'))
    HOTKEY_COMBINATION = '<cmd>+<alt>+<ctrl>+v'

# Ensure directories exist
ensure_directory_exists(IMAGE_SAVE_DIR) if CROSS_PLATFORM_UTILS_AVAILABLE else IMAGE_SAVE_DIR.mkdir(parents=True, exist_ok=True)
DEFAULT_TTS_RATE = 200
USER_TTS_RATE = DEFAULT_TTS_RATE # Nová premenná pre rýchlosť nastavenú používateľom
TTS_RATE = DEFAULT_TTS_RATE
TTS_VOLUME = 1.0
MANUAL_TTS_RATE_ADJUSTMENT = False # Nový príznak pre manuálne nastavenie rýchlosti
READING_OCR_INTERVAL = 0.3      # Interval pre aktívne čítanie (nastavené na 0.3s)
DETECTION_OCR_INTERVAL = 0.3     # Interval pre fázu automatickej detekcie
current_ocr_interval = 0.3  # Defaultný OCR interval  # Aktuálne používaný interval, ktorý sa dynamicky mení
TTS_PROCESS_INTERVAL = 0.1  # Zrýchlené spracovanie TTS
OCR_THRESHOLD = 0.99 # Prah pre binarizáciu obrazu
CONTEXT_CHECK_INTERVAL = 0.2 # Znížený interval pre rýchlejšiu reakciu
MIN_WORDS_PER_LINE_FOR_ANCHOR = 1 # Znížené pre lepšie rozpoznávanie krátkych textov
MAX_DEVIATION_PERCENT_FOR_ANCHOR = 0.10 # Maximálna povolená odchýlka (25%) stredu riadku od stredu obrazovky

# Konfigurácie špecifické pre dynamický režim
DYNAMIC_OCR_LANGUAGE = 'eng' # Jazyk OCR pre dynamický režim
DYNAMIC_OCR_THRESHOLD = 0.90 # Prah pre binarizáciu obrazu pre dynamický režim (príklad, môže sa líšiť)
# DYNAMIC_MAX_DEVIATION_PERCENT_FOR_ANCHOR = 0.2 # Príklad: Väčšia tolerancia pre odchýlku od stredu pre dynamický režim

# Nové klávesové skratky a parametre pre ovládanie TTS
HOTKEY_VOLUME_DOWN = '<cmd>+<alt>+<ctrl>+<left>'
HOTKEY_VOLUME_UP = '<cmd>+<alt>+<ctrl>+<right>'
HOTKEY_RATE_DOWN = '<cmd>+<alt>+<ctrl>+<down>'
HOTKEY_RATE_UP = '<cmd>+<alt>+<ctrl>+<up>'
HOTKEY_ANOMALY_REPORT = '<cmd>+<alt>+<ctrl>+i' # Nová klávesová skratka pre hlásenie anomálií
TTS_RATE_STEP = 20
TTS_VOLUME_STEP = 0.1 # Hlasitosť je 0.0-1.0, krok 0.05 zodpovedá 5%
MIN_TTS_RATE = 100
MAX_TTS_RATE = 400
MIN_TTS_VOLUME = 0.0
MAX_TTS_VOLUME = 1.0
TTS_HISTORY_SIMILARITY_THRESHOLD = 0.85 # Prah pre kontrolu duplicity v TTS histórii
MIN_DYNAMIC_TEXT_LENGTH = 5 # Minimálna dĺžka textu pre čítanie v dynamickom režime
STABILITY_SIMILARITY_THRESHOLD = 0.90 # Prah podobnosti pre stabilitu riadku v dynamickom režime (zvýšené z 0.80)

# === JEDNODUCHÉ PARAMETRE PRE DETEKCIU REŽIMU ===
STATIC_SWITCH_DELAY_MS = 1500 # Doba čakania pred prepnutím na statický režim (v milisekundách) - skrátené pre jednoriadkové texty
TWO_FRAME_STABILITY_THRESHOLD = 0.90 # Prah podobnosti pre dvojrámcovú stabilitu v dynamickom režime








# --- Globálne Premenné (Stav Aplikácie) ---
is_reading = False
reading_mode = "full_automatic" # Predvolený režim je full automatic
status_message = "Aplikácia pripravená. Stlačte skratku pre štart."
unified_speech_queue = queue.Queue()  # Nová centrálna fronta pre všetky texty na čítanie
raw_ocr_queue = queue.Queue()
youtube_tts_queue = queue.Queue()
tts_command_queue = queue.Queue()
tts_worker_thread = None
unified_speech_processor_thread = None  # Nové centrálne vlákno
ocr_dispatcher_thread = None
tts_stop_event = threading.Event()
ocr_trigger_event = threading.Event() # Nový event na proaktívne spustenie OCR
tts_trigger_event = threading.Event() # Nový event na okamžité spracovanie TTS fronty

# --- Premenné pre automatický režim detekcie ---
automatic_detection_queue = queue.Queue()  # Fronta pre OCR dáta v automatickom režime
automatic_detection_thread = None  # Vlákno pre automatickú detekciu

# Priebežná detekcia - história textov
detection_text_history = deque(maxlen=20)  # História posledných 20 textov
consecutive_static_count = 0  # Počítadlo po sebe idúcich statických textov
last_detected_mode = None  # Posledný detekovaný režim ('static' alebo 'dynamic')
mode_stable_since = None  # Čas, odkedy je režim stabilný
current_base_text = ""  # Základný text pre detekciu dynamického rastu

# --- Premenné pre full automatic režim ---
full_automatic_detection_queue = queue.Queue()  # Fronta pre OCR dáta v full automatic režime
full_automatic_detection_thread = None  # Vlákno pre full automatic detekciu
full_auto_current_sub_mode = 'static'  # Aktuálny pod-režim ('static' alebo 'dynamic')

# Full automatic detekcia - robustný algoritmus
full_auto_detection_history = deque(maxlen=20)  # História typov zmien ('INCREMENTAL', 'REPLACE', 'CLEAR', 'STABLE')
full_auto_last_analyzed_text = ""  # Posledný analyzovaný text pre porovnanie
full_auto_detection_thresholds = {}  # Prahové hodnoty pre rozhodovanie (nastavujú sa dynamicky)
full_auto_last_detected_mode = None  # Posledný detekovaný režim ('static' alebo 'dynamic')
full_auto_mode_stable_since = None  # Čas, odkedy je režim stabilný
full_auto_first_detection_done = False  # Či už bola vykonaná prvá detekcia režimu

# Staré premenné (zachované pre kompatibilitu)
full_auto_detection_text_history = deque(maxlen=20)  # História posledných 20 textov
full_auto_consecutive_static_count = 0  # Počítadlo po sebe idúcich statických textov
full_auto_current_base_text = ""  # Základný text pre detekciu dynamického rastu

# Parametre pre detekciu
STATIC_SIMILARITY_THRESHOLD = 0.95  # 95% podobnosť pre statický text
STATIC_SEQUENCE_THRESHOLD = 4  # 4+ po sebe idúcich = statický režim (znížené pre rýchlejšiu detekciu)
MODE_STABILITY_TIME = 4.0  # 4 sekundy stability režimu

# Konfigurácia robustnej detekcie pre Full Automatic režim

# Počiatočné nastavenia (rýchla detekcia na začiatku)
FULL_AUTO_INITIAL_STATIC_THRESHOLD = 0.5  # 50% zmien pre počiatočnú detekciu statického režimu (znížené z 60%)
FULL_AUTO_INITIAL_DYNAMIC_THRESHOLD = 0.5  # 50% zmien pre počiatočnú detekciu dynamického režimu (znížené z 60%)
FULL_AUTO_INITIAL_MIN_SAMPLES = 3  # Minimálny počet vzoriek pre počiatočnú detekciu (znížené z 4)

# Stabilné nastavenia (po prvej detekcii)
FULL_AUTO_STABLE_STATIC_THRESHOLD = 0.75  # 75% zmien musí byť REPLACE/CLEAR pre statický režim
FULL_AUTO_STABLE_DYNAMIC_THRESHOLD = 0.75  # 75% zmien musí byť INCREMENTAL pre dynamický režim
FULL_AUTO_STABLE_MIN_SAMPLES = 12  # Minimálny počet vzoriek pre stabilné rozhodnutie (zvýšené z 8 pre stabilitu)

FULL_AUTO_HISTORY_SIZE = 50  # Veľkosť pohyblivého okna pre históriu zmien (zvýšené pre dlhšiu observáciu)

# Prahové hodnoty pre kategorizáciu zmien
FULL_AUTO_REPLACE_THRESHOLD = 0.4  # Podobnosť pod 40% = REPLACE
FULL_AUTO_STABLE_THRESHOLD = 0.95  # Podobnosť nad 95% = STABLE

# Hysteréza pre prepínanie režimov (zabráni častému prepínaniu)
FULL_AUTO_MODE_SWITCH_HYSTERESIS = 0.1  # 10% hysteréza pre prepínanie

# === NOVÉ PARAMETRE PRE VYLEPŠENÚ DETEKCIU ===
# Dynamické titulky - detekcia rastu
DYNAMIC_MIN_WORD_LENGTH = 2  # Minimálna dĺžka pridaného slova
DYNAMIC_MAX_WORDS_PER_CYCLE = 3  # Maximálne slov pridaných naraz
DYNAMIC_GROWTH_SIMILARITY = 0.8  # Minimálna podobnosť pre detekciu rastu

# Statické titulky - detekcia stability a výmeny
STATIC_STABILITY_THRESHOLD = 0.95  # Prah pre stabilný statický text
STATIC_REPLACEMENT_THRESHOLD = 0.3  # Prah spoločných slov pre detekciu výmeny
STATIC_OCR_ERROR_THRESHOLD = 0.7  # Prah pre OCR chyby v statickom texte

# Všeobecné detekčné parametre
TEXT_SIMILARITY_PRECISION = 0.01  # Presnosť podobnosti (0.01 = 1%)
MIN_TEXT_LENGTH_FOR_ANALYSIS = 3  # Minimálna dĺžka textu pre analýzu

# === NOVÁ DETEKCIA STABILITY RIADKOV ===
# Parametre pre detekciu stability (konfigurovateľné cez GUI)
STABILITY_FIRST_LINE_THRESHOLD = 0.95   # Prah stability prvého riadku (95%)
STABILITY_SECOND_LINE_THRESHOLD = 0.95  # Prah stability druhého riadku (95%)
MIN_CYCLES_FIRST_LINE_STABILITY = 2     # Min. cyklov stability prvého riadku
MIN_CYCLES_SECOND_LINE_STABILITY = 2    # Min. cyklov stability druhého riadku
MIN_CYCLES_SECOND_LINE_GROWTH = 2       # Min. cyklov rastu druhého riadku

# === SPOLOČNÉ POČÍTADLÁ PRE PREPNUTIE NA STATICKÝ REŽIM ===
STABILITA_JEDNEHO_TEXTU_CYKLY = 2  # Koľko cyklov musí byť jeden text stabilný
POCET_ROZNYCH_STABILNYCH_TEXTOV_CIEL = 3  # Koľko rôznych stabilných textov je potrebných pre prepnutie na statický režim

POCITADLO_PRE_PREPNUTIE_STATICKEHO_MODU_CYKLY = 4 # Počet cyklov pre prepnutie na statický režim

# Live monitoring premenné (aktualizované v reálnom čase)
current_first_line_stability = 0.0      # Aktuálna stabilita 1. riadku (0.0-1.0)
current_second_line_stability = 0.0     # Aktuálna stabilita 2. riadku (0.0-1.0)
first_line_stability_count = 0          # Počet cyklov stability 1. riadku
second_line_stability_count = 0         # Počet cyklov stability 2. riadku
second_line_growth_count = 0            # Počet cyklov rastu 2. riadku
second_line_growth_detected = False     # Či je detekovaný rast 2. riadku

# Detekčné stavy
detection_phase = "initial"             # "initial" / "first_change" / "analyzing"
mode_decision = "staying_dynamic"       # "staying_dynamic" / "switching_static"
lines_detected = "none"                 # "single" / "double" / "none"

# História pre detekciu zmien
stability_previous_first_line = ""      # Predchádzajúci prvý riadok
stability_previous_second_line = ""     # Predchádzajúci druhý riadok
stability_first_change_detected = False # Či bola detekovaná prvá zmena

# Globálne premenné pre sledovanie aktuálnych riadkov (pre detekciu)
current_first_line = ""  # Aktuálny prvý riadok z dynamického režimu
current_second_line = ""  # Aktuálny druhý riadok z dynamického režimu

# CSV Logging konfigurácia
CSV_LOGGING_ENABLED = True  # Zapnutie/vypnutie CSV loggingu
CSV_LOG_FILE = "log_analysis.csv"  # Názov CSV súboru

# Dynamic mode konfigurácia
DYNAMIC_FIRST_LINE_STABILITY_THRESHOLD = 2  # Počet cyklov pre stabilitu prvého riadku

# Detekcia ľavej strany dynamických titulkov (len pre dynamic mode)
dynamic_subtitle_left_x = None  # X-súradnica ľavej strany dynamických titulkov
dynamic_subtitle_detection_active = False  # Či je aktívna detekcia ľavej strany
dynamic_subtitle_detection_samples = []  # Vzorky X-pozícií pre detekciu
DYNAMIC_SUBTITLE_FILTERING_ENABLED = False  # Či je aktívne filtrovanie podľa detekovanej oblasti (defaultne vypnuté)
USE_TESSERACT_TEXTLINES = False  # Experimentálne: Použiť Tesseract textlines namiesto slov (vypnuté kvôli problémom)

# GUI nastavenia
ALWAYS_ON_TOP = False  # Defaultne vypnuté; prepínateľné v GUI
ALLOW_UPPERCASE_TEXT = True  # Povoliť čítanie textov celých vo veľkých písmenách

# === PREKLADAČ ODSTRÁNENÝ ===
# Prekladač bol úplne odstránený zo systému

# === DEMO REŽIM SPRÁVY ===
# Správa prečítaná po vypršaní demo času
DEMO_EXPIRED_MESSAGE_CS = "Vypršel limit 20 minut demo verze pro dnešek. Buď si zakupte plnou verzi, nebo počkejte do zítřka."
DEMO_EXPIRED_MESSAGE_SK = "Vypršal limit 20 minút demo verzie pre dnes. Buď si zakúpte plnú verziu, alebo počkajte do zajtra."
DEMO_EXPIRED_MESSAGE_EN = "20 minutes demo time limit expired for today. Either purchase the full version or wait until tomorrow."

# Predvolená správa (česky)
DEMO_EXPIRED_MESSAGE = DEMO_EXPIRED_MESSAGE_CS

# Nastavenia pre demo expired TTS
DEMO_EXPIRED_TTS_ENABLED = True  # Či sa má prečítať správa po vypršaní demo času
DEMO_EXPIRED_TTS_VOICE_PREFERENCE = "czech"  # Preferovaný jazyk hlasu ("czech", "slovak", "english", "auto")

# Parametre pre ladenie filtrovania pozície titulkov (nastaviteľné cez GUI)
LEFT_TOLERANCE = 150  # Tolerancia vľavo od detekovanej ľavej strany (px)
RIGHT_TOLERANCE = 150  # Tolerancia vpravo od detekovanej ľavej strany (px)
SUBTITLE_TOLERANCE = 150  # Tolerancia pre titulkové riadky (px)
Y_TOLERANCE = 15  # Tolerancia pre zoskupovanie slov do riadkov (px)
SUBTITLE_Y_THRESHOLD = 0.6  # Prah pre hľadanie titulkov v dolnej časti obrazovky (0.0-1.0)
current_say_process = None # Na uloženie aktuálneho 'say' procesu
tts_lock = threading.Lock() # Zámok pre bezpečný prístup k current_say_process
tts_history = deque(maxlen=3) # História posledných 3 prečítaných textov (skrátené z 20 pre lepšiu detekciu)








# Premenné pre YouTube režim
subtitles = []
current_youtube_url = ""
expected_ocr_video_url = ""
video_time = 0.0
last_synced_real_time = None
last_synced_video_time = 0.0
is_paused_video = False # Zatiaľ sa plne nevyužíva
last_subtitle_index = 0
has_ocr_sync = False
last_enqueued_youtube_text = ""
is_youtube_no_video_page = False  # Premenná pre detekciu YouTube stránky bez videa
failed_subtitle_urls = set()  # Nová množina pre ukladanie URL, pre ktoré sa nepodarilo stiahnuť titulky

# hotkey_listener už sa nepoužíva - klávesové skratky sú teraz cez tkinter
ocr_timer = None
youtube_ocr_timer = None # Nový časovač pre YouTube OCR
context_timer = None
gui_instance = None # Pre prístup k GUI z hotkey handlerov


@dataclasses.dataclass
class Subtitle:
    start_time: float
    end_time: float
    text: str

@dataclasses.dataclass
class OCR_Cycle_Data:
    cycle_id: int
    raw_screenshot: any
    processed_image: any
    raw_ocr_text: str
    cleaned_ocr_text: str
    timestamp: datetime.datetime
    reading_mode: str # Nové pole pre uloženie režimu čítania

# Premenné pre zaznamenávanie anomálií
ANOMALY_REPORT_DIR = IMAGE_SAVE_DIR  # Use the same cross-platform directory
ocr_history = deque(maxlen=10) # Uchováva históriu posledných 10 OCR cyklov
current_tts_cycle_id = None # ID cyklu, ktorého text sa práve číta

# Nové premenné pre dynamický režim - buffery a stav
candidate_line_buffer: str = "" # Buffer pre kandidátsky jednoriadkový text
second_line_buffer: str = ""
previous_first_line: str = ""
previous_second_line: str = ""
previous_single_line_text: str = ""  # Predchádzajúci jednoriadkový text pre dynamické čítanie
recently_read_from_buffer: str = ""  # Text nedávno prečítaný z buffera (aby sa nečítal znovu)
last_processed_dynamic_text: str = ""
first_line_stable_count: int = 0

# --- GUI Update Queue ---
gui_update_queue = queue.Queue()

# --- CSV Logging ---
CSV_LOGGING_ENABLED = True
CSV_LOG_FILE = "log_analysis.csv"

# --- Test Logging ---
TEST_LOGGING_MODE = "app"  # "app", "static", "dynamic"
TEST_LOG_FILES = {
    "app": "app.log",
    "static": "static.log",
    "dynamic": "dynamic.log"
}
current_test_log_file = TEST_LOG_FILES["app"]



# NLLB System Settings
NLLB_DEVICE = "auto"  # auto, cpu, mps, cuda
NLLB_MODEL_NAME = "facebook/nllb-200-distilled-600M"
NLLB_TORCH_DTYPE = "auto"  # auto, float16, float32
NLLB_LOW_CPU_MEM_USAGE = True
NLLB_TRUST_REMOTE_CODE = False
