import datetime
import logging
import os
from pathlib import Path
from PIL import Image # Import Image

import common_config as config

def report_anomaly():
    logging.info("Hotkey pre hlásenie anomálie aktivovaný.")

    # Vytvorenie adres<PERSON>ra pre reporty, ak neexistuje
    if not config.ANOMALY_REPORT_DIR.exists():
        config.ANOMALY_REPORT_DIR.mkdir(parents=True, exist_ok=True)

    # Vytvorenie podadresára pre konkrétnu anomáliu
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    anomaly_dir = config.ANOMALY_REPORT_DIR / timestamp
    anomaly_dir.mkdir(parents=True, exist_ok=True)

    # Vyhľadanie dát OCR cyklu na základe current_tts_cycle_id
    ocr_data_to_report = None
    if config.current_tts_cycle_id is not None:
        for data in reversed(config.ocr_history): # Hľad<PERSON>me od najnovších po najstaršie
            if data.cycle_id == config.current_tts_cycle_id:
                ocr_data_to_report = data
                break

    if ocr_data_to_report:
        # Uloženie surového snímku obrazovky
        if ocr_data_to_report.raw_screenshot:
            raw_screenshot_path = anomaly_dir / f"raw_screenshot_{timestamp}.png"
            try:
                sct_img_pil = Image.frombytes("RGB", ocr_data_to_report.raw_screenshot.size, ocr_data_to_report.raw_screenshot.rgb)
                sct_img_pil.save(str(raw_screenshot_path))
                logging.info(f"Surový snímok obrazovky uložený: {raw_screenshot_path}")
            except Exception as e:
                logging.error(f"Chyba pri ukladaní surového snímku obrazovky: {e}")

        # Uloženie spracovaného obrázka (presne ten, ktorý ide do Tesseract OCR)
        if ocr_data_to_report.processed_image:
            processed_image_path = anomaly_dir / f"tesseract_input_{timestamp}.png"
            try:
                ocr_data_to_report.processed_image.save(str(processed_image_path))
                logging.info(f"Tesseract input obrázok uložený: {processed_image_path}")

                # Uloženie aj kópie s popisným názvom
                descriptive_path = anomaly_dir / f"TESSERACT_INPUT_FINAL.png"
                ocr_data_to_report.processed_image.save(str(descriptive_path))
                logging.info(f"Tesseract input obrázok (popisný názov): {descriptive_path}")
            except Exception as e:
                logging.error(f"Chyba pri ukladaní spracovaného obrázka: {e}")

        # Uloženie detailného textového reportu
        report_path = anomaly_dir / f"anomaly_report_{timestamp}.txt"
        with open(report_path, "w", encoding="utf-8") as f:
            f.write("=" * 80 + "\n")
            f.write("DETAILNÝ REPORT ANOMÁLIE OCR/TTS SPRACOVANIA\n")
            f.write("=" * 80 + "\n")
            f.write(f"Čas zaznamenania anomálie: {datetime.datetime.now()}\n")
            f.write(f"ID OCR cyklu: {ocr_data_to_report.cycle_id}\n")
            f.write(f"Čas OCR cyklu: {ocr_data_to_report.timestamp}\n")
            f.write(f"Režim čítania: {ocr_data_to_report.reading_mode}\n")

            # Základné konfiguračné parametre
            f.write("\n" + "=" * 50 + "\n")
            f.write("KONFIGURAČNÉ PARAMETRE\n")
            f.write("=" * 50 + "\n")
            f.write(f"OCR interval: {config.current_ocr_interval}s\n")
            f.write(f"Reading OCR interval: {config.READING_OCR_INTERVAL}s\n")
            f.write(f"Detection OCR interval: {config.DETECTION_OCR_INTERVAL}s\n")
            f.write(f"TTS rýchlosť: {config.TTS_RATE}\n")
            f.write(f"TTS hlasitosť: {config.TTS_VOLUME}\n")
            f.write(f"OCR threshold: {config.OCR_THRESHOLD}\n")
            f.write(f"Save images: {config.SAVE_IMAGES}\n")

            # Parametre filtrovania pozície
            f.write("\n" + "-" * 30 + "\n")
            f.write("PARAMETRE FILTROVANIA POZÍCIE\n")
            f.write("-" * 30 + "\n")
            f.write(f"LEFT_TOLERANCE: {getattr(config, 'LEFT_TOLERANCE', 'N/A')}px\n")
            f.write(f"RIGHT_TOLERANCE: {getattr(config, 'RIGHT_TOLERANCE', 'N/A')}px\n")
            f.write(f"SUBTITLE_TOLERANCE: {getattr(config, 'SUBTITLE_TOLERANCE', 'N/A')}px\n")
            f.write(f"Y_TOLERANCE: {getattr(config, 'Y_TOLERANCE', 'N/A')}px\n")
            f.write(f"SUBTITLE_Y_THRESHOLD: {getattr(config, 'SUBTITLE_Y_THRESHOLD', 'N/A')}\n")

            # Stav dynamického režimu
            if ocr_data_to_report.reading_mode == "dynamic":
                f.write("\n" + "-" * 30 + "\n")
                f.write("STAV DYNAMICKÉHO REŽIMU\n")
                f.write("-" * 30 + "\n")
                f.write(f"Ľavá strana titulkov: {config.dynamic_subtitle_left_x}\n")
                f.write(f"Detekcia aktívna: {config.dynamic_subtitle_detection_active}\n")
                f.write(f"Vzorky detekcie: {len(config.dynamic_subtitle_detection_samples)}/5\n")
                f.write(f"Filtrovanie povolené: {config.DYNAMIC_SUBTITLE_FILTERING_ENABLED}\n")
                f.write(f"Prvý riadok stabilný count: {config.first_line_stable_count}\n")
                f.write(f"Predchádzajúci prvý riadok: '{config.previous_first_line}'\n")
                f.write(f"Predchádzajúci druhý riadok: '{config.previous_second_line}'\n")
                f.write(f"Kandidátsky buffer: '{config.candidate_line_buffer}'\n")
                f.write(f"Druhý riadok buffer: '{config.second_line_buffer}'\n")

            # OCR dáta a spracovanie obrázkov
            f.write("\n" + "=" * 50 + "\n")
            f.write("OCR SPRACOVANIE - REŤAZEC KROKOV\n")
            f.write("=" * 50 + "\n")
            f.write("ULOŽENÉ OBRÁZKY:\n")
            f.write("-" * 40 + "\n")
            f.write(f"• raw_screenshot_{timestamp}.png - Orezaný screenshot (dolná tretina obrazovky)\n")
            f.write(f"• tesseract_input_{timestamp}.png - Spracovaný obrázok pre Tesseract\n")
            f.write(f"• TESSERACT_INPUT_FINAL.png - Kópia s popisným názvom\n")

            f.write("\n1. SUROVÝ OCR TEXT (priamo z Tesseract)\n")
            f.write("-" * 40 + "\n")
            f.write(ocr_data_to_report.raw_ocr_text if ocr_data_to_report.raw_ocr_text else "(žiadny text)\n")

            f.write("\n2. VYČISTENÝ OCR TEXT (po clean_text)\n")
            f.write("-" * 40 + "\n")
            cleaned_text_str = "\n".join(ocr_data_to_report.cleaned_ocr_text) if isinstance(ocr_data_to_report.cleaned_ocr_text, list) else str(ocr_data_to_report.cleaned_ocr_text)
            f.write(cleaned_text_str if cleaned_text_str else "(žiadny text)\n")

            # Informácie o spracovaní obrázka
            f.write("\n3. SPRACOVANIE OBRÁZKA\n")
            f.write("-" * 40 + "\n")
            f.write("Kroky spracovania obrázka pre Tesseract:\n")
            f.write("1. Screenshot celej obrazovky\n")
            f.write("2. Orezanie na dolnú tretinu (67-100% výšky obrazovky)\n")
            f.write("3. Konverzia na RGB\n")
            f.write("4. Binarizácia (threshold)\n")
            f.write("5. Zvýšenie kontrastu\n")
            f.write("6. Inverzia farieb (biely text na čiernom pozadí)\n")
            f.write("7. Vstup do Tesseract OCR\n")
            f.write(f"\nPozorovanie: Skontrolujte súbor 'TESSERACT_INPUT_FINAL.png'\n")
            f.write("pre vizuálnu kontrolu, ako Tesseract vidí text.\n")

            # Posledné CSV záznamy pre kontext
            f.write("\n" + "=" * 50 + "\n")
            f.write("POSLEDNÉ CSV ZÁZNAMY (kontext)\n")
            f.write("=" * 50 + "\n")
            try:
                import csv_logger
                recent_logs = csv_logger.get_recent_logs(10)  # Posledných 10 záznamov
                if recent_logs:
                    for log_entry in recent_logs:
                        f.write(f"CycleID={log_entry.get('cycle_id', 'N/A')}: {log_entry.get('event_type', 'N/A')} - {log_entry.get('description', 'N/A')}\n")
                else:
                    f.write("Žiadne CSV záznamy dostupné\n")
            except Exception as e:
                f.write(f"Chyba pri načítaní CSV záznamov: {e}\n")

            # História OCR cyklov
            f.write("\n" + "=" * 50 + "\n")
            f.write("HISTÓRIA POSLEDNÝCH OCR CYKLOV\n")
            f.write("=" * 50 + "\n")
            for i, ocr_data in enumerate(reversed(list(config.ocr_history)[-5:])):  # Posledných 5
                f.write(f"\n--- Cyklus {ocr_data.cycle_id} (pred {i+1} cyklami) ---\n")
                f.write(f"Čas: {ocr_data.timestamp}\n")
                f.write(f"Režim: {ocr_data.reading_mode}\n")
                f.write(f"Surový text: {ocr_data.raw_ocr_text[:100] if ocr_data.raw_ocr_text else '(žiadny)'}{'...' if ocr_data.raw_ocr_text and len(ocr_data.raw_ocr_text) > 100 else ''}\n")

            f.write("\n" + "=" * 50 + "\n")
            f.write("KOMENTÁR/POPIS ANOMÁLIE (doplňte ručne)\n")
            f.write("=" * 50 + "\n")
            f.write("Čo ste očakávali, že sa stane?\n\n")
            f.write("Čo sa skutočne stalo?\n\n")
            f.write("Ďalšie poznámky:\n\n")

        logging.info(f"Report anomálie uložený: {report_path}")
    else:
        logging.warning("Nenašli sa žiadne relevantné dáta OCR cyklu pre hlásenie anomálie.")
        # Fallback - vytvoríme okamžitý screenshot a report
        create_immediate_anomaly_report(anomaly_dir, timestamp)

def create_immediate_anomaly_report(anomaly_dir, timestamp):
    """
    Vytvorí okamžitý anomaly report so screenshotom a OCR analýzou.
    Používa sa keď nie sú dostupné historické dáta.
    """
    try:
        import mss
        import ocr_core

        logging.info("Vytváram okamžitý anomaly report...")

        # Okamžitý screenshot - používame rovnakú logiku ako normálny OCR proces
        sct_img = ocr_core.capture_screen_region()
        if not sct_img:
            logging.error("Chyba pri vytváraní okamžitého screenshotu")
            return

        # Uloženie surového screenshotu (už orezaného na dolnú tretinu)
        raw_screenshot_path = anomaly_dir / f"immediate_raw_screenshot_{timestamp}.png"
        sct_img_pil = Image.frombytes("RGB", sct_img.size, sct_img.rgb)
        sct_img_pil.save(str(raw_screenshot_path))

        # Spracovanie pre OCR
        processed_img = ocr_core.preprocess_image_for_ocr(sct_img)
        if processed_img:
            # Uloženie spracovaného obrázka
            processed_image_path = anomaly_dir / f"immediate_tesseract_input_{timestamp}.png"
            processed_img.save(str(processed_image_path))

            descriptive_path = anomaly_dir / f"IMMEDIATE_TESSERACT_INPUT.png"
            processed_img.save(str(descriptive_path))

            # Vykonanie OCR
            if config.reading_mode == 'dynamic':
                ocr_text = ocr_core.perform_dynamic_ocr(processed_img)
            else:
                ocr_text = ocr_core.perform_static_ocr(processed_img)
        else:
            ocr_text = "(chyba pri spracovaní obrázka)"

        # Vytvorenie textového reportu
        report_path = anomaly_dir / f"immediate_anomaly_report_{timestamp}.txt"
        with open(report_path, "w", encoding="utf-8") as f:
            f.write("=" * 80 + "\n")
            f.write("OKAMŽITÝ ANOMALY REPORT (bez historických dát)\n")
            f.write("=" * 80 + "\n")
            f.write(f"Čas vytvorenia: {datetime.datetime.now()}\n")
            f.write(f"Režim čítania: {config.reading_mode}\n")
            f.write(f"OCR interval: {config.current_ocr_interval}s\n")

            # Aktuálne parametre
            f.write("\n" + "=" * 50 + "\n")
            f.write("AKTUÁLNE PARAMETRE FILTROVANIA\n")
            f.write("=" * 50 + "\n")
            f.write(f"LEFT_TOLERANCE: {getattr(config, 'LEFT_TOLERANCE', 'N/A')}px\n")
            f.write(f"RIGHT_TOLERANCE: {getattr(config, 'RIGHT_TOLERANCE', 'N/A')}px\n")
            f.write(f"SUBTITLE_TOLERANCE: {getattr(config, 'SUBTITLE_TOLERANCE', 'N/A')}px\n")
            f.write(f"Y_TOLERANCE: {getattr(config, 'Y_TOLERANCE', 'N/A')}px\n")
            f.write(f"SUBTITLE_Y_THRESHOLD: {getattr(config, 'SUBTITLE_Y_THRESHOLD', 'N/A')}\n")

            # Stav dynamického režimu
            if config.reading_mode == "dynamic":
                f.write(f"Ľavá strana titulkov: {config.dynamic_subtitle_left_x}\n")
                f.write(f"Detekcia aktívna: {config.dynamic_subtitle_detection_active}\n")

            # OCR výsledky
            f.write("\n" + "=" * 50 + "\n")
            f.write("OKAMŽITÉ OCR VÝSLEDKY\n")
            f.write("=" * 50 + "\n")
            f.write("ULOŽENÉ OBRÁZKY:\n")
            f.write(f"• immediate_raw_screenshot_{timestamp}.png - Okamžitý screenshot (dolná tretina obrazovky)\n")
            f.write(f"• immediate_tesseract_input_{timestamp}.png - Spracovaný pre Tesseract\n")
            f.write(f"• IMMEDIATE_TESSERACT_INPUT.png - Kópia s popisným názvom\n")
            f.write("\nOCR TEXT:\n")
            f.write("-" * 40 + "\n")
            f.write(ocr_text if ocr_text else "(žiadny text rozpoznaný)\n")

            f.write("\n" + "=" * 50 + "\n")
            f.write("POZNÁMKY\n")
            f.write("=" * 50 + "\n")
            f.write("Tento report bol vytvorený okamžite bez historických dát.\n")
            f.write("Skontrolujte obrázky a OCR výsledky pre debugging.\n")
            f.write("\nKomentár/Popis problému (doplňte ručne):\n\n")

        logging.info(f"Okamžitý anomaly report uložený: {report_path}")

    except Exception as e:
        logging.error(f"Chyba pri vytváraní okamžitého anomaly reportu: {e}")
        # Minimálny fallback
        fallback_path = anomaly_dir / f"anomaly_report_{timestamp}_minimal.txt"
        with open(fallback_path, "w", encoding="utf-8") as f:
            f.write(f"Čas zaznamenania anomálie: {datetime.datetime.now()}\n")
            f.write(f"Chyba pri vytváraní detailného reportu: {e}\n")
            f.write("\n-- Žiadne dáta OCR cyklu neboli nájdené pre aktuálny TTS text. --\n")
        logging.info(f"Minimálny fallback report uložený: {fallback_path}")