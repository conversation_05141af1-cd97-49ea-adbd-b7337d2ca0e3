#!/bin/bash

# 🍎 Build script pre macOS aplikáciu pomocou py2app
# Vytvorí universal binary pre ARM aj Intel Mac

set -e  # Ukončí script pri chybe

echo "🍎 Subtitle Reader - macOS Build Script"
echo "========================================"

# Farby pre výstup
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Funkcie pre farebný výstup
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Kontrola systému
print_info "Kontrolujem systém..."
if [[ "$OSTYPE" != "darwin"* ]]; then
    print_error "Tento script funguje len na macOS!"
    exit 1
fi

# Aktivuj virtual environment ak existuje
if [ -d "subtitle_reader_env" ]; then
    print_info "Aktivujem virtual environment..."
    source subtitle_reader_env/bin/activate
fi

# Kontrola Python verzie
print_info "Kontrolujem Python..."
PYTHON_VERSION=$(python --version 2>&1 | cut -d' ' -f2)
print_info "Python verzia: $PYTHON_VERSION"
print_info "Python cesta: $(which python)"

# Kontrola py2app
if ! python -c "import py2app" 2>/dev/null; then
    print_error "py2app nie je nainštalované!"
    print_info "Spusti najprv: source subtitle_reader_env/bin/activate && pip install py2app"
    exit 1
fi

# Kontrola závislostí
print_info "Kontrolujem závislosti..."
if ! python -c "import PyQt6" 2>/dev/null; then
    print_error "PyQt6 nie je nainštalované!"
    print_info "Spusti najprv: source subtitle_reader_env/bin/activate && pip install PyQt6"
    exit 1
fi

# Vyčisti staré build súbory
print_info "Čistím staré build súbory..."
rm -rf build/
rm -rf dist/
rm -rf *.egg-info/

# Vytvor build adresár v domovskom priečinku
BUILD_DIR="$HOME/SubtitleReader_Build"
print_info "Vytváram build adresár: $BUILD_DIR"
mkdir -p "$BUILD_DIR"
cd "$BUILD_DIR"

# Skopíruj potrebné súbory
print_info "Kopírujem súbory pre build..."
cp -R "/Applications/SubtitleReader"/* .
rm -rf build/ dist/ *.egg-info/ subtitle_reader_env/

# Aktivuj virtual environment
source "/Applications/SubtitleReader/subtitle_reader_env/bin/activate"

print_success "Príprava dokončená"

# Build aplikácie
print_info "Spúšťam py2app build..."
echo "Toto môže trvať niekoľko minút..."

# Spusti py2app
if python setup.py py2app; then
    print_success "Build dokončený úspešne!"
else
    print_error "Build zlyhal!"
    exit 1
fi

# Kontrola výsledku
APP_PATH="dist/Subtitle Reader.app"
if [ -d "$APP_PATH" ]; then
    print_success "Aplikácia vytvorená: $APP_PATH"
    
    # Zobraz informácie o aplikácii
    APP_SIZE=$(du -sh "$APP_PATH" | cut -f1)
    print_info "Veľkosť aplikácie: $APP_SIZE"
    
    # Kontrola architektúry
    BINARY_PATH="$APP_PATH/Contents/MacOS/Subtitle Reader"
    if [ -f "$BINARY_PATH" ]; then
        print_info "Architektúry:"
        file "$BINARY_PATH" | sed 's/^/  /'
        
        # Kontrola universal binary
        if file "$BINARY_PATH" | grep -q "universal binary"; then
            print_success "Universal binary vytvorený úspešne (ARM + Intel)"
        else
            print_warning "Nie je universal binary"
        fi
    fi
    
    # Test spustenia
    print_info "Testujem spustenie aplikácie..."
    if open "$APP_PATH" --args --test 2>/dev/null; then
        print_success "Aplikácia sa spúšťa správne"
    else
        print_warning "Test spustenia zlyhal (môže byť normálne)"
    fi
    
    echo ""
    print_success "🎉 Build dokončený!"
    echo ""
    print_info "Aplikácia je pripravená v: $APP_PATH"
    print_info "Môžeš ju spustiť dvojklikom v Finderi"
    print_info "Alebo príkazom: open '$APP_PATH'"
    
    # Návod na ďalšie kroky
    echo ""
    print_info "📋 Ďalšie kroky:"
    echo "  1. Otestuj aplikáciu: open '$APP_PATH'"
    echo "  2. Vytvor DMG: ./create_dmg.sh"
    echo "  3. Podpíš aplikáciu (voliteľné): codesign -s 'Developer ID' '$APP_PATH'"
    
else
    print_error "Aplikácia sa nevytvorila!"
    print_info "Skontroluj chyby vyššie"
    exit 1
fi

print_success "Hotovo! 🚀"
