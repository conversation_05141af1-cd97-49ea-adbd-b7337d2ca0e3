#!/bin/bash

# 🍎 macOS .command súbor pre Subtitle Reader
# Tento súbor sa dá spustiť dvojklikom v Finderi

echo "🚀 Subtitle Reader - macOS Launcher"
echo "===================================="

# Získaj adresár aplikácie
APP_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
cd "$APP_DIR"

echo "📁 Spúšťam z: $APP_DIR"

# Aktivuj virtual environment ak existuje
if [ -d "venv" ]; then
    echo "🔧 Aktivujem virtual environment..."
    source venv/bin/activate
elif [ -d ".venv" ]; then
    echo "🔧 Aktivujem virtual environment..."
    source .venv/bin/activate
fi

# Spusti aplikáciu
echo "🚀 Spúšťam Subtitle Reader..."
if command -v python3 &> /dev/null; then
    python3 main_qt.py
else
    python main_qt.py
fi

# Počkaj na stlačenie klávesy pred zatvorením terminálu
echo ""
echo "👋 Aplikácia sa ukončila."
read -p "Stlač Enter pre zatvorenie tohto okna..."
