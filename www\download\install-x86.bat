@echo off
chcp 65001 >nul
setlocal enableextensions enabledelayedexpansion

echo.
echo ========================================================
echo   Subtitle Reader - Automatick<PERSON> in<PERSON> (x86)
echo ========================================================
echo.

:: Over, či bežíme ako správca (vyžaduje sa pre Tesseract a PATH)
net session >nul 2>&1
if %errorLevel% NEQ 0 (
    echo ❌ Tento inštalátor musí bežať s právami SPRÁVCA.
    echo    Kliknite pravým na install-x86.bat a zvoľte "Spustiť ako správca".
    pause
    exit /b 1
)

:: Detekcia architektúry OS
echo 🔍 Kontrolujem architektúru systému...
set ARCH=%PROCESSOR_ARCHITECTURE%
echo    ARCH=%ARCH%
if /I "%ARCH%"=="ARM64" (
    echo ❌ ARM64 nie je podporované v tomto inštalátore (použite ARM64 balíček)
    pause
    exit /b 1
)
:: (Voliteľné) Jednosúborový režim: stiahnutie balíčka aplikácie, ak chýbajú súbory
:: Nastav URL ZIP balíčka do APP_ZIP_URL pre online inštaláciu iba s týmto .bat
set "APP_ZIP_URL=https://www.voxoloxo.com/download/subtitle-reader-x86.zip"

if not exist main_qt.py (
    echo ℹ️ Aplikačné súbory neboli nájdené v aktuálnom priečinku.
    if exist subtitle-reader-x86\main_qt.py (
        echo   🔎 Našiel som podpriečinok 'subtitle-reader-x86' – použijem ho.
        pushd subtitle-reader-x86
    ) else if not "%APP_ZIP_URL%"=="" (
        echo   🌐 Sťahujem balíček aplikácie z: %APP_ZIP_URL%
        powershell -NoProfile -ExecutionPolicy Bypass -Command "Invoke-WebRequest -UseBasicParsing -Uri '%APP_ZIP_URL%' -OutFile 'payload.zip'" || (
            echo ❌ Nepodarilo sa stiahnuť balíček aplikácie
            pause & exit /b 1
        )
        echo   📦 Rozbaľujem balíček...
        powershell -NoProfile -ExecutionPolicy Bypass -Command "Expand-Archive -Path 'payload.zip' -DestinationPath '.' -Force" || (
            echo ❌ Nepodarilo sa rozbaliť balíček
            pause & exit /b 1
        )
        del payload.zip 2>nul
        if exist subtitle-reader-x86\main_qt.py (
            pushd subtitle-reader-x86
        ) else (
            echo ⚠️ Po rozbalení som nenašiel 'subtitle-reader-x86\\main_qt.py'. Skontrolujte balíček.
            pause & exit /b 1
        )
    ) else (
        echo   👉 Nastavte premennú APP_ZIP_URL na URL ZIP balíčka alebo skopírujte sem súbory aplikácie.
        echo      Príklad: set "APP_ZIP_URL=https://server/subtitle-reader-x86.zip" a spustite znovu.
        pause & exit /b 1
    )
)


:: 1) Python 32-bit
echo.
echo 🐍 Kontrolujem Python...
where python >nul 2>&1
if %errorLevel% EQU 0 (
    for /f "usebackq tokens=*" %%A in (`python -c "import platform;print(platform.architecture()[0])"`) do set PYARCH=%%A
    echo    Nájdený Python architektúry: %PYARCH%
) else (
    set PYARCH=
)

if "%PYARCH%"=="32bit" (
    echo ✅ Python 32-bit je prítomný
) else (
    echo ❗ Python 32-bit nebol nájdený. Inštalujem Python 3.11 (x86)...
    set PY_URL=https://www.python.org/ftp/python/3.11.9/python-3.11.9.exe
    set PY_EXE=python-3.11.9-x86.exe
    powershell -NoProfile -ExecutionPolicy Bypass -Command "Invoke-WebRequest -UseBasicParsing -Uri '%PY_URL%' -OutFile '%PY_EXE%'" || (
        echo ❌ Nepodarilo sa stiahnuť Python inštalátor
        pause
        exit /b 1
    )
    echo 🔧 Spúšťam Python inštalátor (môže trvať pár minút)...
    "%PY_EXE%" /quiet InstallAllUsers=1 PrependPath=1 Include_test=0 || (
        echo ❌ Inštalácia Python zlyhala
        pause
        exit /b 1
    )
    del "%PY_EXE%" 2>nul

    :: Skús obnoviť PATH v tomto okne
    set "OLDPATH=%PATH%"
    for /f "usebackq tokens=*" %%A in (`where python 2^>nul`) do set PYEXE=%%A
    if not defined PYEXE (
        echo ⚠️ PATH nebol aktualizovaný. Zatvorte toto okno a spustite install-x86.bat znova.
        pause
        exit /b 1
    )
)

:: 2) Virtuálne prostredie
echo.
echo 🔧 Vytváram virtuálne prostredie (.venv)...
if exist .venv (
    echo   ♻️ Odstraňujem existujúce .venv
    rmdir /s /q .venv
)
python -m venv .venv || (
    echo ❌ Nepodarilo sa vytvoriť .venv
    pause
    exit /b 1
)

echo ✅ Virtuálne prostredie vytvorené

:: 3) Aktivácia .venv
echo.
echo 🔧 Aktivujem .venv...
call .venv\Scripts\activate.bat || (
    echo ❌ Nepodarilo sa aktivovať .venv
    pause
    exit /b 1
)

:: 4) Aktualizácia pip a inštalácia závislostí
echo.
echo 📦 Aktualizujem pip a inštalujem závislosti (x86)...
python -m pip install --upgrade pip wheel setuptools
if exist requirements_x86.txt (
    python -m pip install -r requirements_x86.txt || (
        echo ❌ Inštalácia závislostí zlyhala
        pause
        exit /b 1
    )
) else (
    echo ⚠️ requirements_x86.txt nebol nájdený, inštalujem minimum...
    python -m pip install PyQt6 pytesseract Pillow pyttsx3 pywin32 WMI pynput mss RapidFuzz Levenshtein pyperclip packaging six pywebview requests comtypes
)

:: 5) Inštalácia Tesseract OCR (32-bit)
echo.
echo 🔍 Kontrolujem Tesseract OCR...
tesseract --version >nul 2>&1
if errorlevel 1 (
    echo ❗ Tesseract nie je nainštalovaný. Inštalujem 32-bit verziu...
    set TESS_URL=https://github.com/UB-Mannheim/tesseract/releases/download/v5.3.3.20231005/tesseract-ocr-w32-setup-5.3.3.20231005.exe
    set TESS_EXE=tesseract-x86-installer.exe
    powershell -NoProfile -ExecutionPolicy Bypass -Command "Invoke-WebRequest -UseBasicParsing -Uri '%TESS_URL%' -OutFile '%TESS_EXE%'" || (
        echo ❌ Nepodarilo sa stiahnuť Tesseract inštalátor
        echo    Prosím nainštalujte ručne z: https://github.com/UB-Mannheim/tesseract/releases
        pause
        exit /b 1
    )
    echo 🔧 Spúšťam Tesseract inštalátor (ručný sprievodca)...
    "%TESS_EXE%"
    echo ⏳ Počkajte na dokončenie inštalácie a stlačte Enter...
    pause >nul
    del "%TESS_EXE%" 2>nul
) else (
    echo ✅ Tesseract OCR je dostupný
)

:: 6) Český jazyk pre Tesseract
echo.
echo 🇨🇿 Overujem český jazyk pre Tesseract (ces)...
if exist "C:\Program Files\Tesseract-OCR\tessdata\ces.traineddata" (
    echo ✅ Český jazyk už je nainštalovaný
) else (
    echo 📥 Sťahujem ces.traineddata...
    powershell -NoProfile -ExecutionPolicy Bypass -Command "Invoke-WebRequest -UseBasicParsing -Uri 'https://github.com/tesseract-ocr/tessdata/raw/main/ces.traineddata' -OutFile 'ces.traineddata'" && (
        echo 📂 Kopírujem do Tesseract tessdata...
        copy /Y ces.traineddata "C:\Program Files\Tesseract-OCR\tessdata\ces.traineddata" >nul
        del ces.traineddata
        echo ✅ Český jazyk nainštalovaný
    )
)

:: 7) Rýchle testy
echo.
echo 🧪 Spúšťam rýchle testy...
python -c "print('✅ Python OK')" || echo ❌ Python test zlyhal
python -c "from PyQt6 import QtWidgets; print('✅ PyQt6 OK')" || echo ❌ PyQt6 test zlyhal
python -c "import pytesseract; print('✅ pytesseract OK')" || echo ❌ pytesseract test zlyhal
python -c "import pyttsx3; print('✅ pyttsx3 OK')" || echo ❌ pyttsx3 test zlyhal

:: 8) Spustenie aplikácie
echo.
echo ========================================================
echo   🎉 INŠTALÁCIA DOKONČENÁ!
echo ========================================================
echo.
echo 🚀 Spúšťam Subtitle Reader...
echo.

set TESSERACT_CMD=C:\Program Files\Tesseract-OCR\tesseract.exe
if exist run_app_x86.bat (
    call run_app_x86.bat
) else (
    python main_qt.py
)

echo.
echo 👋 Aplikácia ukončená. Pre opätovné spustenie použite run_app_x86.bat
echo.
pause
endlocal

