#!/usr/bin/env python3
"""
Test OneCore hlasov na Windows
"""

import asyncio
import logging
import sys
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

async def test_onecore_voices():
    """Test OneCore hlasov."""
    print("🚀 OneCore Voices Test")
    print("=" * 60)
    
    try:
        # Import WinRT modules
        from winrt.windows.media.speechsynthesis import SpeechSynthesizer
        from winrt.windows.storage.streams import DataReader
        
        print("✅ WinRT moduly úspešne importované")
        
        # Create synthesizer
        synthesizer = SpeechSynthesizer()
        print("✅ SpeechSynthesizer vytvorený")
        
        # List all available voices
        print(f"\n🎤 Dostupné OneCore hlasy:")

        # Try different ways to get voices
        voices = None
        try:
            voices = SpeechSynthesizer.all_voices
            print("✅ Použité SpeechSynthesizer.all_voices")
        except:
            try:
                voices = await SpeechSynthesizer.get_all_voices_async()
                print("✅ Použité SpeechSynthesizer.get_all_voices_async()")
            except:
                try:
                    voices = list(SpeechSynthesizer.all_voices)
                    print("✅ Použité list(SpeechSynthesizer.all_voices)")
                except Exception as e:
                    print(f"❌ Chyba pri získavaní hlasov: {e}")
                    # Try to inspect the synthesizer object
                    print(f"🔍 Synthesizer attributes: {dir(synthesizer)}")
                    return False

        print(f"📊 Celkovo nájdených hlasov: {len(voices) if voices else 0}")
        
        for i, voice in enumerate(voices):
            print(f"   {i+1:2d}. {voice.display_name}")
            print(f"       Jazyk: {voice.language}")
            print(f"       Pohlavie: {voice.gender}")
            print(f"       ID: {voice.id}")
            print()
        
        if len(voices) == 0:
            print("❌ Žiadne OneCore hlasy nenájdené!")
            print("💡 Skúste nainštalovať hlasy v Windows Settings > Time & Language > Speech")
            return False
        
        # Test speech synthesis
        print("🔍 Testovanie syntézy reči...")
        
        # Use first available voice
        test_voice = voices[0]
        synthesizer.voice = test_voice
        
        print(f"🎤 Používam hlas: {test_voice.display_name}")
        
        # Synthesize text
        test_text = "Hello, this is a test of OneCore speech synthesis."
        print(f"📝 Text: {test_text}")
        
        stream = await synthesizer.synthesize_text_to_stream_async(test_text)
        print(f"✅ Syntéza úspešná, veľkosť streamu: {stream.size} bajtov")
        
        # Read stream data
        reader = DataReader(stream)
        await reader.load_async(stream.size)
        data = bytearray(stream.size)
        reader.read_bytes(data)
        
        # Save to file
        output_file = Path("onecore_test.wav")
        with open(output_file, "wb") as f:
            f.write(data)
        
        print(f"💾 Audio uložené do: {output_file}")
        print(f"📊 Veľkosť súboru: {len(data)} bajtov")
        
        # Try to play the audio (optional)
        try:
            import pygame
            pygame.mixer.init()
            pygame.mixer.music.load(str(output_file))
            pygame.mixer.music.play()
            
            print("🔊 Prehrávam audio...")
            while pygame.mixer.music.get_busy():
                await asyncio.sleep(0.1)
            
            print("✅ Audio prehrané")
            
        except ImportError:
            print("💡 pygame nie je nainštalované - audio sa neprehrá")
        except Exception as e:
            print(f"⚠️ Chyba pri prehrávaní: {e}")
        
        print("\n" + "=" * 60)
        print("🎉 OneCore test úspešný!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Skúste nainštalovať: pip install winrt-Windows.Media.SpeechSynthesis winrt-Windows.Storage.Streams")
        return False
    except Exception as e:
        print(f"❌ Test zlyhal: {e}")
        logging.exception("OneCore test error")
        return False

async def test_voice_selection():
    """Test výberu konkrétneho hlasu."""
    print("\n🔍 Test výberu hlasu...")
    
    try:
        from winrt.windows.media.speechsynthesis import SpeechSynthesizer
        
        synthesizer = SpeechSynthesizer()

        # Try different ways to get voices
        voices = None
        try:
            voices = SpeechSynthesizer.all_voices
        except:
            try:
                voices = await SpeechSynthesizer.get_all_voices_async()
            except:
                voices = []
        
        # Look for specific languages
        languages_to_test = ['en', 'sk', 'cs', 'de', 'fr']
        
        for lang in languages_to_test:
            matching_voices = [v for v in voices if lang.lower() in v.language.lower()]
            if matching_voices:
                print(f"✅ {lang.upper()}: {len(matching_voices)} hlasov")
                for voice in matching_voices[:3]:  # Show max 3 voices per language
                    print(f"   - {voice.display_name}")
            else:
                print(f"❌ {lang.upper()}: žiadne hlasy")
        
        return True
        
    except Exception as e:
        print(f"❌ Voice selection test zlyhal: {e}")
        return False

if __name__ == "__main__":
    print("🎯 Spúšťam OneCore voices test...")
    
    async def main():
        # Test basic functionality
        basic_success = await test_onecore_voices()
        
        # Test voice selection
        selection_success = await test_voice_selection()
        
        print("\n" + "=" * 60)
        print("📊 FINÁLNE VÝSLEDKY:")
        print(f"   🔧 OneCore Basic: {'✅ ÚSPEŠNÝ' if basic_success else '❌ ZLYHAL'}")
        print(f"   🎤 Voice Selection: {'✅ ÚSPEŠNÝ' if selection_success else '❌ ZLYHAL'}")
        
        if basic_success and selection_success:
            print("\n🎉 ONECORE HLASY SÚ FUNKČNÉ!")
            return 0
        else:
            print("\n❌ Niektoré testy zlyhali")
            return 1
    
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
