@echo off
echo ========================================
echo   Subtitle Reader - Safe Mode
echo ========================================
echo.
echo ⚠️ POZOR: TTS je zakázané kvôli nestabilite
echo 🔍 Len OCR funkcie sú aktívne
echo.

REM Nastavenie premenných prostredia
set TESSERACT_CMD=C:\Program Files\Tesseract-OCR\tesseract.exe
set PYTHONIOENCODING=utf-8
set DISABLE_TTS=1

echo 🔧 Aktivujem virtuálne prostredie...
call .venv\Scripts\activate.bat

echo 🖥️ Spúšťam bezpečnú aplikáciu...
echo 📍 GUI okno by sa malo zobraziť
echo.

python safe_main.py

echo.
echo 👋 Bezpečná aplikácia ukončená
pause
