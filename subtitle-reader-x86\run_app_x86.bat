@echo off
chcp 65001 >nul
echo.
echo ========================================================
echo   Subtitle Reader - Spustenie aplikácie (x86)
echo ========================================================
echo.

:: Kontrola virtuálneho prostredia
if not exist .venv (
    echo ❌ Virtuálne prostredie neexistuje
    echo    Prosím spustite najprv install_x86.bat
    pause
    exit /b 1
)

:: Aktivácia virtuálneho prostredia
echo 🔧 Aktivujem virtuálne prostredie...
call .venv\Scripts\activate.bat

:: Nastavenie Tesseract cesty
set TESSERACT_CMD=C:\Program Files\Tesseract-OCR\tesseract.exe

:: Kontrola hlavného súboru
if not exist main_qt.py (
    echo ❌ Hlavný súbor main_qt.py neexistuje
    echo    Prosím skontrolujte inštaláciu
    pause
    exit /b 1
)

:: Spustenie aplikácie
echo 🚀 Spúšťam Subtitle Reader...
echo.
python main_qt.py

echo.
echo 👋 Aplikácia ukončená
pause
