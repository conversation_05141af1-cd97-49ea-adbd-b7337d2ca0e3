{"app_title": "Čtečka titulků", "main_tab": "Hlavní ovládání", "debug_tab": "Laděn<PERSON> parametrů", "detection_tab": "<PERSON><PERSON><PERSON> titulků", "start_reading": "Spustit čtení", "stop_reading": "<PERSON><PERSON><PERSON><PERSON>", "hotkey_hint": "nebo stiskněte Cmd+Alt+Ctrl+V", "mode_group": "<PERSON><PERSON><PERSON>", "mode_full_auto": "Plně automatický", "mode_static": "Statický", "mode_dynamic": "Dynamický", "ocr_group": "OCR", "ocr_threshold": "Prahování OCR:", "language_group": "Jazyk", "app_language": "Jazyk aplikace:", "reading_language": "Jazyk čtení titulků:", "tts_group": "Řeč (TTS)", "speech_rate": "Rychlost řeči:", "speech_volume": "Hlasitost řeči:", "allow_uppercase": "Povolit čtení velkých písmen", "perf_speed": "Rychlost (beam=1)", "perf_balanced": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (beam=4)", "perf_quality": "<PERSON><PERSON><PERSON> (beam=5)", "perf_custom": "Vlastní", "tts_detecting_subtitles": "Detekuji typ titulků.", "tts_starting_continuous": "Spouštím kontinuální detekci titulků.", "tts_starting_reading": "Začínám Ti číst titulky.", "tts_ending_reading": "Končím č<PERSON>í.", "tts_detected_dynamic": "Detekovan<PERSON> t<PERSON>.", "tts_detected_static": "Detekovan<PERSON> t<PERSON>.", "tts_switched_dynamic": "Přepnul jsem na dynamické zpracování.", "tts_switched_static": "Přepnul jsem na statické zpracování.", "tts_switched_to_static": "Prepnuté na statické titulky.", "demo_expired_message": "Vypršel limit 20 minut demo verze pro dnešek. Buď si zakupte plnou verzi, nebo počkejte do zítřka.", "demo_time_remaining": "🔒 <PERSON><PERSON> verze, pro dnešek Ti zbývá ještě {time}", "purchase_button": "Zakoupit na rok", "full_version_status": "🔓 Plná verze, platnost do {expires_at} ({days_remaining} dní)", "demo_expired_title": "Demo čas vypršal", "demo_expired_dialog": "<PERSON><PERSON>š denný demo čas vypršal.\n\nPre pokračovanie v používaní si zakúpte plnú verziu na rok.\n\nDemo čas sa obnoví zajtra.", "demo_test_button": "Demo verze", "full_test_button": "Plná verze", "tts_test_button": "🔊 Test TTS", "toolbar_actions": "Ak<PERSON>", "reset_completed_title": "<PERSON><PERSON>", "reset_completed_message": "Všechny nastavení byla obnovena na výchozí hodnoty podle systémového jazyka.", "start_reading_section": "SPUSTIT ČTENÍ", "translator_stats": "Statistiky", "translator_stats_legacy": "Statistiky: Legacy systém", "translator_stats_unavailable": "Statistiky: Multi-translator <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "translator_stats_error": "Statistiky: Chyba inicializace", "reading_allowed": "povoleno", "reading_forbidden": "zakázáno", "uppercase_reading_status": "Čten<PERSON> velk<PERSON>ch písmen je", "keyboard_image_not_found": "Obrázek klávesnice se nena<PERSON>el (keyboard_MAC_200.jpg)", "keyboard_image_error": "Chyba při načítání obrázku:", "performance_speed": "🚀 Rychlost (beam=1)", "performance_balanced": "⚖️ <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (beam=4)", "performance_quality": "🎯 <PERSON><PERSON><PERSON> (beam=5)", "performance_custom": "⚙️ Vlastní", "quality_fast": "⚡ <PERSON><PERSON><PERSON><PERSON>", "quality_balanced": "⚖️ Vyvážený", "quality_quality": "🏆 Kvalitní", "quality_custom": "⚙️ Vlastní", "no_voices_available": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>", "statistics_not_available": "Statistiky: <PERSON><PERSON><PERSON><PERSON>", "statistics_legacy_system": "Statistiky: Legacy systém", "statistics_initialization_error": "Statistiky: Chyba inicializace", "connection_status_testing": "Status: 🔄 Testuji př<PERSON>ní...", "connection_status_invalid_credentials": "Status: ❌ <PERSON><PERSON><PERSON><PERSON>é credentials", "connection_status_success": "Status: ✅ Připojení úspěšné", "connection_status_failed": "Status: ❌ Test přip<PERSON><PERSON><PERSON><PERSON> selhal", "connection_status_error": "Status: ❌ Chyba:", "connection_status_configured": "Status: ✅ Credentials nastavené", "connection_status_not_configured": "Status: <PERSON><PERSON><PERSON>", "filtering_enabled": "ZAPNUTÉ", "filtering_disabled": "VYPNUTÉ", "filtering_active": "Aktivní (X={x}px)", "filtering_detecting": "Detekuje ({samples}/5 vzorků)", "filtering_waiting": "Čeká na detekci", "filtering_all_text": "Bere všechen text bez filtrování", "device_cpu": "CPU", "device_mps": "MPS (Apple Silicon)", "device_cuda": "CUDA (NVIDIA)", "model_status_not_loaded": "<PERSON> <PERSON><PERSON><PERSON>", "model_status_loaded": "<PERSON> nač<PERSON>", "model_status_loading": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> model...", "memory_usage": "Paměť: {memory} MB", "voice_for_reading": "🎤 Hlas pro čtení:", "voice_for_reading_tooltip": "Vyberte hlas pro čtení titulků a systémových hlášek", "available_languages_info": "Dostupné: {count} j<PERSON><PERSON><PERSON> (OCR: {ocr}, TTS: {tts})", "custom_settings_button": "⚙️ Nastavení", "similarity_thresholds": "⚙️ Základní prahy podobnosti", "tts_duplicate_threshold": "TTS duplicitní práh", "stability_similarity": "Stabilita <PERSON> (podobnost)", "two_frame_stability": "Stabilita dvou snímků", "static_similarity": "Statická podobnost", "static_stability_threshold": "P<PERSON><PERSON><PERSON> stability", "static_replacement_threshold": "<PERSON><PERSON><PERSON><PERSON> vý<PERSON>ě<PERSON>", "cycling_stability": "🔄 Cyklování stability", "min_cycles_first_line": "Min c<PERSON>lů 1. <PERSON><PERSON><PERSON>", "min_cycles_second_line": "<PERSON> 2. <PERSON><PERSON><PERSON>", "min_cycles_second_growth": "Min cyklů růst 2. řád<PERSON>", "single_text_cycles": "Cykly stability jednoho textu", "different_texts_count": "Počet různých textů pro přepnutí", "dynamic_growth": "📈 Dynamický růst", "dynamic_growth_similarity": "Podobnost dynamického růstu", "dynamic_min_word_length": "<PERSON> d<PERSON><PERSON>a p<PERSON>a", "dynamic_max_words": "Max slov př<PERSON><PERSON><PERSON><PERSON> na<PERSON>", "full_auto_thresholds": "🤖 Plně automatic<PERSON> prahy", "fa_initial_static": "FA: počáteční statický práh", "fa_initial_dynamic": "FA: počáteční dynamický práh", "fa_stable_static": "FA: stabilní statický práh", "fa_stable_dynamic": "FA: stabilní dynamický práh", "fa_min_samples": "FA: min stabilních vzorků", "fa_replace_threshold": "FA: pr<PERSON><PERSON> vý<PERSON>", "ocr_analysis": "🔍 OCR a analýza", "ocr_threshold_param": "OCR práh", "dynamic_ocr_threshold": "Dynamický OCR práh", "min_text_length": "<PERSON> délka textu pro analýzu", "tests_tab": "Testy", "tolerance_group": "Tolerancie pozície titulků", "ocr_interval_group": "OCR interval", "filtering_group": "Filtrování podle levé strany", "actions_group": "Ak<PERSON>", "dialog_success": "Úspech", "dialog_error": "Chyba", "dialog_warning": "Upozornění", "dialog_confirmation": "Potvrzení", "dialog_critical_error": "Kritická chyba", "device_mps_gpu": "🚀 MPS GPU (Apple Silicon)", "device_cuda_gpu": "🚀 CUDA GPU", "device_cpu_only": "💻 CPU", "status_initialized": "✅ Inicializovaný", "status_not_initialized": "❌ <PERSON>ein<PERSON><PERSON><PERSON><PERSON>ý", "memory_usage_model": "~2-3 GB (600M model)", "memory_usage_none": "0 GB", "test_connection": "🧪 Test připojení", "test_connection_tooltip": "Otestuje API připojení s aktuálními credentials", "cache_settings_group": "🗄️ <PERSON><PERSON> na<PERSON>", "enable_cache": "Povolit cache", "max_cache_size": "Max cache size:", "api_settings_group": "⚙️ API nastavení", "timeout_label": "Timeout:", "timeout_suffix": " sekund", "timeout_tooltip": "Timeout pro API requesty", "rate_limit_label": "Rate limit:", "rate_limit_suffix": " sekund", "rate_limit_tooltip": "Minimální interval mezi API requesty", "batch_size_label": "Batch size:", "batch_size_tooltip": "Počet textů odeslaných v jednom API requestu", "beam_search_group": "🔹 Beam Search", "penalties_group": "⚖️ Penalizace", "no_repeat_ngram": "No repeat ngram size:", "no_repeat_ngram_tooltip": "Blokuje opakování n-gramů (např. 'the the the')\n0 = vyp<PERSON><PERSON>, 2-4 = typick<PERSON> hodnoty", "repetition_penalty": "Repetition penalty:", "repetition_penalty_tooltip": "Penalizuje opakovaná slova\n>1.0 = <PERSON><PERSON><PERSON>, 1.1-1.3 = doporučené", "length_penalty": "Length penalty:", "length_limits_group": "🔹 D<PERSON>lk<PERSON>é limity", "max_length_label": "Max length:", "optimizations_group": "🚀 Optimalizace", "fast_mode": "Enable fast mode:", "fast_mode_tooltip": "Zapne rychlé optimalizace", "memory_optimization": "Memory optimization:", "memory_optimization_tooltip": "Zapne optimal<PERSON>", "info_group": "ℹ️ Informace", "reset_to_default": "🔄 Reset na default", "ok_button": "✅ OK", "cancel_button": "❌ <PERSON>ruš<PERSON>", "generation_group": "🎯 Generování textu", "max_length_tokens": "<PERSON>:", "tokens_suffix": " tokenů", "min_length_tokens": "<PERSON><PERSON>:", "num_beams": "Počet beams:", "temperature": "Temperature:", "top_k": "Top-k:", "top_p": "Top-p:", "use_cache": "Použít cache", "early_stopping": "Early stopping", "sampling": "Sampling", "y_threshold_label": "Práh Y titulků (%):", "reference_logging": "Referenční logování pro analýzu detekce", "current_logging_to": "Aktuální logování do:", "standard_logging": "app.log (standardní logování)", "static_logging": "static.log (pro statick<PERSON> titulky)", "dynamic_logging": "dynamic.log (pro <PERSON><PERSON><PERSON> titulky)", "clear_test_logs": "Smazat všechny test logy", "open_logs_folder": "Otevřít složku s logy", "source_language_label": "Zdrojový jazyk:", "target_language_label": "Cílový jazyk:", "mode_off": "Vypnuté", "mode_manual": "Manuální", "mode_auto": "Automatický", "mode_smart": "Inteligentní", "quality_profile_label": "Kvalitní profil:", "profile_fast": "⚡ <PERSON><PERSON><PERSON><PERSON>", "profile_balanced": "⚖️ Vyvážený", "profile_quality": "🏆 Kvalitní", "profile_custom": "⚙️ Vlastní", "active_status": " (aktivní)", "not_exists": "neexist<PERSON><PERSON>", "error_loading_stats": "Chyba při načítání statistik: {error}", "reading_uppercase_enabled": "Čtení velk<PERSON>ch písmen je POVOLENO", "reading_uppercase_disabled": "Čtení velkých písmen je ZAKÁZÁNO", "speech_rate_set": "Rychlost řeči nastavená na {rate}", "speech_volume_set": "Hlasitost řeči nastavená na {volume}", "ocr_threshold_set": "Prahování OCR nastavené na {threshold}", "thread_safe_status_update": "Thread‑safe aktualizace statusu.", "start_stop_action": "Spustit/Zastavit", "test_text_placeholder": "Zadejte text pro test:", "swap_languages_button": "🔄 Vymě<PERSON>t jaz<PERSON>y", "confirmation_dialog": "Potvrzení", "success_dialog": "Úspěch", "error_dialog": "Chyba", "warning_dialog": "Upozornění", "critical_error_dialog": "Kritická chyba", "test_connection_button": "🧪 Test připojení", "max_retries_param": "Max retries:", "no_repeat_ngram_label": "No repeat ngram size:", "repetition_penalty_label": "Repetition penalty:", "length_penalty_label": "Length penalty:", "max_length_not_supported_label": "Max length (nepodporované):", "fast_mode_label": "Enable fast mode:", "memory_opt_label": "Memory optimization:", "reset_to_defaults": "🔄 Reset na default", "ocr_interval_label": "OCR interval (s):", "switched_to_static": "přepnuto na statický režim", "all_modes_enabled": "všechny režimy povoleny", "reset_left_detection": "Reset levé de<PERSON>ce", "report_anomaly": "Nahlásit anomálii", "dialog_ok": "OK", "dialog_cancel": "Zrušit", "seconds_suffix": " sekund", "timeout_param": "Timeout:", "rate_limit_param": "Rate limit:", "batch_size_param": "Batch size:", "memory_opt_tooltip": "Zapne optimal<PERSON>", "max_length_param": "<PERSON>:", "min_length_param": "<PERSON><PERSON>:", "num_beams_param": "Počet beams:", "use_cache_param": "Použít cache", "ocr_value_initial": "0%", "rate_value_initial": "0", "volume_value_initial": "0%", "ocr_interval_initial": "0.00s", "status_credentials_configured": "Status: ✅ Credentials nastavené", "status_not_configured": "Status: <PERSON><PERSON><PERSON>"}