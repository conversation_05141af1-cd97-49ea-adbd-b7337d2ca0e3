#!/usr/bin/env python3
"""
Jednoduchý test GUI pre overenie, že PyQt6 funguje
"""

import sys
from PyQt6 import QtWidgets, QtCore

def main():
    print("🖥️ Spúšťam jednoduchý GUI test...")
    
    # Vytvor aplikáciu
    app = QtWidgets.QApplication(sys.argv)
    
    # Vytvor jednoduché okno
    window = QtWidgets.QWidget()
    window.setWindowTitle("Subtitle Reader - GUI Test")
    window.setGeometry(100, 100, 400, 300)
    
    # Pridaj layout a widgety
    layout = QtWidgets.QVBoxLayout()
    
    label = QtWidgets.QLabel("🎉 GUI Test úspešný!")
    label.setStyleSheet("font-size: 18px; padding: 20px;")
    layout.addWidget(label)
    
    button = QtWidgets.QPushButton("Zavrieť")
    button.clicked.connect(window.close)
    layout.addWidget(button)
    
    window.setLayout(layout)
    
    # Z<PERSON>raz okno
    window.show()
    window.raise_()  # Priveď okno do popredia
    window.activateWindow()  # Aktivuj okno
    
    print("✅ GUI okno by sa malo zobraziť")
    print("📍 Ak nevidíte okno, skontrolujte taskbar alebo skúste Alt+Tab")
    
    # Spusti event loop
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
