import logging
import statistics
from collections import defaultdict
import pytesseract
from PIL import Image
import common_config as config
import csv_logger

def start_dynamic_subtitle_left_detection():
    """Spustí detekciu ľavej strany dynamických titulkov."""
    config.dynamic_subtitle_detection_active = True
    config.dynamic_subtitle_detection_samples = []
    config.dynamic_subtitle_left_x = None
    logging.info("[DYNAMIC_SUBTITLE] Spustená detekcia ľavej strany dynamických titulkov")
    csv_logger.log_custom_event("DYNAMIC_SUBTITLE_DETECTION_START", "Detekcia ľavej strany dynamických titulkov spustená")

def detect_dynamic_subtitle_left_from_image(image_path):
    """
    Detekuje ľavú X-súradnicu dynamických titulkov z obrázka.

    Args:
        image_path: Cesta k obrázku

    Returns:
        int: X-súradnica ľavej strany titulkov alebo None
    """
    if not config.dynamic_subtitle_detection_active:
        return None
    
    try:
        # OCR s pozičnými údajmi
        image = Image.open(image_path)
        ocr_data = pytesseract.image_to_data(image, lang='ces', output_type=pytesseract.Output.DICT)
        
        # Filtrovanie textových blokov s dostatočnou dôverou
        text_blocks = []
        for i in range(len(ocr_data['text'])):
            text = ocr_data['text'][i].strip()
            conf = int(ocr_data['conf'][i])
            
            if text and conf > 30:  # Minimálna dôvera 30%
                x = ocr_data['left'][i]
                y = ocr_data['top'][i]
                w = ocr_data['width'][i]
                h = ocr_data['height'][i]
                
                text_blocks.append({
                    'text': text,
                    'x': x,
                    'y': y,
                    'width': w,
                    'height': h,
                    'confidence': conf,
                    'right_x': x + w
                })
        
        if not text_blocks:
            return None
        
        # Analýza pozícií - hľadáme dynamické titulky (zvyčajne v dolnej časti obrazovky)
        image_height = image.height

        # Titulky sú zvyčajne v dolnej tretine obrazovky
        subtitle_y_threshold = image_height * config.SUBTITLE_Y_THRESHOLD

        subtitle_candidates = []
        for block in text_blocks:
            if block['y'] > subtitle_y_threshold and len(block['text']) > 3:
                subtitle_candidates.append(block)

        if not subtitle_candidates:
            logging.debug("[DYNAMIC_SUBTITLE] Žiadne kandidáty na titulky v dolnej časti obrazovky")
            return None

        # Analýza ľavých X-pozícií titulkových kandidátov
        left_positions = [block['x'] for block in subtitle_candidates]

        # Najčastejšie ľavé X-pozície (dynamické titulky sú zvyčajne zarovnané vľavo)
        left_x_groups = defaultdict(list)
        for x in left_positions:
            # Grupovanie s toleranciou ±15 pixelov
            group_key = round(x / 15) * 15
            left_x_groups[group_key].append(x)

        if not left_x_groups:
            return None

        # Najväčšia skupina = pravdepodobne ľavá strana dynamických titulkov
        main_group_key = max(left_x_groups.keys(), key=lambda k: len(left_x_groups[k]))
        detected_left_x = int(statistics.median(left_x_groups[main_group_key]))

        logging.debug(f"[DYNAMIC_SUBTITLE] Detekovaná ľavá strana: X={detected_left_x}, kandidátov: {len(subtitle_candidates)}")
        return detected_left_x
        
    except Exception as e:
        logging.error(f"[SUBTITLE_AREA] Chyba pri detekcii oblasti: {e}")
        return None

def process_dynamic_subtitle_sample(image_path):
    """
    Spracuje vzorku pre detekciu ľavej strany dynamických titulkov.

    Args:
        image_path: Cesta k obrázku
    """
    if not config.dynamic_subtitle_detection_active:
        return

    detected_left_x = detect_dynamic_subtitle_left_from_image(image_path)
    if detected_left_x is not None:
        config.dynamic_subtitle_detection_samples.append(detected_left_x)
        logging.debug(f"[DYNAMIC_SUBTITLE] Pridaná vzorka {len(config.dynamic_subtitle_detection_samples)}: X={detected_left_x}")

        # Po 5 vzorkách vyhodnotíme ľavú stranu
        if len(config.dynamic_subtitle_detection_samples) >= 5:
            finalize_dynamic_subtitle_detection()

def finalize_dynamic_subtitle_detection():
    """Dokončí detekciu ľavej strany dynamických titulkov na základe nazbieraných vzoriek."""
    if not config.dynamic_subtitle_detection_samples:
        logging.warning("[DYNAMIC_SUBTITLE] Žiadne vzorky pre detekciu ľavej strany")
        return

    # Analýza vzoriek - mediánová hodnota pre stabilitu
    final_left_x = int(statistics.median(config.dynamic_subtitle_detection_samples))

    # Nastavenie globálnej hodnoty
    config.dynamic_subtitle_left_x = final_left_x
    config.dynamic_subtitle_detection_active = False

    # Štatistiky
    left_variance = statistics.variance(config.dynamic_subtitle_detection_samples) if len(config.dynamic_subtitle_detection_samples) > 1 else 0

    logging.info(f"[DYNAMIC_SUBTITLE] Detekcia dokončená:")
    logging.info(f"[DYNAMIC_SUBTITLE] Ľavá strana dynamických titulkov: X={final_left_x}")
    logging.info(f"[DYNAMIC_SUBTITLE] Variancia pozície: {left_variance:.1f} (vzorky: {config.dynamic_subtitle_detection_samples})")

    csv_logger.log_custom_event("DYNAMIC_SUBTITLE_DETECTION_COMPLETE",
                               f"Ľavá strana: X={final_left_x}, vzorky={len(config.dynamic_subtitle_detection_samples)}, variancia={left_variance:.1f}")

def is_text_left_of_dynamic_subtitles(text_blocks_with_positions):
    """
    Filtruje textové bloky podľa ľavej strany dynamických titulkov.

    LOGIKA:
    1. Zoskupí slová do riadkov (podľa Y-pozície)
    2. Pre každý riadok nájde slovo NAJBLIŽŠIE k detekovanej ľavej strane
       (nie najľavejšie - rieši problém s nesprávnymi pozíciami posledných slov)
    3. Ak najbližšie slovo je v tolerancii ±30px od detekovanej ľavej strany
       → CELÝ riadok je titulkový (všetky slová v riadku sa berú)
    4. Ak nie → CELÝ riadok sa ignoruje

    Args:
        text_blocks_with_positions: List textových blokov s pozíciami

    Returns:
        list: Filtrované textové bloky z titulkových riadkov
    """
    if not text_blocks_with_positions:
        return []

    # Ak nemáme detekovanú ľavú stranu, vrátime všetko
    if config.dynamic_subtitle_left_x is None:
        logging.debug("[DYNAMIC_SUBTITLE] Ľavá strana nie je detekovaná, vraciam všetky bloky")
        return text_blocks_with_positions

    # Tolerancia pre začiatok titulkového riadku (prvé slovo)
    TOLERANCE = config.SUBTITLE_TOLERANCE  # Nastaviteľné cez GUI

    # Krok 1: Zoskupenie slov do riadkov podľa Y-pozície
    lines = {}  # {y_group: [blocks]}
    Y_TOLERANCE = config.Y_TOLERANCE  # Nastaviteľné cez GUI

    for block in text_blocks_with_positions:
        block_y = block.get('y', 0)

        # Nájdeme existujúci riadok alebo vytvoríme nový
        found_line = False
        for line_y in lines.keys():
            if abs(block_y - line_y) <= Y_TOLERANCE:
                lines[line_y].append(block)
                found_line = True
                break

        if not found_line:
            lines[block_y] = [block]

    # Krok 2: Pre každý riadok kontrolujeme PRVÉ SLOVO (najľavejšie X)
    filtered_blocks = []

    for line_y, line_blocks in lines.items():
        if not line_blocks:
            continue

        # Nájdeme slovo NAJBLIŽŠIE k detekovanej ľavej strane (nie najľavejšie!)
        # Toto rieši problém keď posledné slovo má nesprávnu X pozíciu
        closest_word = min(line_blocks, key=lambda b: abs(b.get('x', 0) - config.dynamic_subtitle_left_x))
        closest_word_x = closest_word.get('x', 0)

        # Kontrola, či NAJBLIŽŠIE SLOVO je v tolerancii ±30px
        distance_from_left = closest_word_x - config.dynamic_subtitle_left_x
        is_subtitle_line = abs(distance_from_left) <= TOLERANCE

        if is_subtitle_line:
            # NAJBLIŽŠIE SLOVO je v tolerancii → CELÝ RIADOK je titulkový
            filtered_blocks.extend(line_blocks)
            line_texts = [b.get('text', '') for b in line_blocks]
            leftmost_x = min(b.get('x', 0) for b in line_blocks)
            rightmost_x = max(b.get('x', 0) for b in line_blocks)
            logging.debug(f"[DYNAMIC_SUBTITLE] Riadok Y={line_y} je TITULKOVÝ: najbližšie slovo X={closest_word_x} (offset={distance_from_left:+d}px), rozsah X={leftmost_x}-{rightmost_x}, všetky slová: {line_texts}")
        else:
            # NAJBLIŽŠIE SLOVO je mimo toleranciu → CELÝ RIADOK sa ignoruje
            line_texts = [b.get('text', '') for b in line_blocks]
            logging.debug(f"[DYNAMIC_SUBTITLE] Riadok Y={line_y} je MIMO titulkov: najbližšie slovo X={closest_word_x} (offset={distance_from_left:+d}px), ignorované slová: {line_texts}")

    logging.debug(f"[DYNAMIC_SUBTITLE] Filtrovanie dokončené: {len(filtered_blocks)}/{len(text_blocks_with_positions)} blokov zostalo")
    return filtered_blocks

def get_dynamic_subtitle_info():
    """Vráti informácie o detekcii ľavej strany dynamických titulkov."""
    return {
        'left_x': config.dynamic_subtitle_left_x,
        'detection_active': config.dynamic_subtitle_detection_active,
        'samples_count': len(config.dynamic_subtitle_detection_samples)
    }
