#!/usr/bin/env python3
"""
Setup script pre vytvorenie macOS aplikácie pomocou py2app
Subtitle Reader - OCR a TTS aplikácia pre čítanie titulkov
"""

from setuptools import setup
import sys
import os

# Pridaj aktuálny adresár do Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

APP = ['main_qt.py']
DATA_FILES = [
    # Lokalizačné súbory
    ('locales', ['locales/sk.json', 'locales/en.json', 'locales/cs.json',
                 'locales/de.json', 'locales/fr.json', 'locales/pl.json']),

    # Konfiguračné súbory
    ('', ['license.json', 'app_settings.json']),

    # Dokument<PERSON>cia
    ('docs', ['README.md', 'SPUSTENIE_APLIKACIE.md', 'AUTOMATOR_NAVOD.md']),

    # Obr<PERSON>zky/asset-y potrebné v GUI (root Resources)
    ('', ['keyboard_MAC_200.jpg']),
]

# Všetky Python moduly aplikácie
PACKAGES = [
    'automatic_mode',
    'dynamic_mode',
    'full_automatic_mode',
    'static_mode',
    'requests',
    'urllib3',
    'certifi',
    'charset_normalizer',
    'idna',
    'pynput',
    'pytesseract',
    'pyttsx3',
    'PyQt6',
    'Levenshtein',
    'rapidfuzz',
    'mss',
    'PIL',
    'packaging',
    'six',
]

# Explicitne zahrň všetky lokálne moduly
INCLUDES = [
    'app_logic',
    'common_config',
    'common_utils',
    'csv_logger',
    'demo_timer',
    'hardware_fingerprint',
    'i18n_manager',
    'language_detector',
    'license_manager',
    'mac_hotkey_manager',
    'mac_tts_manager',
    'macos_dark_theme',
    'ocr_core',
    'ocr_processing',
    'ocr_text_corrector',
    'platform_loader',
    'platform_macos',
    'platform_utils',
    'qt_gui',
    'settings_manager',
    'subtitle_area_detector',
    'youtube_utils',
    'testing',

    # Moduly v podadresároch
    'automatic_mode.automatic_logic',
    'dynamic_mode.dynamic_logic',
    'full_automatic_mode.full_automatic_logic',
    'full_automatic_mode.full_auto_dynamic_detector',
    'full_automatic_mode.full_auto_stability_detector',
    'static_mode.static_logic',

    # Externé závislosti
    'requests',
    'urllib3',
    'certifi',
    'charset_normalizer',
    'idna',

    # Hotkey/TTS dependencies that use dynamic imports on macOS
    'pynput.keyboard',
    'pynput.keyboard._darwin',
    'pynput._util',
    'pynput._util.darwin',
    'pyttsx3.drivers',
    'pyttsx3.drivers.nsss',
    'Quartz',
    'AppKit',
]

# Externé závislosti
EXCLUDES = [
    # Vylúč Windows-specific moduly
    'win_hotkey_manager',
    'win_tts_manager',
    'windows_hotkeys',
    'onecore_tts_provider',
    'platform_windows',
    'platform_linux',

    # Vylúč test súbory
    'test_*',
    'simple_test',

    # Vylúč build súbory
    'create_*',
    'fix_*',

    # Vylúč problematické moduly
    'matplotlib',
    'numpy',
    'IPython',
    'jupyter',
    'zmq',
    'tornado',
    'debugpy',
    'jedi',
    'parso',
    'pygments',
]

OPTIONS = {
    'argv_emulation': False,
    'iconfile': None,  # Môžeme pridať ikonu neskôr
    'plist': {
        'CFBundleName': 'Subtitle Reader',
        'CFBundleDisplayName': 'Subtitle Reader',
        'CFBundleIdentifier': 'com.subtitlereader.app',
        'CFBundleVersion': '1.0.0',
        'CFBundleShortVersionString': '1.0.0',
        'CFBundleInfoDictionaryVersion': '6.0',
        'NSHumanReadableCopyright': 'Copyright © 2025 Subtitle Reader',
        'NSHighResolutionCapable': True,
        'LSMinimumSystemVersion': '10.15.0',
        'NSRequiresAquaSystemAppearance': False,
        
        # Povolenia pre macOS
        'NSCameraUsageDescription': 'Subtitle Reader potrebuje prístup ku kamere pre OCR funkcie.',
        'NSMicrophoneUsageDescription': 'Subtitle Reader potrebuje prístup k mikrofónu pre TTS funkcie.',
        'NSScreenCaptureDescription': 'Subtitle Reader potrebuje prístup k obrazovke pre čítanie titulkov.',
        'NSAccessibilityUsageDescription': 'Subtitle Reader potrebuje prístup k accessibility pre hotkeys.',
        
        # Nastavenia aplikácie
        'LSUIElement': False,  # Zobrazí sa v Docku
        'NSAppleScriptEnabled': True,
        'NSPrincipalClass': 'NSApplication',
    },
    
    # Zahrň všetky potrebné moduly
    'includes': INCLUDES,
    'excludes': EXCLUDES,
    'packages': PACKAGES,
    
    # Optimalizácie
    'optimize': 2,
    'compressed': True,
    'strip': True,
    
    # Architektúra - universal2
    'arch': 'universal2',
    
    # Dodatočné nastavenia
    'site_packages': True,
    'alias': False,
    'semi_standalone': False,
    'no_chdir': False,
}

setup(
    name='Subtitle Reader',
    app=APP,
    data_files=DATA_FILES,
    options={'py2app': OPTIONS},
    setup_requires=['py2app'],
    # Odstránené install_requires - py2app má s tým problémy
    # Závislosti sú už nainštalované v systéme
    python_requires='>=3.8',
    author='Subtitle Reader Team',
    description='OCR a TTS aplikácia pre čítanie titulkov na macOS',
    long_description='Subtitle Reader je pokročilá aplikácia pre automatické čítanie titulkov pomocou OCR a TTS technológií.',
    url='https://subtitlereader.com',
    classifiers=[
        'Development Status :: 4 - Beta',
        'Intended Audience :: End Users/Desktop',
        'License :: OSI Approved :: MIT License',
        'Operating System :: MacOS :: MacOS X',
        'Programming Language :: Python :: 3',
        'Programming Language :: Python :: 3.8',
        'Programming Language :: Python :: 3.9',
        'Programming Language :: Python :: 3.10',
        'Programming Language :: Python :: 3.11',
        'Topic :: Multimedia :: Sound/Audio :: Speech',
        'Topic :: Multimedia :: Graphics :: Capture :: Screen Capture',
    ],
)
