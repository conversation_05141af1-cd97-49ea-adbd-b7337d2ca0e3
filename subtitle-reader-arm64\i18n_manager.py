"""
Internationalization (i18n) Manager
Spravuje jazyky aplikácie, OCR a TTS
"""

import json
import os
import logging
import common_config as config
from language_detector import get_language_detector

class Translator:
    """Správca prekladov aplikácie."""
    
    def __init__(self, lang="cs"):
        self.lang = lang
        self.translations = {}
        self.load_language(lang)

    def load_language(self, lang):
        """Načíta jazykový súbor."""
        try:
            path = f"locales/{lang}.json"
            if not os.path.exists(path):
                logging.warning(f"⚠️ Jazyk {lang} nenájdený, fallback na CS")
                path = "locales/cs.json"
                
            with open(path, "r", encoding="utf-8") as f:
                self.translations = json.load(f)
                
            self.lang = lang
            logging.info(f"✅ Jazyk aplikácie načítaný: {lang}")
            
        except Exception as e:
            logging.error(f"❌ Chyba pri načítaní jazyka {lang}: {e}")
            # Fallback na základné texty
            self.translations = {
                "start_reading": "Spustit čtení",
                "stop_reading": "Zastavit čtení"
            }

    def t(self, key):
        """Preloží kľúč. Ak neexistuje, vráti kľúč."""
        return self.translations.get(key, key)

    def get_available_languages(self):
        """Vráti zoznam dostupných jazykov."""
        languages = []
        locales_dir = "locales"
        
        if os.path.exists(locales_dir):
            for file in os.listdir(locales_dir):
                if file.endswith(".json"):
                    lang_code = file[:-5]  # odstráni .json
                    languages.append(lang_code)
                    
        return sorted(languages)


class LanguageManager:
    """Správca jazykov pre OCR a TTS s automatickou detekciou."""

    def __init__(self):
        logging.info("🔍 Inicializujem LanguageManager...")
        self.current_app_language = getattr(config, "APP_LANGUAGE", "cs")
        self.current_reading_language = getattr(config, "READING_LANGUAGE", "cs")

        # Základné jazyky (bez automatickej detekcie zatiaľ)
        self.available_languages = {
            "app": {"cs", "sk", "en", "de", "fr", "pl"},
            "ocr": {"cs", "sk", "en", "de", "fr", "pl"},
            "tts": {"cs", "sk", "en", "de", "fr", "pl"},
            "reading": {"cs", "sk", "en", "de", "fr", "pl"}
        }

        logging.info("🔍 LanguageManager inicializovaný s predvolenými jazykmi")

        # Automatická detekcia jazykov
        try:
            logging.info("🔍 Spúšťam automatickú detekciu jazykov...")
            self.detector = get_language_detector()
            detected = self.detector.detect_all_languages()
            if detected:
                # Aktualizuj dostupné jazyky s detekovanými
                for key, langs in detected.items():
                    if langs:  # Len ak sú detekované nejaké jazyky
                        self.available_languages[key] = langs
                logging.info("✅ Automatická detekcia úspešná")
                logging.info(f"📱 Finálne dostupné jazyky: {self.available_languages}")

                # 🔧 OPRAVA: Automaticky nastav najlepší hlas pre aktuálny jazyk čítania
                logging.info(f"🔧 Nastavujem najlepší hlas pre aktuálny jazyk čítania: {self.current_reading_language}")
                self.set_reading_language(self.current_reading_language)
            else:
                logging.warning("⚠️ Automatická detekcia nevrátila žiadne jazyky")
        except Exception as e:
            logging.warning(f"⚠️ Automatická detekcia zlyhala: {e}")
            import traceback
            logging.debug(traceback.format_exc())
            logging.info("🔧 Používam predvolené jazyky")
    
    def get_available_app_languages(self):
        """Vráti zoznam dostupných jazykov aplikácie."""
        return sorted(list(self.available_languages.get("app", {"cs", "en"})))

    def get_available_reading_languages(self):
        """Vráti zoznam dostupných jazykov pre čítanie (OCR + TTS)."""
        return sorted(list(self.available_languages.get("reading", {"cs", "en"})))

    def get_language_info(self, lang_code):
        """Vráti informácie o jazyku."""
        return self.detector.get_language_info(lang_code)

    def get_language_display_name(self, lang_code):
        """Vráti zobrazovací názov jazyka s vlajkou."""
        return self.detector.get_display_name(lang_code)

    def is_language_available_for_reading(self, lang_code):
        """Kontroluje, či je jazyk dostupný pre čítanie."""
        return self.detector.is_language_available_for_reading(lang_code)
    
    def set_app_language(self, lang_code):
        """Nastaví jazyk aplikácie."""
        if lang_code in self.available_languages.get("app", set()):
            self.current_app_language = lang_code
            config.APP_LANGUAGE = lang_code
            logging.info(f"🌍 Jazyk aplikácie zmenený na: {lang_code}")
            return True
        return False
    
    def set_reading_language(self, lang_code):
        """Nastaví jazyk čítania (OCR + TTS) s automatickým nastavením najlepšieho hlasu."""
        if self.is_language_available_for_reading(lang_code):
            self.current_reading_language = lang_code
            config.READING_LANGUAGE = lang_code

            # Nastaví OCR jazyk
            info = self.get_language_info(lang_code)
            config.OCR_LANGUAGE = info["tesseract"]
            config.TTS_LANGUAGE = info["apple_tts"]

            # Nájde najlepší hlas pre daný jazyk
            best_voice = self.detector.get_best_voice_for_language(lang_code)
            if best_voice:
                config.TTS_VOICE = best_voice
                try:
                    config.READING_TTS_VOICE = best_voice
                    config.SELECTED_READING_VOICE = best_voice
                except Exception:
                    pass
                logging.info(f"🎤 Najlepší hlas pre {lang_code}: {best_voice}")

            # TTS hlášky sa neaktualizujú pri zmene jazyka čítania
            # TTS hlášky sú v jazyku aplikácie, nie čítania

            logging.info(f"📖 Jazyk čítania zmenený na: {lang_code}")
            logging.info(f"🔍 OCR jazyk: {info['tesseract']}")
            logging.info(f"🔊 TTS jazyk: {info['apple_tts']}")
            return True
        else:
            logging.warning(f"⚠️ Jazyk {lang_code} nie je dostupný pre čítanie (chýba OCR alebo TTS)")
            return False
    
    def get_tesseract_language(self, lang_code=None):
        """Vráti Tesseract jazyk kód."""
        if lang_code is None:
            lang_code = self.current_reading_language
        return self.get_language_info(lang_code)["tesseract"]
    
    def get_tts_language(self, lang_code=None):
        """Vráti Apple TTS jazyk kód."""
        if lang_code is None:
            lang_code = self.current_reading_language
        return self.get_language_info(lang_code)["apple_tts"]

    def get_translator_supported_languages(self):
        """Vráti jazyky podporované prekladačom."""
        try:
            from translation_manager import get_supported_languages
            return set(get_supported_languages().keys())
        except ImportError:
            logging.warning("⚠️ Translation manager nie je dostupný")
            return {"en", "cs", "sk", "de", "fr"}

    def get_available_source_languages(self):
        """Vráti jazyky dostupné pre 'Preklad z' (OCR ∩ prekladač source)."""
        ocr_langs = self.available_languages.get("ocr", set())
        translator_langs = self.get_translator_supported_languages()
        return sorted(list(ocr_langs.intersection(translator_langs)))

    def get_available_target_languages(self):
        """Vráti jazyky dostupné pre 'Preklad do' (TTS ∩ prekladač target)."""
        tts_langs = self.available_languages.get("tts", set())
        translator_langs = self.get_translator_supported_languages()
        return sorted(list(tts_langs.intersection(translator_langs)))

    def set_translation_source_language(self, lang_code):
        """Nastaví source jazyk prekladača + OCR jazyk."""
        if lang_code in self.get_available_source_languages():
            # Nastaví OCR jazyk
            info = self.get_language_info(lang_code)
            config.OCR_LANGUAGE = info["tesseract"]
            config.TRANSLATION_SOURCE_LANGUAGE = lang_code

            # OPRAVA: Nastaví aj source jazyk v translation manageri
            try:
                import translation_manager
                translation_manager.set_source_language(lang_code)
                logging.info(f"🔄 Translation manager source jazyk nastavený na: {lang_code}")
            except Exception as e:
                logging.error(f"❌ Chyba pri nastavovaní translation manager source jazyka: {e}")

            # 🔧 NOVÉ: Aktualizuj aj multi-translator jazyky
            try:
                from multi_translator import update_translator_languages
                current_target = getattr(config, "TRANSLATION_TARGET_LANGUAGE", "cs")
                update_translator_languages(lang_code, current_target)
                logging.info(f"🔄 Multi-translator source jazyk aktualizovaný na: {lang_code}")
            except Exception as e:
                logging.debug(f"Multi-translator source jazyk update zlyhal: {e}")

            logging.info(f"🔄 Translation source jazyk: {lang_code}")
            logging.info(f"🔍 OCR jazyk nastavený na: {info['tesseract']}")
            return True
        else:
            logging.warning(f"⚠️ Jazyk {lang_code} nie je dostupný pre source preklad")
            return False

    def set_translation_target_language(self, lang_code):
        """Nastaví target jazyk prekladača + TTS jazyk."""
        if lang_code in self.get_available_target_languages():
            # Nastaví TTS jazyk
            info = self.get_language_info(lang_code)
            config.TTS_LANGUAGE = info["apple_tts"]
            config.TRANSLATION_TARGET_LANGUAGE = lang_code

            # Nájde najlepší hlas pre daný jazyk
            best_voice = self.detector.get_best_voice_for_language(lang_code)
            if best_voice:
                config.TTS_VOICE = best_voice
                try:
                    config.TRANSLATION_TTS_VOICE = best_voice
                except Exception:
                    pass
                logging.info(f"🎤 Najlepší hlas pre {lang_code}: {best_voice}")
            else:
                logging.warning(f"⚠️ Žiadny hlas pre {lang_code}, používam predvolený")

            # Aktualizuje TTS hlášky na nový jazyk
            global tts_message_manager
            if 'tts_message_manager' in globals():
                tts_message_manager.update_language(lang_code)

            # OPRAVA: Nastaví aj target jazyk v translation manageri
            try:
                import translation_manager
                translation_manager.set_target_language(lang_code)
                logging.info(f"🔄 Translation manager target jazyk nastavený na: {lang_code}")
            except Exception as e:
                logging.error(f"❌ Chyba pri nastavovaní translation manager target jazyka: {e}")

            # 🔧 NOVÉ: Aktualizuj aj multi-translator jazyky
            try:
                from multi_translator import update_translator_languages
                current_source = getattr(config, "TRANSLATION_SOURCE_LANGUAGE", "en")
                update_translator_languages(current_source, lang_code)
                logging.info(f"🔄 Multi-translator target jazyk aktualizovaný na: {lang_code}")
            except Exception as e:
                logging.debug(f"Multi-translator target jazyk update zlyhal: {e}")

            logging.info(f"🔄 Translation target jazyk: {lang_code}")
            logging.info(f"🔊 TTS jazyk nastavený na: {info['apple_tts']}")
            return True
        else:
            logging.warning(f"⚠️ Jazyk {lang_code} nie je dostupný pre target preklad")
            return False

    def restore_reading_language_settings(self):
        """Obnoví OCR + TTS nastavenia z 'Jazyk čítania titulků'."""
        self.set_reading_language(self.current_reading_language)
        logging.info(f"🔄 Obnovené nastavenia z jazyka čítania: {self.current_reading_language}")

    def get_voices_for_language(self, lang_code: str):
        """Vráti zoznam dostupných hlasov pre daný jazyk."""
        return self.detector.get_voices_for_language(lang_code)


# Globálne inštancie (lazy inicializácia)
print("🔍 Vytváram globálny Translator...")
translator = Translator()
print("🔍 Translator vytvorený")

# LanguageManager sa vytvorí až pri prvom použití
_language_manager = None

def _create_language_manager():
    """Vytvorí LanguageManager s plným logovaním."""
    global _language_manager
    if _language_manager is None:
        print("🔍 Vytváram LanguageManager s detekciou jazykov...")
        _language_manager = LanguageManager()
        print("✅ LanguageManager úspešne vytvorený")
    return _language_manager

print("🔍 Globálne inštancie pripravené (lazy loading)")

def get_translator():
    """Vráti globálnu inštanciu prekladača."""
    return translator

def get_language_manager():
    """Vráti globálnu inštanciu správcu jazykov (lazy loading)."""
    return _create_language_manager()

def set_app_language(lang_code):
    """Zmení jazyk aplikácie."""
    global translator
    translator.load_language(lang_code)
    get_language_manager().set_app_language(lang_code)

def set_reading_language(lang_code):
    """Zmení jazyk čítania."""
    get_language_manager().set_reading_language(lang_code)


class TTSMessageManager:
    """Správca TTS hlášok v jazyku čítania."""

    def __init__(self):
        # TTS hlášky by mali byť v jazyku aplikácie, nie čítania
        self.current_language = getattr(config, "APP_LANGUAGE", "cs")
        self.tts_translator = None
        self._load_tts_translator()

    def _load_tts_translator(self):
        """Načíta translator pre TTS hlášky v jazyku aplikácie."""
        try:
            self.tts_translator = Translator(self.current_language)
            logging.info(f"🔊 TTS hlášky načítané pre jazyk: {self.current_language}")
        except Exception as e:
            logging.error(f"❌ Chyba pri načítaní TTS hlášok: {e}")
            self.tts_translator = Translator("cs")  # Fallback

    def update_language(self, lang_code):
        """Aktualizuje jazyk TTS hlášok."""
        if lang_code != self.current_language:
            self.current_language = lang_code
            self._load_tts_translator()

    def get_message(self, key):
        """Vráti TTS hlášku v jazyku čítania."""
        if self.tts_translator:
            return self.tts_translator.t(key)
        return key  # Fallback na kľúč


# Globálna inštancia TTS message managera
tts_message_manager = TTSMessageManager()

def get_tts_message_manager():
    """Vráti globálnu inštanciu TTS message managera."""
    return tts_message_manager

def get_tts_message(key):
    """Skratka pre získanie TTS hlášky."""
    return tts_message_manager.get_message(key)

def update_tts_language(lang_code):
    """Aktualizuje jazyk TTS hlášok."""
    tts_message_manager.update_language(lang_code)
