#!/usr/bin/env python3
"""
Test script pre overenie funkcionality Subtitle Reader na Windows ARM64
"""

import sys
import os
import platform
import subprocess

def test_python_version():
    """Test Python verzie"""
    print("🐍 Python verzia:")
    print(f"   Verzia: {sys.version}")
    print(f"   Platforma: {platform.platform()}")
    print(f"   Architektúra: {platform.machine()}")
    print()

def test_dependencies():
    """Test závislostí"""
    print("📦 Testovanie závislostí:")
    
    dependencies = [
        'PyQt6',
        'pytesseract', 
        'Pillow',
        'pynput',
        'pyttsx3',
        'pywin32',
        'WMI',
        'requests',
        'numpy',
        'rapidfuzz'
    ]
    
    for dep in dependencies:
        try:
            __import__(dep.lower().replace('-', '_'))
            print(f"   ✅ {dep}")
        except ImportError:
            print(f"   ❌ {dep} - CHÝBA")
    print()

def test_tesseract():
    """Test Tesseract OCR"""
    print("🔍 Testovanie Tesseract OCR:")
    
    tesseract_paths = [
        "C:\\Program Files\\Tesseract-OCR\\tesseract.exe",
        "tesseract"
    ]
    
    for path in tesseract_paths:
        try:
            result = subprocess.run([path, "--version"], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                version_line = result.stdout.split('\n')[0]
                print(f"   ✅ Tesseract nájdený: {version_line}")
                return True
        except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError):
            continue
    
    print("   ❌ Tesseract OCR nie je nájdený")
    print("   💡 Nainštalujte Tesseract z: https://github.com/UB-Mannheim/tesseract/wiki")
    return False

def test_tts():
    """Test Text-to-Speech"""
    print("🔊 Testovanie TTS:")
    
    try:
        import pyttsx3
        engine = pyttsx3.init()
        voices = engine.getProperty('voices')
        print(f"   ✅ TTS engine inicializovaný")
        print(f"   📢 Dostupné hlasy: {len(voices) if voices else 0}")
        
        if voices:
            for i, voice in enumerate(voices[:3]):  # Zobraz prvé 3 hlasy
                print(f"      {i+1}. {voice.name} ({voice.languages})")
        
        engine.stop()
        return True
    except Exception as e:
        print(f"   ❌ TTS test zlyhal: {e}")
        return False

def test_gui():
    """Test GUI"""
    print("🖥️ Testovanie GUI:")
    
    try:
        from PyQt6 import QtWidgets, QtCore
        app = QtWidgets.QApplication([])
        
        # Test základného okna
        window = QtWidgets.QWidget()
        window.setWindowTitle("Test Window")
        window.resize(300, 200)
        
        print("   ✅ PyQt6 GUI test úspešný")
        
        app.quit()
        return True
    except Exception as e:
        print(f"   ❌ GUI test zlyhal: {e}")
        return False

def main():
    """Hlavná funkcia"""
    print("=" * 50)
    print("  Subtitle Reader - Windows ARM64 Test")
    print("=" * 50)
    print()
    
    test_python_version()
    test_dependencies()
    tesseract_ok = test_tesseract()
    tts_ok = test_tts()
    gui_ok = test_gui()
    
    print("=" * 50)
    print("📊 SÚHRN TESTOV:")
    print(f"   🔍 Tesseract OCR: {'✅ OK' if tesseract_ok else '❌ CHYBA'}")
    print(f"   🔊 TTS Engine: {'✅ OK' if tts_ok else '❌ CHYBA'}")
    print(f"   🖥️ GUI Framework: {'✅ OK' if gui_ok else '❌ CHYBA'}")
    
    if tesseract_ok and tts_ok and gui_ok:
        print()
        print("🎉 Všetky testy prešli úspešne!")
        print("💡 Aplikáciu môžete spustiť pomocou: run_app.bat")
    else:
        print()
        print("⚠️ Niektoré testy zlyhali. Skontrolujte chyby vyššie.")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
