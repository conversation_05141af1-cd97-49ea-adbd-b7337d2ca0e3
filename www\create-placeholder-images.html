<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vytvorenie placeholder ob<PERSON><PERSON><PERSON><PERSON></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        canvas {
            border: 2px solid #ddd;
            border-radius: 8px;
            margin: 10px 0;
        }
        .download-section {
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖼️ Vytvoření placeholder obrázků pro VOXO LOXO</h1>

        <div class="info">
            <strong>Instrukce:</strong> Tato stránka vytvoří placeholder obrázky hero.png, faq.png, overlay.png a download.png.
            Klikněte na tlačítka "Stáhnout" a uložte soubory do www/ složky.
        </div>
        
        <h2>Hero.png (1200x800px)</h2>
        <p>Pozadí pro úvodní sekci s gradientem a textem</p>
        <canvas id="heroCanvas" width="1200" height="800"></canvas>
        <div class="download-section">
            <button onclick="downloadHero()">📥 Stáhnout hero.png</button>
            <button onclick="regenerateHero()">🔄 Regenerovat</button>
        </div>
        
        <h2>FAQ.png (1200x800px)</h2>
        <p>Statické pozadí pro FAQ sekci s geometrickým vzorem</p>
        <canvas id="faqCanvas" width="1200" height="800"></canvas>
        <div class="download-section">
            <button onclick="downloadFaq()">📥 Stáhnout faq.png</button>
            <button onclick="regenerateFaq()">🔄 Regenerovat</button>
        </div>

        <h2>Overlay.png (1200x800px)</h2>
        <p>Pozadí pro podporované platformy s tech vzorem</p>
        <canvas id="overlayCanvas" width="1200" height="800"></canvas>
        <div class="download-section">
            <button onclick="downloadOverlay()">📥 Stáhnout overlay.png</button>
            <button onclick="regenerateOverlay()">🔄 Regenerovat</button>
        </div>

        <h2>Download.png (1200x800px)</h2>
        <p>Pozadí pro download sekci s cloud vzorem</p>
        <canvas id="downloadCanvas" width="1200" height="800"></canvas>
        <div class="download-section">
            <button onclick="downloadDownload()">📥 Stáhnout download.png</button>
            <button onclick="regenerateDownload()">🔄 Regenerovat</button>
        </div>
        
        <div class="info">
            <h3>Jak použít:</h3>
            <ol>
                <li>Stáhněte všechny 4 obrázky kliknutím na tlačítka</li>
                <li>Uložte je do www/ složky jako hero.png, faq.png, overlay.png a download.png</li>
                <li>Otevřete <a href="index.php">index.php</a> pro finální výsledek</li>
            </ol>

            <h4>Nové funkce:</h4>
            <ul>
                <li><strong>Hero sekce:</strong> Statické pozadí (fixed) s tmavě modrým překrytím</li>
                <li><strong>FAQ sekce:</strong> Statické pozadí (fixed) s průhledným kontejnerem</li>
                <li><strong>Platformy:</strong> Šedé kontejnery, aktivní OS v barvě systému</li>
                <li><strong>Download:</strong> 70% neprůhlednost pro aktivní OS kontejner</li>
                <li><strong>Parallax efekt:</strong> Stránka se posouvá přes statická pozadí (desktop)</li>
                <li><strong>Mobilní kompatibilita:</strong> Scroll pozadí pro telefony a tablety</li>
                <li><strong>iOS Safari fix:</strong> Automatická detekce a fallback</li>
            </ul>
        </div>
        
        <div class="download-section">
            <h3>Alternatívne riešenie:</h3>
            <p>Ak chcete použiť vlastné obrázky, nahraďte hero.png a faq.png vlastnými súbormi. 
            Odporúčané rozmery sú minimálne 1200x800px pre najlepšiu kvalitu.</p>
        </div>
    </div>

    <script>
        // Vytvorenie hero.png
        function createHeroImage() {
            const canvas = document.getElementById('heroCanvas');
            const ctx = canvas.getContext('2d');
            
            // Gradient pozadie
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(0.5, '#764ba2');
            gradient.addColorStop(1, '#667eea');
            
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // Pridaj geometrické tvary
            ctx.globalAlpha = 0.1;
            for (let i = 0; i < 20; i++) {
                ctx.fillStyle = '#ffffff';
                ctx.beginPath();
                ctx.arc(
                    Math.random() * canvas.width,
                    Math.random() * canvas.height,
                    Math.random() * 100 + 50,
                    0,
                    2 * Math.PI
                );
                ctx.fill();
            }
            
            // Pridaj text
            ctx.globalAlpha = 0.3;
            ctx.fillStyle = '#ffffff';
            ctx.font = 'bold 120px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('VOXO LOXO', canvas.width / 2, canvas.height / 2);
            
            ctx.font = 'bold 60px Arial';
            ctx.fillText('čtečka titulků', canvas.width / 2, canvas.height / 2 + 100);
            
            // Pridaj hviezdy
            ctx.globalAlpha = 0.8;
            ctx.fillStyle = '#FFD700';
            for (let i = 0; i < 50; i++) {
                const x = Math.random() * canvas.width;
                const y = Math.random() * canvas.height;
                const size = Math.random() * 3 + 1;
                
                ctx.beginPath();
                ctx.arc(x, y, size, 0, 2 * Math.PI);
                ctx.fill();
            }
        }
        
        // Vytvorenie faq.png
        function createFaqImage() {
            const canvas = document.getElementById('faqCanvas');
            const ctx = canvas.getContext('2d');
            
            // Základné pozadie
            const gradient = ctx.createRadialGradient(
                canvas.width / 2, canvas.height / 2, 0,
                canvas.width / 2, canvas.height / 2, canvas.width / 2
            );
            gradient.addColorStop(0, '#f8f9fa');
            gradient.addColorStop(1, '#e9ecef');
            
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // Geometrický vzor
            const size = 60;
            ctx.globalAlpha = 0.1;
            
            for (let x = 0; x < canvas.width; x += size) {
                for (let y = 0; y < canvas.height; y += size) {
                    if ((x / size + y / size) % 2 === 0) {
                        ctx.fillStyle = '#007bff';
                    } else {
                        ctx.fillStyle = '#28a745';
                    }
                    
                    ctx.beginPath();
                    ctx.arc(x + size / 2, y + size / 2, size / 3, 0, 2 * Math.PI);
                    ctx.fill();
                }
            }
            
            // Pridaj otázky
            ctx.globalAlpha = 0.2;
            ctx.fillStyle = '#495057';
            ctx.font = 'bold 80px Arial';
            ctx.textAlign = 'center';
            
            const questions = ['?', '?', '?', '?', '?'];
            questions.forEach((q, i) => {
                const x = (canvas.width / (questions.length + 1)) * (i + 1);
                const y = canvas.height / 2 + Math.sin(i) * 100;
                ctx.fillText(q, x, y);
            });
            
            // Pridaj FAQ text
            ctx.globalAlpha = 0.15;
            ctx.font = 'bold 100px Arial';
            ctx.fillText('FAQ', canvas.width / 2, canvas.height / 2 + 200);
        }
        
        // Download funkce
        function downloadHero() {
            const canvas = document.getElementById('heroCanvas');
            const link = document.createElement('a');
            link.download = 'hero.png';
            link.href = canvas.toDataURL();
            link.click();
        }
        
        function downloadFaq() {
            const canvas = document.getElementById('faqCanvas');
            const link = document.createElement('a');
            link.download = 'faq.png';
            link.href = canvas.toDataURL();
            link.click();
        }
        
        // Regenerate funkce
        function regenerateHero() {
            createHeroImage();
        }
        
        function regenerateFaq() {
            createFaqImage();
        }
        
        // Vytvorenie overlay.png
        function createOverlayImage() {
            const canvas = document.getElementById('overlayCanvas');
            const ctx = canvas.getContext('2d');

            // Tmavé pozadie
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#343a40');
            gradient.addColorStop(0.5, '#495057');
            gradient.addColorStop(1, '#343a40');

            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // Tech vzor - kruhy a čiary
            ctx.globalAlpha = 0.1;
            ctx.strokeStyle = '#ffffff';
            ctx.lineWidth = 2;

            // Mriežka
            for (let x = 0; x < canvas.width; x += 100) {
                ctx.beginPath();
                ctx.moveTo(x, 0);
                ctx.lineTo(x, canvas.height);
                ctx.stroke();
            }

            for (let y = 0; y < canvas.height; y += 100) {
                ctx.beginPath();
                ctx.moveTo(0, y);
                ctx.lineTo(canvas.width, y);
                ctx.stroke();
            }

            // Kruhy
            ctx.globalAlpha = 0.2;
            for (let i = 0; i < 15; i++) {
                ctx.beginPath();
                ctx.arc(
                    Math.random() * canvas.width,
                    Math.random() * canvas.height,
                    Math.random() * 80 + 20,
                    0,
                    2 * Math.PI
                );
                ctx.stroke();
            }

            // Platform ikony
            ctx.globalAlpha = 0.15;
            ctx.fillStyle = '#ffffff';
            ctx.font = 'bold 150px Arial';
            ctx.textAlign = 'center';

            const platforms = ['⊞', '⌘', '🐧'];
            platforms.forEach((icon, i) => {
                const x = (canvas.width / (platforms.length + 1)) * (i + 1);
                const y = canvas.height / 2;
                ctx.fillText(icon, x, y);
            });
        }

        // Vytvorenie download.png
        function createDownloadImage() {
            const canvas = document.getElementById('downloadCanvas');
            const ctx = canvas.getContext('2d');

            // Svetlé pozadie
            const gradient = ctx.createRadialGradient(
                canvas.width / 2, canvas.height / 2, 0,
                canvas.width / 2, canvas.height / 2, canvas.width / 2
            );
            gradient.addColorStop(0, '#ffffff');
            gradient.addColorStop(1, '#f8f9fa');

            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // Cloud vzor
            ctx.globalAlpha = 0.1;
            ctx.fillStyle = '#007bff';

            for (let i = 0; i < 20; i++) {
                const x = Math.random() * canvas.width;
                const y = Math.random() * canvas.height;
                const size = Math.random() * 60 + 30;

                // Cloud shape
                ctx.beginPath();
                ctx.arc(x, y, size, 0, 2 * Math.PI);
                ctx.arc(x + size * 0.5, y, size * 0.8, 0, 2 * Math.PI);
                ctx.arc(x - size * 0.5, y, size * 0.8, 0, 2 * Math.PI);
                ctx.fill();
            }

            // Download šípky
            ctx.globalAlpha = 0.2;
            ctx.fillStyle = '#28a745';
            ctx.font = 'bold 100px Arial';
            ctx.textAlign = 'center';

            for (let i = 0; i < 8; i++) {
                const x = Math.random() * canvas.width;
                const y = Math.random() * canvas.height;
                ctx.fillText('↓', x, y);
            }

            // Download text
            ctx.globalAlpha = 0.15;
            ctx.font = 'bold 120px Arial';
            ctx.fillText('DOWNLOAD', canvas.width / 2, canvas.height / 2);
        }

        // Download funkce
        function downloadOverlay() {
            const canvas = document.getElementById('overlayCanvas');
            const link = document.createElement('a');
            link.download = 'overlay.png';
            link.href = canvas.toDataURL();
            link.click();
        }

        function downloadDownload() {
            const canvas = document.getElementById('downloadCanvas');
            const link = document.createElement('a');
            link.download = 'download.png';
            link.href = canvas.toDataURL();
            link.click();
        }

        // Regenerate funkce
        function regenerateOverlay() {
            createOverlayImage();
        }

        function regenerateDownload() {
            createDownloadImage();
        }

        // Inicializácia
        document.addEventListener('DOMContentLoaded', function() {
            createHeroImage();
            createFaqImage();
            createOverlayImage();
            createDownloadImage();
        });
    </script>
</body>
</html>
