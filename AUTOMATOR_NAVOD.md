# 🤖 Vytvorenie Automator aplikácie pre Subtitle Reader

## Prečo Automator?
Automator aplikácie sa spúšťajú **bez dialógov** a správajú sa ako natívne macOS aplikácie.

## 📋 Krok za krokom návod:

### 1. <PERSON><PERSON><PERSON> Automator
- <PERSON><PERSON><PERSON> `Cmd + Space` a napíš "Automator"
- Alebo choď do Applications → Automator

### 2. Vytvor novú aplikáciu
- <PERSON><PERSON><PERSON> na "New Document"
- Vyber "Application" (ikona s ozubeným kolieskom)
- Klikni "Choose"

### 3. Pridaj "Run Shell Script" akciu
- V ľavom paneli nájdi "Run Shell Script"
- Pretiahni ju do pravého panelu (workflow area)

### 4. Nastav shell script
V textovom poli nahraď obsah týmto kódom:

```bash
# Get the directory where this app is located
APP_DIR="$(dirname "$0")"
cd "$APP_DIR"

# Find Python - tested path
PYTHON_CMD="/Library/Frameworks/Python.framework/Versions/3.10/bin/python3"

# Try alternatives if not found
if [ ! -f "$PYTHON_CMD" ]; then
    if command -v python3 &> /dev/null; then
        PYTHON_CMD="python3"
    elif command -v python &> /dev/null; then
        PYTHON_CMD="python"
    else
        exit 1
    fi
fi

# Check if main file exists
if [ ! -f "main_qt.py" ]; then
    exit 1
fi

# Launch Subtitle Reader
exec $PYTHON_CMD main_qt.py
```

### 5. Nastav možnosti
- **Shell**: `/bin/bash` (predvolené)
- **Pass input**: "as arguments" (predvolené)

### 6. Ulož aplikáciu
- Stlač `Cmd + S`
- Názov: `SubtitleReader`
- Umiestnenie: Tvoj SubtitleReader adresár (`/Applications/SubtitleReader`)
- Klikni "Save"

### 7. Otestuj aplikáciu
- Zatvor Automator
- V Finderi choď do `/Applications/SubtitleReader`
- Dvojklik na `SubtitleReader.app`
- **Aplikácia sa spustí bez dialógov!** 🎉

## ✅ Výhody Automator aplikácie:
- ✅ Žiadne dialógy pri spustení
- ✅ Natívny macOS vzhľad
- ✅ Ikona v Docku
- ✅ Správa sa ako normálna aplikácia
- ✅ Môžeš ju pridať do Applications

## 🎯 Výsledok:
Po vytvorení budeš mať `SubtitleReader.app` ktorá:
- Sa spustí dvojklikom bez dialógov
- Automaticky nájde Python
- Spustí Subtitle Reader na pozadí
- Nebude zobrazovať terminál

## 🔧 Riešenie problémov:
Ak sa aplikácia nespustí:
1. Skontroluj, či je `main_qt.py` v rovnakom adresári ako `SubtitleReader.app`
2. Skontroluj cestu k Pythonu v scripte
3. Otvor Automator aplikáciu a skontroluj script
