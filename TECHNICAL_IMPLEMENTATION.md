# 🔧 Technická implementácia - Platform Loader System

## 🎯 Architektúra riešenia

### <PERSON><PERSON><PERSON>m, ktor<PERSON> rie<PERSON>
Pôvodne existovali dve samostatné aplikácie:
- `mac/` - aplik<PERSON>cia pre macOS
- `windows/` - aplikácia pre Windows

Každá mala svoje špecifické implementácie pre:
- **Hotkey management** (pynput vs keyboard)
- **TTS systémy** (macOS say vs Windows SAPI)
- **Platform-specific API calls**

### Naše riešenie: Platform Loader Pattern

## 🏗️ Kľúčové komponenty

### 1. `platform_loader.py` - Centrálny loader

```python
class PlatformModuleLoader:
    def __init__(self):
        self.current_platform = self.get_current_platform()
        self._hotkey_manager = None
        self._tts_manager = None
        
    def get_current_platform(self):
        """Detekuje aktuálnu platformu"""
        system = platform.system().lower()
        platform_map = {
            'darwin': 'macos',
            'windows': 'windows', 
            'linux': 'linux'
        }
        return platform_map.get(system, 'unknown')
    
    def get_hotkey_manager(self):
        """Lazy loading hotkey managera"""
        if self._hotkey_manager is None:
            if self.current_platform == 'macos':
                import mac_hotkey_manager
                self._hotkey_manager = mac_hotkey_manager
            elif self.current_platform == 'windows':
                import win_hotkey_manager  
                self._hotkey_manager = win_hotkey_manager
        return self._hotkey_manager
```

### 2. Wrapper Functions Pattern

```python
# V app_logic.py
def _get_tts_function(func_name):
    """Dynamicky získa TTS funkciu"""
    from platform_loader import get_tts_manager
    tts_manager = get_tts_manager()
    if hasattr(tts_manager, func_name):
        return getattr(tts_manager, func_name)
    return lambda *args, **kwargs: None

# Wrapper funkcie
def speak_text(*args, **kwargs):
    return _get_tts_function('speak_text')(*args, **kwargs)

def init_tts_worker(*args, **kwargs):
    return _get_tts_function('init_tts_worker')(*args, **kwargs)
```

### 3. Riešenie cirkulárnych importov

**Problém:** `app_logic.py` ↔ `hotkey_manager.py` ↔ `tts_manager.py`

**Riešenie:** Dynamic imports v wrapper funkciách
```python
def _get_app_logic_function(func_name):
    """Dynamicky importuje app_logic funkcie"""
    try:
        import app_logic
        return getattr(app_logic, func_name, lambda *args, **kwargs: None)
    except ImportError:
        return lambda *args, **kwargs: None
```

## 📦 Platformovo-špecifické implementácie

### macOS Implementation

#### `mac_hotkey_manager.py`
```python
import pynput
from pynput import keyboard

# macOS špecifické klávesy
HOTKEY_MAPPINGS = {
    'toggle_reading': '<cmd>+<alt>+x',
    'copy_text': '<cmd>+<alt>+c',
    'open_settings': '<cmd>+<alt>+i'
}

def register_global_hotkeys():
    """Registruje globálne hotkeys pre macOS"""
    with keyboard.GlobalHotKeys(HOTKEY_MAPPINGS) as h:
        h.join()
```

#### `mac_tts_manager.py`
```python
import subprocess

def speak_text(text, voice="Laura (Enhanced)", rate=250):
    """macOS TTS pomocou say command"""
    cmd = ['/usr/bin/say', '-v', voice, '-r', str(rate), text]
    subprocess.Popen(cmd)

def get_available_voices():
    """Získa dostupné macOS hlasy"""
    result = subprocess.run(['/usr/bin/say', '-v', '?'], 
                          capture_output=True, text=True)
    # Parse voices...
```

### Windows Implementation

#### `win_hotkey_manager.py`
```python
import keyboard

# Windows špecifické klávesy  
HOTKEY_MAPPINGS = {
    'toggle_reading': 'alt+x',
    'copy_text': 'alt+c', 
    'open_settings': 'ctrl+alt+i'
}

def register_global_hotkeys():
    """Registruje globálne hotkeys pre Windows"""
    for action, hotkey in HOTKEY_MAPPINGS.items():
        keyboard.add_hotkey(hotkey, lambda a=action: handle_hotkey(a))
```

#### `win_tts_manager.py`
```python
import pyttsx3

def speak_text(text, voice=None, rate=200):
    """Windows TTS pomocou pyttsx3/SAPI"""
    engine = pyttsx3.init()
    if voice:
        engine.setProperty('voice', voice)
    engine.setProperty('rate', rate)
    engine.say(text)
    engine.runAndWait()

def get_available_voices():
    """Získa dostupné Windows hlasy"""
    engine = pyttsx3.init()
    voices = engine.getProperty('voices')
    return [voice.name for voice in voices]
```

## 🔄 Migration Process

### Krok 1: Identifikácia rozdielov
```bash
# Porovnanie súborov
diff -u mac/hotkey_manager.py windows/hotkey_manager.py
diff -u mac/tts_manager.py windows/tts_manager.py
```

### Krok 2: Vytvorenie platform loader
1. Analýza spoločného API
2. Implementácia `PlatformModuleLoader`
3. Vytvorenie wrapper funkcií

### Krok 3: Refaktoring existujúceho kódu
```python
# Pred refaktoringom
import tts_manager
tts_manager.speak_text("Hello")

# Po refaktoringu  
from platform_loader import get_tts_function
speak_text = get_tts_function('speak_text')
speak_text("Hello")
```

### Krok 4: Aktualizácia všetkých importov
- `app_logic.py` - 15 wrapper funkcií
- `qt_gui.py` - 8 wrapper funkcií  
- `main_qt.py` - 3 wrapper funkcie
- Všetky test súbory

## 🧪 Testing Strategy

### Unit Tests
```python
def test_platform_detection():
    loader = PlatformModuleLoader()
    assert loader.current_platform in ['macos', 'windows', 'linux']

def test_module_loading():
    loader = PlatformModuleLoader()
    hotkey_mgr = loader.get_hotkey_manager()
    assert hotkey_mgr is not None
    assert hasattr(hotkey_mgr, 'register_global_hotkeys')
```

### Integration Tests
```python
def test_tts_functionality():
    speak_text = get_tts_function('speak_text')
    # Test by mal overiť, že sa TTS spustí bez chyby
    speak_text("Test message")
```

### Platform-specific Tests
```python
@pytest.mark.skipif(platform.system() != 'Windows', 
                   reason="Windows-specific test")
def test_windows_hotkeys():
    # Test Windows hotkey functionality
    pass
```

## 🚀 Performance Optimizations

### Lazy Loading
```python
class PlatformModuleLoader:
    def get_tts_manager(self):
        if self._tts_manager is None:
            # Načíta modul len pri prvom použití
            self._tts_manager = self._load_tts_manager()
        return self._tts_manager
```

### Caching
```python
@functools.lru_cache(maxsize=128)
def get_tts_function(func_name):
    """Cache pre často používané funkcie"""
    return _get_tts_function_impl(func_name)
```

## 🔒 Error Handling

### Graceful Degradation
```python
def get_tts_function(func_name):
    try:
        tts_manager = get_tts_manager()
        return getattr(tts_manager, func_name)
    except (ImportError, AttributeError) as e:
        logging.error(f"TTS function '{func_name}' not available: {e}")
        return lambda *args, **kwargs: None
```

### Platform Fallbacks
```python
def get_hotkey_manager(self):
    try:
        if self.current_platform == 'windows':
            import win_hotkey_manager
            return win_hotkey_manager
    except ImportError:
        logging.warning("Windows hotkey manager not available, using fallback")
        import fallback_hotkey_manager
        return fallback_hotkey_manager
```

## 📊 Metrics & Monitoring

### Performance Metrics
- Module loading time
- Function call overhead  
- Memory usage per platform

### Error Tracking
- Failed imports
- Missing functions
- Platform detection failures

## 🔮 Future Enhancements

### 1. Plugin Architecture
```python
class PluginLoader:
    def load_platform_plugin(self, platform_name):
        # Dynamické načítanie pluginov
        pass
```

### 2. Configuration-driven Loading
```yaml
# platform_config.yaml
platforms:
  windows:
    hotkey_manager: "win_hotkey_manager"
    tts_manager: "win_tts_manager"
  macos:
    hotkey_manager: "mac_hotkey_manager" 
    tts_manager: "mac_tts_manager"
```

### 3. Cross-platform Testing
- Automated testing na všetkých platformách
- CI/CD pipeline s matrix builds
- Docker containers pre testing

---
*Technická dokumentácia - Verzia 1.0*
