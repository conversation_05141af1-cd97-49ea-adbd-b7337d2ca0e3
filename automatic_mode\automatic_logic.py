import logging
import threading
import queue
import time
from collections import deque
import os
import glob

import common_config as config
import common_utils
from tts_manager import speak_text
from full_automatic_mode.full_automatic_logic import log_text_change_to_csv
from i18n_manager import get_tts_message

def reset_automatic_detection_state():
    """
    Centrálna funkcia na resetovanie všetkých premenných automatického režimu.
    Volá sa pri každom spustení automatického režimu.
    """
    config.detection_text_history.clear()
    config.consecutive_static_count = 0
    config.last_detected_mode = None
    config.mode_stable_since = None
    config.current_base_text = ""

    # Vyčistenie fronty automatickej detekcie
    while not config.automatic_detection_queue.empty():
        try:
            config.automatic_detection_queue.get_nowait()
        except queue.Empty:
            break

    logging.info("[AUTO_DETECT] Všetky premenné automatického režimu resetované.")

# Import pre prepínanie režimov
import app_logic

def is_text_growing_dynamically(new_text, base_text):
    """
    Detekuje, či nový text je dynamickým rastom základného textu.
    Vráti True ak nový text obsahuje základný text + nové slová na konci.
    """
    if not base_text or not new_text:
        return False

    # Normalizujeme texty (odstránime extra medzery)
    base_normalized = " ".join(base_text.split())
    new_normalized = " ".join(new_text.split())

    # Ak je nový text kratší, nie je to rast
    if len(new_normalized) <= len(base_normalized):
        return False

    # Ak nový text začína základným textom, je to dynamický rast
    if new_normalized.startswith(base_normalized):
        added_part = new_normalized[len(base_normalized):].strip()
        if added_part:  # Niečo sa pridalo
            logging.debug(f"[AUTO_DETECT] Dynamický rast detekovaný: '{base_normalized}' → '{new_normalized}'")
            return True

    return False

def analyze_text_for_mode_detection(current_text):
    """
    Analyzuje aktuálny text a rozhoduje o režime na základe histórie.
    Vráti: ('static', 'dynamic', alebo None)
    """
    if not current_text.strip():
        return None

    # Pridáme text do histórie
    config.detection_text_history.append(current_text)

    # Ak nemáme dostatok histórie, pokračujeme v zbere
    if len(config.detection_text_history) < 2:
        return None

    # Porovnáme s predchádzajúcim textom
    previous_text = config.detection_text_history[-2]
    similarity = common_utils.calculate_similarity(current_text, previous_text)

    # Detekcia statického textu
    if similarity >= config.STATIC_SIMILARITY_THRESHOLD:
        config.consecutive_static_count += 1
        logging.debug(f"[AUTO_DETECT] Statický text #{config.consecutive_static_count}: podobnosť {similarity:.2f}")

        # Ak máme 10+ po sebe idúcich statických textov
        if config.consecutive_static_count >= config.STATIC_SEQUENCE_THRESHOLD:
            return 'static'
    else:
        # Text sa zmenil - resetujeme počítadlo statických
        config.consecutive_static_count = 0

        # Detekcia dynamického rastu
        if config.current_base_text:
            if is_text_growing_dynamically(current_text, config.current_base_text):
                return 'dynamic'

        # Aktualizujeme základný text pre budúce porovnania
        config.current_base_text = current_text
        logging.debug(f"[AUTO_DETECT] Nový základný text: '{config.current_base_text}'")

    return None

def subtitle_detection_worker():
    """
    Priebežná detekcia typu titulkov:
    1. Číta text ako dočasný statický režim
    2. Priebežne analyzuje každý text
    3. Rozhoduje o režime na základe vzorcov
    4. Prepína režim pri detekcii
    """
    logging.info("Automatic subtitle detection worker started.")
    if config.gui_instance:
        config.gui_instance.update_status("Automatická detekcia: Analyzujem titulky...")

    # Inicializácia už bola vykonaná v start_automatic_detection_processing()

    # Spustíme dočasný statický režim pre čítanie počas detekcie
    import static_mode.static_logic
    static_mode.static_logic.start_static_mode_processing()
    logging.info("[AUTO_DETECT] Spustený dočasný statický režim pre čítanie počas detekcie.")

    # Hlavný cyklus priebežnej detekcie
    while config.is_reading and config.reading_mode == 'automatic':
        try:
            raw_ocr_data = config.automatic_detection_queue.get(timeout=1.0)
            text = raw_ocr_data.get('text', '')
            cycle_id = raw_ocr_data.get('cycle_id', -1)

            cleaned_text = common_utils.clean_text(text)
            current_text_line = " ".join(cleaned_text) if cleaned_text else ""

            # Analyzujeme text pre detekciu režimu
            detected_mode = analyze_text_for_mode_detection(current_text_line)

            if detected_mode:
                # Režim bol detekovaný
                current_time = time.time()

                if config.last_detected_mode != detected_mode:
                    # Nový režim detekovaný
                    config.last_detected_mode = detected_mode
                    config.mode_stable_since = current_time
                    logging.info(f"[AUTO_DETECT] Detekovaný nový režim: {detected_mode.upper()}")

                    # Pri prvej detekcii okamžite prepneme
                    if detected_mode == 'dynamic':
                        logging.info("[AUTO_DETECT] Okamžité prepnutie na DYNAMICKÝ režim.")
                        if config.gui_instance:
                            config.gui_instance.update_status("Detekovaný dynamický režim. Prepínam...")

                        # Zastavíme dočasný statický režim
                        static_mode.static_logic.stop_static_mode_processing()

                        # Prepneme na dynamický režim
                        config.reading_mode = "dynamic"
                        tts_msg = get_tts_message("tts_switched_dynamic")
                        speak_text(tts_msg)
                        log_text_change_to_csv(None, 'TTS_SPEAK', 1.0, '', tts_msg, tts_msg)

                        import dynamic_mode.dynamic_logic
                        dynamic_mode.dynamic_logic.start_dynamic_mode_processing()
                        break  # Ukončíme automatickú detekciu

                    elif detected_mode == 'static':
                        logging.info("[AUTO_DETECT] Okamžité prepnutie na STATICKÝ režim.")
                        if config.gui_instance:
                            config.gui_instance.update_status("Detekovaný statický režim. Prepínam...")

                        # Zastavíme dočasný statický režim
                        static_mode.static_logic.stop_static_mode_processing()

                        # Prepneme na statický režim
                        config.reading_mode = "static"

                        # Nastavíme správny interval pre statický režim
                        config.current_ocr_interval = 0.2
                        logging.info(f"Nastavený pomalší interval OCR pre statický režim: {config.current_ocr_interval}s")

                        # Spustíme skutočný statický režim
                        static_mode.static_logic.start_static_mode_processing()

                        tts_msg = get_tts_message("tts_switched_to_static")
                        speak_text(tts_msg)
                        log_text_change_to_csv(None, 'TTS_SPEAK', 1.0, '', tts_msg, tts_msg)
                        break  # Ukončíme automatickú detekciu

                elif config.mode_stable_since and (current_time - config.mode_stable_since) >= config.MODE_STABILITY_TIME:
                    # Režim je stabilný 4+ sekúnd
                    if detected_mode == 'static':
                        logging.info("[AUTO_DETECT] Potvrdený STATICKÝ režim po 4 sekundách stability.")
                        if config.gui_instance:
                            config.gui_instance.update_status("Potvrdený statický režim.")

                        # Už beží statický režim, len oznámime
                        tts_msg = get_tts_message("tts_detected_static")
                        speak_text(tts_msg)
                        log_text_change_to_csv(None, 'TTS_SPEAK', 1.0, '', tts_msg, tts_msg)
                        break  # Ukončíme automatickú detekciu

            config.automatic_detection_queue.task_done()

        except queue.Empty:
            continue
        except Exception as e:
            logging.error(f"Chyba v subtitle_detection_worker: {e}")
            time.sleep(0.1)

    logging.info("Automatic subtitle detection worker ukončený.")




def handle_automatic_detection(raw_ocr_data):
    """
    Táto funkcia je volaná dispečerom a posiela OCR dáta do detekčnej fronty.
    """
    # Odstránený testovací režim, posielajú sa len reálne dáta
    config.automatic_detection_queue.put(raw_ocr_data)


def start_automatic_detection_processing():
    # Vyčistenie stavu pri štarte pomocou centrálnej funkcie
    reset_automatic_detection_state()

    config.automatic_detection_thread = threading.Thread(target=subtitle_detection_worker, daemon=True)
    config.automatic_detection_thread.start()
    logging.info("Automatic detection processing started.")


def stop_automatic_detection_processing():
    if config.automatic_detection_thread and config.automatic_detection_thread.is_alive():
        config.automatic_detection_thread.join(timeout=1.0)
        if config.automatic_detection_thread.is_alive():
            logging.warning("Automatic detection thread sa neukončil včas.")
        config.automatic_detection_thread = None

    # Resetovanie stavu pri zastavení pre čistý štart pri ďalšom spustení
    reset_automatic_detection_state()
    logging.info("Automatic detection processing stopped.")