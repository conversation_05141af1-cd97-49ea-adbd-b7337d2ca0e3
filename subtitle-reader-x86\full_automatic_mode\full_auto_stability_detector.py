"""
Full Automatic Mode - Detekcia stability riadkov pre rozhodovanie o type titulkov.

NOVÁ LOGIKA:
- <PERSON><PERSON><PERSON> be<PERSON> režim (číta hneď)
- Sleduje stabilitu prvého a druhého riadku
- Detekuje vzorce správania pre rozhodnutie o prepnutí na statický režim
- Detekcia prebieha len pri dvojriadkových titulkoch

PODMIENKY PRE ZOSTANIE V DYNAMICKOM REŽIME:
- Prvý riadok stabilný (2+ krát rovnaký ≥95%)
- Druhý riadok stabilný (2+ kr<PERSON>t rovnaký)
- Prvý riadok sa ZMENÍ
- Nový prvý riadok stabilný (2+ krát)
- Nový druhý riadok RASTIE (pribúdajú slová)

PODMIENKY PRE PREPNUTIE NA STATICKÝ REŽIM:
- Prvý riadok stabilný (2+ kr<PERSON>t rovnaký ≥95%)
- Druhý riadok stabilný (2+ kr<PERSON>t rovnaký)
- Prvý riadok sa ZMENÍ
- Nový prvý riadok stabilný (2+ krát)
- Nový druhý riadok TIEŽ stabilný (2+ krát rovnaký)
"""

import logging
import time
from difflib import SequenceMatcher

import common_config as config
import common_utils


def reset_stability_detection():
    """Resetuje všetky premenné pre detekciu stability."""
    config.current_first_line_stability = 0.0
    config.current_second_line_stability = 0.0
    config.first_line_stability_count = 0
    config.second_line_stability_count = 0
    config.second_line_growth_count = 0
    config.second_line_growth_detected = False

    # === SPOLOČNÉ POČÍTADLÁ PRE PREPNUTIE NA STATICKÝ REŽIM ===
    config.stabilita_aktualneho_textu = 0  # Koľko cyklov je aktuálny text stabilný
    config.pocet_roznych_stabilnych_textov = 0  # Koľko rôznych stabilných textov sme mali po sebe
    config.posledny_stabilny_text = ""  # Posledný text, ktorý bol označený ako stabilný
    config.detection_phase = "initial"
    config.mode_decision = "staying_dynamic"
    config.lines_detected = "none"
    config.stability_previous_first_line = ""
    config.stability_previous_second_line = ""
    config.stability_first_change_detected = False
    
    logging.info("[STABILITY_DETECT] Stav detekcie stability resetovaný")


def spracuj_stabilitu_textu(current_text, similarity, threshold, text_type):
    """
    Spracuje stabilitu textu a aktualizuje spoločné počítadlá.

    Args:
        current_text: Aktuálny text
        similarity: Podobnosť s predchádzajúcim textom
        threshold: Prah stability
        text_type: Typ textu ("druhý riadok" alebo "jednoriadkový")

    Returns:
        bool: True ak sa má prepnúť na statický režim
    """
    import common_config as config

    if similarity >= threshold:
        # Text je stabilný
        config.stabilita_aktualneho_textu += 1
        logging.debug(f"[STABILITY_DETECT] {text_type} stabilný - stabilita aktuálneho textu: {config.stabilita_aktualneho_textu}")

        # Kontrola, či text dosiahol požadovanú stabilitu
        if config.stabilita_aktualneho_textu >= config.STABILITA_JEDNEHO_TEXTU_CYKLY:
            # Text je dostatočne stabilný - kontrola, či je to nový text
            if current_text != config.posledny_stabilny_text:
                # Nový stabilný text
                config.pocet_roznych_stabilnych_textov += 1
                config.posledny_stabilny_text = current_text
                logging.info(f"[STABILITY_DETECT] Nový stabilný text: '{current_text}' - počet rôznych stabilných textov: {config.pocet_roznych_stabilnych_textov}")

                # Reset stability aktuálneho textu pre ďalší text
                config.stabilita_aktualneho_textu = 0

                # Kontrola, či máme dostatok stabilných textov
                if config.pocet_roznych_stabilnych_textov >= config.POCET_ROZNYCH_STABILNYCH_TEXTOV_CIEL:
                    logging.info(f"[STABILITY_DETECT] Dosiahnutý cieľ {config.POCET_ROZNYCH_STABILNYCH_TEXTOV_CIEL} rôznych stabilných textov - prepínam na statický režim!")
                    return True
    else:
        # Text sa zmenil alebo je nestabilný
        config.stabilita_aktualneho_textu = 0
        logging.debug(f"[STABILITY_DETECT] {text_type} nestabilný - resetujem stabilitu aktuálneho textu na 0")

    return False


def calculate_text_similarity(text1, text2):
    """Vypočíta podobnosť medzi dvoma textami (0.0 - 1.0)."""
    if not text1 or not text2:
        return 0.0
    
    # Normalizácia textov
    text1_clean = text1.strip().lower()
    text2_clean = text2.strip().lower()
    
    if text1_clean == text2_clean:
        return 1.0
    
    # Použitie SequenceMatcher pre presné meranie podobnosti
    similarity = SequenceMatcher(None, text1_clean, text2_clean).ratio()
    return similarity


def detect_text_growth(previous_text, current_text):
    """
    Detekuje, či sa text rozšíril (pribúdli slová).
    Vráti True ak current_text začína previous_text a je dlhší.
    """
    if not previous_text or not current_text:
        return False

    prev_clean = previous_text.strip()
    curr_clean = current_text.strip()

    # Kontrola, či nový text začína starým textom a je dlhší
    if curr_clean.startswith(prev_clean) and len(curr_clean) > len(prev_clean):
        added_part = curr_clean[len(prev_clean):].strip()
        # Kontrola, že sa pridali skutočné slová (nie len medzery/znaky)
        if added_part and len(added_part.split()) >= 1:
            added_words = added_part.split()
            logging.debug(f"[GROWTH_DETECT] Rast detekovaný: '{prev_clean}' + '{added_part}' = '{curr_clean}' (pridané {len(added_words)} slov)")
            return True

    logging.debug(f"[GROWTH_DETECT] Žiadny rast: '{prev_clean}' → '{curr_clean}'")
    return False


def analyze_two_line_stability():
    """
    Hlavná funkcia pre analýzu stability dvojriadkových titulkov.
    Volá sa z full_automatic_logic.py pri každom novom texte.
    """
    # Získanie aktuálnych riadkov z dynamického režimu
    current_first = getattr(config, 'current_first_line', '')
    current_second = getattr(config, 'current_second_line', '')
    
    # Kontrola typu titulkov
    if not current_first and not current_second:
        config.lines_detected = "none"
        return  # Žiadny text - nič na detekciu
    elif current_first and current_second:
        config.lines_detected = "double"
        # Pokračujeme s detekciou dvojriadkových titulkov
    elif current_first and not current_second:
        config.lines_detected = "single"
        # Pokračujeme s detekciou jednoriadkových titulkov
    else:
        config.lines_detected = "none"
        return  # Neplatný stav
    
    # === SPRACOVANIE DVOJRIADKOVÝCH TITULKOV ===
    if config.lines_detected == "double":
        # === VÝPOČET STABILITY RIADKOV ===

        # Stabilita prvého riadku
        if config.stability_previous_first_line:
            config.current_first_line_stability = calculate_text_similarity(
                config.stability_previous_first_line, current_first
            )

            if config.current_first_line_stability >= config.STABILITY_FIRST_LINE_THRESHOLD:
                config.first_line_stability_count += 1
            else:
                # Prvý riadok sa zmenil
                config.first_line_stability_count = 0
                if config.detection_phase == "initial":
                    config.detection_phase = "first_change"
                    config.stability_first_change_detected = True
                    logging.info(f"[STABILITY_DETECT] Prvá zmena prvého riadku detekovaná: '{config.stability_previous_first_line}' → '{current_first}'")

        # Stabilita druhého riadku
        if config.stability_previous_second_line:
            config.current_second_line_stability = calculate_text_similarity(
                config.stability_previous_second_line, current_second
            )

            if config.current_second_line_stability >= config.STABILITY_SECOND_LINE_THRESHOLD:
                config.second_line_stability_count += 1
                config.second_line_growth_detected = False
            else:
                config.second_line_stability_count = 0

            # KONTROLA RASTU DRUHÉHO RIADKU NAJPRV (PRED rozhodnutím o prepnutí)
            if detect_text_growth(config.stability_previous_second_line, current_second):
                config.second_line_growth_count += 1
                config.second_line_growth_detected = True
                # OPRAVA: Reset stability count pri raste druhého riadku
                config.second_line_stability_count = 0
                # NOVÁ LOGIKA: Reset spoločných počítadiel pri dynamickom správaní
                config.stabilita_aktualneho_textu = 0
                config.pocet_roznych_stabilnych_textov = 0
                config.posledny_stabilny_text = ""
                logging.info(f"[STABILITY_DETECT] 📈 DRUHÝ RIADOK RASTIE: '{config.stability_previous_second_line}' → '{current_second}' - resetujem všetky spoločné počítadlá")
                # ZOSTÁVAME V DYNAMICKOM REŽIME - druhý riadok rastie
                config.mode_decision = "staying_dynamic"
                logging.info(f"[STABILITY_DETECT] ✅ ZOSTÁVAME V DYNAMICKOM REŽIME - druhý riadok rastie")
            else:
                config.second_line_growth_count = 0
                config.second_line_growth_detected = False

                # NOVÁ LOGIKA: Spracovanie stability pomocou spoločných počítadiel (LEN ak druhý riadok NERASTIE)
                should_switch = spracuj_stabilitu_textu(
                    current_second,
                    config.current_second_line_stability,
                    config.STABILITY_SECOND_LINE_THRESHOLD,
                    "druhý riadok"
                )

                if should_switch:
                    config.mode_decision = "switching_static"
                    logging.info(f"[STABILITY_DETECT] 🔄 PREPÍNAM NA STATICKÝ REŽIM!")
                    logging.info(f"[STABILITY_DETECT] Dosiahnuté {config.pocet_roznych_stabilnych_textov} rôznych stabilných textov")
                    switch_to_static_mode()
                    return

        # === ROZHODOVANIE O REŽIME ===

        if config.stability_first_change_detected and config.detection_phase == "first_change":
            config.detection_phase = "analyzing"

        if config.detection_phase == "analyzing":
            # Kontrola posunu riadkov v dynamických titulkoch
            if is_line_shift_in_dynamic_subtitles(current_first, current_second):
                # Toto je posun riadkov v dynamických titulkoch, nie statické titulky
                config.mode_decision = "staying_dynamic"
                logging.info(f"[STABILITY_DETECT] Detekovaný posun riadkov v dynamických titulkoch - zostávame v dynamickom režime")
                # Reset stability counts pre nový cyklus
                config.first_line_stability_count = 0
                config.second_line_stability_count = 0
                # Reset spoločných počítadiel pri posune riadkov
                config.stabilita_aktualneho_textu = 0
                config.pocet_roznych_stabilnych_textov = 0
                config.posledny_stabilny_text = ""
            else:
                # Ak sa nedostalo k prepnutiu vyššie a druhý riadok nerastie, zostávame v dynamickom režime
                if config.mode_decision != "switching_static":
                    config.mode_decision = "staying_dynamic"
                    if config.pocet_roznych_stabilnych_textov > 0:
                        logging.debug(f"[STABILITY_DETECT] Čakáme na viac stabilných textov ({config.pocet_roznych_stabilnych_textov}/{config.POCET_ROZNYCH_STABILNYCH_TEXTOV_CIEL})")


    # === SPRACOVANIE JEDNORIADKOVÝCH TITULKOV ===
    elif config.lines_detected == "single":
        # Máme len jednoriadkový text - použijeme ROVNAKÚ logiku ako pre dvojriadkové
        config.lines_detected = "single"

        # === VÝPOČET STABILITY JEDNORIADKOVÉHO TEXTU ===
        if config.stability_previous_first_line:
            config.current_first_line_stability = calculate_text_similarity(
                config.stability_previous_first_line, current_first
            )

            if config.current_first_line_stability >= config.STABILITY_FIRST_LINE_THRESHOLD:
                config.first_line_stability_count += 1
            else:
                # Text sa zmenil - reset
                config.first_line_stability_count = 0
                logging.debug(f"[STABILITY_DETECT] Jednoriadkový text sa zmenil, reset stability count")

            # NOVÁ LOGIKA: Spracovanie stability pomocou spoločných počítadiel
            should_switch = spracuj_stabilitu_textu(
                current_first,
                config.current_first_line_stability,
                config.STABILITY_FIRST_LINE_THRESHOLD,
                "jednoriadkový text"
            )

            if should_switch:
                config.mode_decision = "switching_static"
                logging.info(f"[STABILITY_DETECT] 🔄 PREPÍNAM NA STATICKÝ REŽIM - jednoriadkový text!")
                logging.info(f"[STABILITY_DETECT] Dosiahnuté {config.pocet_roznych_stabilnych_textov} rôznych stabilných textov")
                switch_to_static_mode()
                return
        else:
            # Prvý jednoriadkový text
            config.first_line_stability_count = 1

        # Reset druhého riadku pre jednoriadkový stav
        config.second_line_stability_count = 0
        config.second_line_growth_count = 0
        config.second_line_growth_detected = False
        # Spoločné počítadlo sa resetuje len pri zmene textu, nie pri prechode na jednoriadkový

        # === ROZHODOVANIE O REŽIME (NOVÁ LOGIKA) ===
        # Rozhodovanie už prebehlo v spracuj_stabilitu_textu()
        # Ak sme sa nedostali k prepnutiu, zostávame v dynamickom režime
        config.mode_decision = "staying_dynamic"
        if config.pocet_roznych_stabilnych_textov > 0:
            logging.debug(f"[STABILITY_DETECT] Čakáme na viac stabilných textov ({config.pocet_roznych_stabilnych_textov}/{config.POCET_ROZNYCH_STABILNYCH_TEXTOV_CIEL})")

        logging.debug(f"[STABILITY_DETECT] Rozhodnutie (jednoriadkový): {config.mode_decision}")

    else:
        # Žiadny text alebo neplatný stav
        config.mode_decision = "staying_dynamic"
        # Reset všetkých počítadiel
        config.first_line_stability_count = 0
        config.second_line_stability_count = 0
        config.second_line_growth_count = 0
        # NOVÉ POČÍTADLÁ
        config.stabilita_aktualneho_textu = 0
        config.pocet_roznych_stabilnych_textov = 0
        config.posledny_stabilny_text = ""
        # Reset časovača
        if hasattr(config, 'static_detection_start_time'):
            delattr(config, 'static_detection_start_time')

        logging.debug(f"[STABILITY_DETECT] Žiadny text - reset všetkých počítadiel")

    # Aktualizácia predchádzajúcich riadkov
    config.stability_previous_first_line = current_first
    config.stability_previous_second_line = current_second
    
    # Debug výpis
    log_stability_status()


def is_line_shift_in_dynamic_subtitles(current_first, current_second):
    """
    Detekuje, či sa jedná o posun riadkov v dynamických titulkoch.

    Posun riadkov = predchádzajúci druhý riadok sa stal prvým riadkom.
    Toto je normálne správanie dynamických titulkov, nie statických.
    """
    if not config.stability_previous_second_line or not current_first:
        return False

    # Kontrola, či sa predchádzajúci druhý riadok stal prvým
    similarity = calculate_text_similarity(config.stability_previous_second_line, current_first)

    if similarity >= 0.8:  # Vysoká podobnosť = posun riadku
        logging.info(f"[STABILITY_DETECT] Detekovaný posun riadku: '{config.stability_previous_second_line}' → '{current_first}' (podobnosť: {similarity:.3f})")
        return True

    return False



def switch_to_static_mode():
    """Prepne z full automatic mode na statický režim."""
    try:
        # Zastavenie dynamického režimu
        import dynamic_mode.dynamic_logic
        dynamic_mode.dynamic_logic.stop_dynamic_mode_processing()

        # Prepnutie na statický režim
        config.reading_mode = "static"

        # Spustenie statického režimu
        import static_mode.static_logic
        static_mode.static_logic.start_static_mode_processing()

        # TTS oznámenie
        from tts_manager import speak_text
        from i18n_manager import get_tts_message
        tts_msg = get_tts_message("tts_switched_to_static")
        speak_text(tts_msg)

        logging.info("[STABILITY_DETECT] ✅ Úspešne prepnuté na statický režim")

    except Exception as e:
        logging.error(f"[STABILITY_DETECT] Chyba pri prepínaní na statický režim: {e}")


def log_stability_status():
    """Vypíše aktuálny stav detekcie stability na konzolu."""
    logging.debug(f"[STABILITY_DETECT] "
                 f"1.riadok: {config.current_first_line_stability:.2f} ({config.first_line_stability_count} cyklov) | "
                 f"2.riadok: {config.current_second_line_stability:.2f} ({config.second_line_stability_count} cyklov) | "
                 f"Rast: {config.second_line_growth_detected} ({config.second_line_growth_count} cyklov) | "
                 f"Fáza: {config.detection_phase} | "
                 f"Rozhodnutie: {config.mode_decision}")


def get_stability_status_for_gui():
    """
    Vráti aktuálny stav detekcie pre zobrazenie v GUI.
    Používa sa pre live monitoring v záložke detekcie.
    """
    return {
        'first_line_stability': f"{config.current_first_line_stability:.1%}",
        'second_line_stability': f"{config.current_second_line_stability:.1%}",
        'first_line_cycles': config.first_line_stability_count,
        'second_line_cycles': config.second_line_stability_count,
        'growth_detected': config.second_line_growth_detected,
        'growth_cycles': config.second_line_growth_count,
        'detection_phase': config.detection_phase,
        'mode_decision': config.mode_decision,
        'lines_detected': config.lines_detected
    }
