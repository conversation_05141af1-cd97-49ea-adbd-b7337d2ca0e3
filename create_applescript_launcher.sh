#!/bin/bash

# 🍎 Vytvorí AppleScript aplikáciu ktorá spustí Python bez terminálu

APP_NAME="SubtitleReader"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
APP_PATH="$SCRIPT_DIR/${APP_NAME}.app"

echo "🍎 Vytváram AppleScript aplikáciu: $APP_NAME.app"

# Vymaž starú aplikáciu ak existuje
if [ -d "$APP_PATH" ]; then
    echo "🗑️ Mažem starú aplikáciu..."
    rm -rf "$APP_PATH"
fi

# Vytvor AppleScript ktorý spustí Python na pozadí
APPLESCRIPT_CODE='
on run
    set scriptPath to (path to me as string)
    set appPath to POSIX path of scriptPath
    set parentDir to do shell script "dirname " & quoted form of appPath

    try
        -- Skús najprv testovanú cestu k Pythonu
        do shell script "cd " & quoted form of parentDir & " && nohup /Library/Frameworks/Python.framework/Versions/3.10/bin/python3 main_qt.py > /dev/null 2>&1 &"
    on error
        try
            -- Ak sa nepodarí, skús python3
            do shell script "cd " & quoted form of parentDir & " && nohup python3 main_qt.py > /dev/null 2>&1 &"
        on error
            try
                -- Posledná možnosť - python
                do shell script "cd " & quoted form of parentDir & " && nohup python main_qt.py > /dev/null 2>&1 &"
            on error
                -- Zobraz chybu ak nič nefunguje
                display dialog "❌ Python sa nenašiel!\n\nNainštaluj Python z https://python.org" buttons {"OK"} default button "OK"
            end try
        end try
    end try

    -- Zobraz notifikáciu o spustení
    display notification "🎬 Subtitle Reader sa spúšťa..." with title "Subtitle Reader"
end run
'

# Vytvor aplikáciu pomocou osacompile
echo "$APPLESCRIPT_CODE" | osacompile -o "$APP_PATH"

if [ -d "$APP_PATH" ]; then
    echo "✅ Aplikácia vytvorená: $APP_PATH"
    echo ""
    echo "🎯 Ako použiť:"
    echo "   Dvojklik na SubtitleReader.app"
    echo "   → Aplikácia sa spustí bez terminálu!"
    echo ""
else
    echo "❌ Chyba pri vytváraní aplikácie"
fi
