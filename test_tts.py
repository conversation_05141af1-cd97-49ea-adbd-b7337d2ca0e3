#!/usr/bin/env python3
"""
Test TTS functionality directly
"""

import sys
import os
import logging
import time

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Setup logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_tts():
    """Test TTS functionality directly"""
    print("🧪 Testing TTS functionality...")
    
    try:
        # Import TTS manager
        from platform_loader import get_tts_function
        import common_config as config
        
        print("📋 Initializing TTS...")
        
        # Initialize TTS worker
        init_tts_worker = get_tts_function('init_tts_worker')
        speak_text = get_tts_function('speak_text')

        init_tts_worker()
        time.sleep(1)  # Wait for initialization

        print("🔊 Testing TTS with simple text...")

        # Test simple TTS
        test_text = "Hello, this is a test of the TTS system."
        speak_text(test_text)

        print("✅ TTS command sent, waiting for completion...")
        time.sleep(5)  # Wait for TTS to complete

        print("🔄 Testing Slovak TTS...")
        slovak_text = "Ahoj, toto je test slovenského TTS systému."
        speak_text(slovak_text)
        
        time.sleep(5)  # Wait for completion
        
        print("✅ Test completed")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_tts()
