#!/bin/bash
echo "=========================================="
echo "  🚀 Setup Translation Environment (Mac M1)"
echo "=========================================="

# 1. Vytvoríme virtuálne prostredie
echo "📦 Vytváram virtuálne prostredie..."
python3 -m venv venv_translation
source venv_translation/bin/activate

# 2. Upgrade pip
echo "⬆️  Aktualizujem pip..."
pip install --upgrade pip setuptools wheel

# 3. Nainštalujeme všetky balíčky
echo "📥 Inštalujem Mac M1 optimalizované balíčky..."
pip install -r requirements_mac_m1.txt

# 4. Otestujeme podporu pre MPS
echo "🧪 Testujem MPS (Metal GPU) podporu..."
python3 - <<EOF
import torch
print(f"PyTorch verzia: {torch.__version__}")
if torch.backends.mps.is_available():
    print("✅ MPS (Metal GPU) je dostupné a pripravené!")
    device = torch.device("mps")
    x = torch.randn(3, 3).to(device)
    print(f"✅ Test tensor na MPS: {x.device}")
else:
    print("⚠️  MPS nie je dostupné, pôjde len CPU.")
    print("💡 Skontrolujte či máte macOS 12.3+ a Apple Silicon Mac")
EOF

# 5. Test základných knižníc
echo "🔧 Testujem základné knižnice..."
python3 - <<EOF
try:
    import transformers
    print(f"✅ Transformers {transformers.__version__}")
except ImportError as e:
    print(f"❌ Transformers: {e}")

try:
    import ctranslate2
    print(f"✅ CTranslate2 {ctranslate2.__version__}")
except ImportError as e:
    print(f"❌ CTranslate2: {e}")

try:
    import requests
    print(f"✅ Requests {requests.__version__}")
except ImportError as e:
    print(f"❌ Requests: {e}")

try:
    from PyQt6 import QtCore
    print(f"✅ PyQt6 {QtCore.PYQT_VERSION_STR}")
except ImportError as e:
    print(f"❌ PyQt6: {e}")
EOF

echo "=========================================="
echo "  ✅ Setup hotový! Aktivuj prostredie:"
echo "     source venv_translation/bin/activate"
echo ""
echo "  🧪 Spusti test prekladačov:"
echo "     python3 test_translate.py"
echo "=========================================="
