<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test OS Detection - Subtitle Reader</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .detection-result {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin: 20px 0;
        }
        .os-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 10px 0;
        }
        .highlight-demo {
            border: 3px solid #28a745;
            background: rgba(40, 167, 69, 0.1);
            padding: 15px;
            border-radius: 10px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h1 class="text-center mb-4">
            <i class="fas fa-desktop"></i> Test detekcie operačného systému
        </h1>
        
        <div class="detection-result text-center">
            <h2 id="detected-os">Detekujem váš systém...</h2>
            <p id="os-details" class="lead"></p>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="os-info">
                    <h4>Technické informácie</h4>
                    <p><strong>User Agent:</strong> <span id="user-agent"></span></p>
                    <p><strong>Platform:</strong> <span id="platform"></span></p>
                    <p><strong>Detekovaný OS:</strong> <span id="os-result"></span></p>
                </div>
            </div>
            <div class="col-md-6">
                <div class="os-info">
                    <h4>Podporované systémy</h4>
                    <ul>
                        <li><i class="fab fa-windows text-primary"></i> Windows 10+</li>
                        <li><i class="fab fa-apple text-success"></i> macOS 10.15+</li>
                        <li><i class="fab fa-linux text-warning"></i> Linux Ubuntu 20.04+</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <h3>Demo zvýraznenia</h3>
                <p>Takto by sa zvýraznili elementy na hlavnej stránke:</p>
                
                <div class="row">
                    <div class="col-md-4">
                        <div class="card platform-card" data-os="windows">
                            <div class="card-body text-center">
                                <i class="fab fa-windows fa-3x text-primary mb-3"></i>
                                <h5>Windows</h5>
                                <p>Windows SAPI TTS</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card platform-card" data-os="macos">
                            <div class="card-body text-center">
                                <i class="fab fa-apple fa-3x text-success mb-3"></i>
                                <h5>macOS</h5>
                                <p>Apple TTS Enhanced</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card platform-card" data-os="linux">
                            <div class="card-body text-center">
                                <i class="fab fa-linux fa-3x text-warning mb-3"></i>
                                <h5>Linux</h5>
                                <p>espeak/pyttsx3 TTS</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="text-center mt-4">
            <a href="index.php" class="btn btn-primary btn-lg">
                <i class="fas fa-arrow-left"></i> Späť na hlavnú stránku
            </a>
        </div>
    </div>
    
    <script>
        // Detekcia operačného systému (rovnaká ako na hlavnej stránke)
        function detectOS() {
            const userAgent = navigator.userAgent.toLowerCase();
            const platform = navigator.platform.toLowerCase();
            
            if (userAgent.includes('iphone') || userAgent.includes('ipad') || userAgent.includes('ipod')) {
                return 'ios';
            } else if (userAgent.includes('android')) {
                return 'android';
            } else if (userAgent.includes('win') || platform.includes('win')) {
                return 'windows';
            } else if (userAgent.includes('mac') || platform.includes('mac')) {
                return 'macos';
            } else if (userAgent.includes('linux') || platform.includes('linux') || userAgent.includes('x11')) {
                return 'linux';
            } else {
                return 'unknown';
            }
        }
        
        const osNames = {
            'windows': 'Windows',
            'macos': 'macOS', 
            'linux': 'Linux',
            'ios': 'iOS',
            'android': 'Android',
            'unknown': 'Neznámy systém'
        };
        
        const osIcons = {
            'windows': 'fab fa-windows',
            'macos': 'fab fa-apple',
            'linux': 'fab fa-linux',
            'ios': 'fab fa-apple',
            'android': 'fab fa-android',
            'unknown': 'fas fa-question'
        };
        
        const osColors = {
            'windows': '#0078d4',
            'macos': '#007aff',
            'linux': '#ff6600',
            'ios': '#007aff',
            'android': '#3ddc84',
            'unknown': '#6c757d'
        };
        
        // Spusti detekciu
        document.addEventListener('DOMContentLoaded', function() {
            const currentOS = detectOS();
            
            // Zobraz výsledky
            document.getElementById('user-agent').textContent = navigator.userAgent;
            document.getElementById('platform').textContent = navigator.platform;
            document.getElementById('os-result').textContent = osNames[currentOS];
            
            // Aktualizuj hlavný výsledok
            const detectedOsElement = document.getElementById('detected-os');
            const osDetailsElement = document.getElementById('os-details');
            
            detectedOsElement.innerHTML = `<i class="${osIcons[currentOS]}"></i> ${osNames[currentOS]}`;
            
            if (currentOS === 'ios' || currentOS === 'android') {
                osDetailsElement.textContent = 'Mobilný systém detekovaný - aplikácia je určená pre desktop';
            } else if (currentOS === 'unknown') {
                osDetailsElement.textContent = 'Nepodarilo sa detekovať váš systém';
            } else {
                osDetailsElement.textContent = 'Váš systém je plne podporovaný!';
            }
            
            // Zvýrazni príslušnú kartu
            const platformCards = document.querySelectorAll('.platform-card');
            platformCards.forEach(card => {
                if (card.dataset.os === currentOS) {
                    card.classList.add('highlight-demo');
                    card.style.borderColor = osColors[currentOS];
                    
                    // Pridaj indikátor
                    const indicator = document.createElement('div');
                    indicator.style.cssText = `
                        position: absolute;
                        top: -10px;
                        right: -10px;
                        background: ${osColors[currentOS]};
                        color: white;
                        border-radius: 50%;
                        width: 30px;
                        height: 30px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-size: 12px;
                        font-weight: bold;
                        box-shadow: 0 2px 10px rgba(0,0,0,0.3);
                    `;
                    indicator.innerHTML = '✓';
                    indicator.title = 'Váš operačný systém';
                    card.style.position = 'relative';
                    card.appendChild(indicator);
                }
            });
        });
    </script>
</body>
</html>
