import datetime
import logging
import threading
import queue
from typing import List, Optional

import common_config as config
import ocr_core
import common_utils
from tts_manager import speak_text

def perform_ocr_and_enqueue():
    """
    Beží v samostatnom vlákne a vykonáva OCR skenovanie.
    Skenovanie je spustené buď periodickým časovačom, alebo proaktívne
    hneď po dočítaní predchádzajúceho textu (cez ocr_trigger_event).
    """
    while config.is_reading:
        # Čak<PERSON> na signál buď od časovača, alebo od TTS workera
        # Timeout je nastavený na aktuálny interval, aby sa cyklus periodicky opakoval
        triggered = config.ocr_trigger_event.wait(timeout=config.current_ocr_interval)
        
        if not config.is_reading:
            break

        if triggered:
            config.ocr_trigger_event.clear() # Reset eventu
            logging.info("[PERF_TRACE] OCR spustený proaktívne signálom od TTS.")
        else:
            logging.info(f"[PERF_TRACE] OCR spustený časovačom (interval: {config.current_ocr_interval}s).")

        sct_img = ocr_core.capture_screen_region()
        if sct_img:
            cycle_id = ocr_core.get_next_cycle_id()
            processed_img = ocr_core.preprocess_image_for_ocr(sct_img)
            if processed_img:
                recognized_text = ""
                if config.reading_mode == 'static':
                    recognized_text = ocr_core.perform_static_ocr(processed_img)
                elif config.reading_mode == 'dynamic':
                    recognized_text = ocr_core.perform_dynamic_ocr(processed_img)
                elif config.reading_mode == 'automatic':
                    # Pre detekciu použijeme rýchlejšiu dynamickú metódu
                    recognized_text = ocr_core.perform_dynamic_ocr(processed_img)
                elif config.reading_mode == 'full_automatic':
                    # Pre full automatic použijeme dynamickú metódu (rýchlejšia pre kontinuálnu detekciu)
                    recognized_text = ocr_core.perform_dynamic_ocr(processed_img)
                
                cleaned_text = common_utils.clean_text(recognized_text)

                # Uloženie dát do histórie - VŽDY
                ocr_data = config.OCR_Cycle_Data(
                    cycle_id=cycle_id,
                    raw_screenshot=sct_img,
                    processed_image=processed_img,
                    raw_ocr_text=recognized_text,
                    cleaned_ocr_text=cleaned_text,
                    timestamp=datetime.datetime.now(),
                    reading_mode=config.reading_mode
                )
                config.ocr_history.append(ocr_data)

                if recognized_text:
                    logging.info(f"[PERF_TRACE] CycleID={cycle_id}: OCR dokončený, text rozpoznaný.")
                    config.raw_ocr_queue.put({'text': recognized_text, 'cycle_id': cycle_id})
                else:
                    logging.debug(f"[PERF_TRACE] CycleID={cycle_id}: OCR dokončený, žiadny text nerozpoznaný.")
                    # Heartbeat: pošli prázdny text, aby sa okamžite spracoval zánik titulkov
                    if config.reading_mode == 'dynamic':
                        config.raw_ocr_queue.put({'text': '', 'cycle_id': cycle_id})
                        logging.debug(f"[PERF_TRACE] CycleID={cycle_id}: Enqueued heartbeat (empty text) for dynamic mode.")
                    elif config.reading_mode == 'full_automatic':
                        config.raw_ocr_queue.put({'text': '', 'cycle_id': cycle_id})
                        logging.debug(f"[PERF_TRACE] CycleID={cycle_id}: Enqueued heartbeat (empty text) for full_automatic mode.")

def process_ocr_text_youtube(current_video_time: float):
    """
    Spracováva YouTube titulky na základe aktuálneho času videa.
    """
    if not config.subtitles:
        return

    # Aktualizácia last_subtitle_index na základe aktuálneho času videa
    # Toto je dôležité pre správne prechádzanie titulkami
    new_index = config.last_subtitle_index
    while new_index < len(config.subtitles) and config.subtitles[new_index].end_time < current_video_time:
        new_index += 1
    config.last_subtitle_index = new_index

    # Prechádzanie titulkami od aktuálneho indexu
    for i in range(config.last_subtitle_index, len(config.subtitles)):
        sub = config.subtitles[i]
        if sub.start_time <= current_video_time < sub.end_time:
            text_to_read = sub.text
            # Porovnanie s posledným enqueued textom, aby sa predišlo duplicitám
            if common_utils.calculate_similarity(config.last_enqueued_youtube_text, text_to_read) < 0.95:
                config.youtube_tts_queue.put(text_to_read)
                config.last_enqueued_youtube_text = text_to_read
            config.last_subtitle_index = i + 1 # Posun na ďalší titulok
        elif current_video_time < sub.start_time:
            # Ak je aktuálny čas pred začiatkom titulku, zastavíme, pretože ďalšie titulky sú ešte ďalej
            break
