#!/bin/bash
# Universal startup script for Subtitle Reader (macOS/Linux)

echo "🚀 Starting Subtitle Reader..."
echo "Platform: $(uname -s)"

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed or not in PATH"
    exit 1
fi

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "📦 Creating virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source venv/bin/activate

# Install/update dependencies
echo "📥 Installing dependencies..."
pip install -r requirements.txt

# Run the application
echo "▶️ Starting application..."
python3 main_qt.py

echo "✅ Application finished."
