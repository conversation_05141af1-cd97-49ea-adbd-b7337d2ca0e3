#!/usr/bin/env python3
"""
Test len GUI časti NLLB integrácie
"""

import sys
import os
import logging

# Nastavenie loggingu
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# Pridanie cesty pre importy
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from PyQt6 import QtWidgets, QtCore
    import qt_gui
    
    print("🚀 Test NLLB GUI komponenty")
    print("=" * 50)
    
    # Vytvorenie aplikácie
    app = QtWidgets.QApplication(sys.argv)
    
    # Vytvorenie hlavného okna
    print("🔍 Vytváram hlavné okno...")
    
    # Mock callbacks
    def mock_closing_callback():
        print("Mock closing callback zavolaný")

    def mock_toggle_reading_callback():
        print("Mock toggle reading callback zavolaný")

    def mock_set_reading_mode_callback(mode):
        print(f"Mock set reading mode callback zavolaný: {mode}")

    # Vytvorenie GUI
    gui = qt_gui.SubtitleReaderQtGUI(
        mock_closing_callback,
        mock_toggle_reading_callback,
        mock_set_reading_mode_callback
    )
    
    print("✅ GUI úspešne vytvorené")
    print("✅ NLLB záložka je dostupná")
    
    # Zobrazenie okna
    gui.show()
    
    print("🎉 GUI test úspešný!")
    print("Zatvorte okno pre ukončenie testu...")
    
    # Spustenie aplikácie
    sys.exit(app.exec())
    
except Exception as e:
    print(f"❌ Chyba pri teste GUI: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
