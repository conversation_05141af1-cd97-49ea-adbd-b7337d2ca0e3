#!/usr/bin/env python3
"""
🧪 Test opráv pre zabránenie opakovaniu tokenov v preklade
"""

import logging
import time

# Nastavenie logovania
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_speed_mode_anti_repetition():
    """Test Speed režimu s anti-repetition nastaveniami."""
    print("🧪 Test 1: Speed režim - anti-repetition")
    print("=" * 60)
    
    try:
        import translation_manager
        
        # Nastavenie Speed režimu
        print("🔄 Prepínam na Speed režim...")
        success = translation_manager.set_translator_mode("speed")
        if not success:
            print("❌ Nepodarilo sa nastaviť Speed režim")
            return False
        
        # Nastavenie Speed výkonnostného režimu
        print("⚡ Nastavujem Speed výkonnostný režim...")
        translation_manager.set_performance_mode("speed")
        
        # Kontrola parametrov
        params = translation_manager.current_translator_params
        print(f"📊 Aktuálne parametre:")
        print(f"   beam_size: {params.get('beam_size')}")
        print(f"   max_length: {params.get('max_length')}")
        print(f"   no_repeat_ngram_size: {params.get('no_repeat_ngram_size')}")
        print(f"   repetition_penalty: {params.get('repetition_penalty')}")
        
        # Kontrola či sú anti-repetition parametre správne nastavené
        if params.get('no_repeat_ngram_size', 0) == 0:
            print("❌ PROBLÉM: no_repeat_ngram_size je 0 - môže spôsobiť opakovanie!")
            return False
        
        if params.get('repetition_penalty', 1.0) == 1.0:
            print("⚠️  UPOZORNENIE: repetition_penalty je 1.0 - žiadna penalizácia opakovaní")
        
        print("✅ Anti-repetition parametre sú správne nastavené")
        
        # Inicializácia prekladača
        print("\n🚀 Inicializujem prekladač...")
        init_success = translation_manager.init_translator()
        if not init_success:
            print("❌ Inicializácia prekladača zlyhala")
            return False
        
        print("✅ Prekladač inicializovaný")
        
        # Test problematických textov ktoré spôsobovali opakovanie
        test_texts = [
            "You know... follow the money.",
            "Hello world",
            "How are you?",
            "This is a test",
            "Good morning"
        ]
        
        print(f"\n📝 Test prekladov (očakávame žiadne opakovanie):")
        
        all_good = True
        for text in test_texts:
            start_time = time.time()
            result = translation_manager.translate_text(text)
            duration = (time.time() - start_time) * 1000
            
            # Kontrola opakovania
            words = result.split()
            has_repetition = False
            
            # Kontrola 2-gramov (dvojíc slov)
            for i in range(len(words) - 1):
                if words[i] == words[i + 1]:
                    has_repetition = True
                    break
            
            # Kontrola 3+ opakovaní rovnakého slova
            for word in set(words):
                if words.count(word) >= 3:
                    has_repetition = True
                    break
            
            status = "❌ OPAKOVANIE!" if has_repetition else "✅ OK"
            print(f"🇬🇧 '{text}' → 🇨🇿 '{result}' ({duration:.1f}ms) {status}")
            
            if has_repetition:
                all_good = False
        
        return all_good
        
    except Exception as e:
        print(f"❌ Chyba pri teste: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_all_modes_anti_repetition():
    """Test všetkých režimov pre anti-repetition."""
    print("\n🧪 Test 2: Všetky režimy - anti-repetition")
    print("=" * 60)
    
    try:
        import translation_manager
        
        modes = ["speed", "balanced", "quality"]
        test_text = "You know... follow the money."
        
        results = {}
        
        for mode in modes:
            print(f"\n🔄 Testuje {mode.upper()} režim...")
            
            # Nastavenie režimu
            success = translation_manager.set_translator_mode(mode)
            if not success:
                print(f"❌ Nepodarilo sa nastaviť {mode} režim")
                continue
            
            # Nastavenie výkonnostného režimu
            translation_manager.set_performance_mode(mode)
            
            # Kontrola parametrov
            params = translation_manager.current_translator_params
            no_repeat = params.get('no_repeat_ngram_size', 0)
            rep_penalty = params.get('repetition_penalty', 1.0)
            
            print(f"   no_repeat_ngram_size: {no_repeat}")
            print(f"   repetition_penalty: {rep_penalty}")
            
            # Inicializácia
            init_success = translation_manager.init_translator()
            if not init_success:
                print(f"❌ Inicializácia {mode} zlyhala")
                continue
            
            # Test prekladu
            start_time = time.time()
            result = translation_manager.translate_text(test_text)
            duration = (time.time() - start_time) * 1000
            
            # Kontrola opakovania
            words = result.split()
            has_repetition = any(words[i] == words[i + 1] for i in range(len(words) - 1))
            
            status = "❌ OPAKOVANIE!" if has_repetition else "✅ OK"
            print(f"   Výsledok: '{result}' ({duration:.1f}ms) {status}")
            
            results[mode] = {
                'result': result,
                'has_repetition': has_repetition,
                'duration': duration,
                'no_repeat': no_repeat,
                'rep_penalty': rep_penalty
            }
        
        # Súhrn
        print(f"\n📊 Súhrn testov:")
        print("=" * 40)
        
        all_good = True
        for mode, data in results.items():
            status = "❌ ZLYHANIE" if data['has_repetition'] else "✅ ÚSPECH"
            print(f"{mode.upper():>8}: {status} | no_repeat={data['no_repeat']}, rep_penalty={data['rep_penalty']}")
            if data['has_repetition']:
                all_good = False
        
        return all_good
        
    except Exception as e:
        print(f"❌ Chyba pri teste: {e}")
        return False

def test_performance_mode_parameters():
    """Test parametrov výkonnostných režimov."""
    print("\n🧪 Test 3: Parametre výkonnostných režimov")
    print("=" * 60)
    
    try:
        import translation_manager
        
        # Test Speed režimu
        print("⚡ Speed režim:")
        translation_manager.set_performance_mode("speed")
        params = translation_manager.current_translator_params
        
        expected_speed = {
            'beam_size': 1,
            'max_length': 32,
            'no_repeat_ngram_size': 2,  # Musí byť > 0
            'repetition_penalty': 1.2   # Musí byť > 1.0
        }
        
        speed_ok = True
        for key, expected in expected_speed.items():
            actual = params.get(key)
            status = "✅" if actual == expected else "❌"
            print(f"   {key}: {actual} (očakávané: {expected}) {status}")
            if actual != expected:
                speed_ok = False
        
        # Test Balanced režimu
        print("\n⚖️ Balanced režim:")
        translation_manager.set_performance_mode("balanced")
        params = translation_manager.current_translator_params
        
        expected_balanced = {
            'beam_size': 4,
            'max_length': 32,
            'no_repeat_ngram_size': 3,  # Musí byť > 0
            'repetition_penalty': 1.1   # Musí byť > 1.0
        }
        
        balanced_ok = True
        for key, expected in expected_balanced.items():
            actual = params.get(key)
            status = "✅" if actual == expected else "❌"
            print(f"   {key}: {actual} (očakávané: {expected}) {status}")
            if actual != expected:
                balanced_ok = False
        
        # Test Quality režimu
        print("\n🏆 Quality režim:")
        translation_manager.set_performance_mode("quality")
        params = translation_manager.current_translator_params
        
        expected_quality = {
            'beam_size': 5,
            'max_length': 32,
            'no_repeat_ngram_size': 3,  # Musí byť > 0
            'repetition_penalty': 1.2   # Musí byť > 1.0
        }
        
        quality_ok = True
        for key, expected in expected_quality.items():
            actual = params.get(key)
            status = "✅" if actual == expected else "❌"
            print(f"   {key}: {actual} (očakávané: {expected}) {status}")
            if actual != expected:
                quality_ok = False
        
        return speed_ok and balanced_ok and quality_ok
        
    except Exception as e:
        print(f"❌ Chyba pri teste: {e}")
        return False

def main():
    """Hlavná test funkcia."""
    print("🚀 Test opráv pre zabránenie opakovaniu tokenov")
    print("=" * 80)
    
    # Test 1: Speed režim anti-repetition
    success1 = test_speed_mode_anti_repetition()
    
    # Test 2: Všetky režimy anti-repetition
    success2 = test_all_modes_anti_repetition()
    
    # Test 3: Parametre výkonnostných režimov
    success3 = test_performance_mode_parameters()
    
    # Súhrn
    print(f"\n🎉 Súhrn testov:")
    print("=" * 40)
    print(f"✅ Speed režim anti-repetition: {'ÚSPECH' if success1 else 'ZLYHANIE'}")
    print(f"✅ Všetky režimy anti-repetition: {'ÚSPECH' if success2 else 'ZLYHANIE'}")
    print(f"✅ Parametre výkonnostných režimov: {'ÚSPECH' if success3 else 'ZLYHANIE'}")
    
    if all([success1, success2, success3]):
        print(f"\n🎉 Všetky testy úspešné! Opakovanie tokenov je opravené.")
        print(f"\n📝 Kľúčové opravy:")
        print(f"1. Speed režim: no_repeat_ngram_size 0→2, repetition_penalty 1.0→1.2")
        print(f"2. MarianMT: používa current_translator_params namiesto globálnych premenných")
        print(f"3. Všetky režimy majú anti-repetition ochranu")
        print(f"4. 32 tokenov pre všetky módy")
    else:
        print(f"\n❌ Niektoré testy zlyhali. Skontrolujte logy pre viac informácií.")

if __name__ == "__main__":
    main()
