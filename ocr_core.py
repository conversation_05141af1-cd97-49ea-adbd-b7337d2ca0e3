import logging
import threading
import time
from pathlib import Path
import platform
# import AppKit # Removed for cross-platform compatibility
from PIL import Image, ImageEnhance, ImageOps, ImageChops
import mss
import pytesseract

import common_config as config
import subtitle_area_detector

# Globálny počítadlo pre cycle_id
_cycle_id_counter = 0
_cycle_id_counter_lock = threading.Lock()

def get_next_cycle_id():
    global _cycle_id_counter
    with _cycle_id_counter_lock:
        _cycle_id_counter += 1
        return _cycle_id_counter

# --- Funkcie pre OCR a spracovanie textu ---

def get_active_app_name() -> str:
    """Získa názov aktívnej aplikácie cross-platform."""
    try:
        # Try cross-platform implementation first
        from platform_utils import get_active_app_detector
        detector = get_active_app_detector()
        app_name = detector.get_active_app_name()
        if app_name:
            return app_name
    except ImportError as e:
        logging.warning(f"⚠️ Cross-platform active app detection not available: {e}")
    except Exception as e:
        logging.debug(f"⚠️ Cross-platform active app detection failed: {e}")

    # Fallback to legacy macOS implementation
    if platform.system() == "Darwin":
        try:
            active_app = AppKit.NSWorkspace.sharedWorkspace().frontmostApplication()
            if active_app:
                app_name = active_app.localizedName()
                return app_name if app_name else ""
        except Exception as e:
            logging.error(f"CHYBA [Active App]: Nepodarilo sa získať názov aktívnej aplikácie: {e}")

    return ""

def capture_screen_region(monitor_number=1, top_offset_factor=0.67, height_factor=0.33):
    try:
        with mss.mss() as sct:
            monitors = sct.monitors
            if not monitors:
                logging.error("CHYBA: Nenašli sa žiadne monitory.")
                return None
            
            if monitor_number < 0 or monitor_number >= len(monitors):
                logging.warning(f"Upozornenie: Monitor číslo {monitor_number} neexistuje. Používam primárny monitor (0).")
                monitor = monitors[0] # Použijeme primárny monitor
            else:
                monitor = monitors[monitor_number]

            capture_height = int(monitor["height"] * height_factor)
            capture_top = monitor["top"] + int(monitor["height"] * top_offset_factor)
            region = {
                "top": capture_top, "left": monitor["left"],
                "width": monitor["width"], "height": capture_height,
                "mon": monitor_number
            }
            return sct.grab(region)
    except Exception as e:
        logging.error(f"CHYBA: Chyba pri snímaní obrazovky: {e}")
        return None

def preprocess_image_for_ocr(sct_img, color_threshold_factor=None, final_contrast_factor=2.0):
    """
    Spracuje obrázok optimalizovane:
    1. Načíta farebný obrázok.
    2. Aplikuje prah na farebný obrázok pomocou operácií na kanáloch: 
       "biele" pixely (R,G,B nad prahom) ostanú biele, ostatné čierne.
    3. Prevedie na skutočný čiernobiely (grayscale 'L').
    4. Zvýši kontrast.
    5. Invertuje farby (čierne písmo na bielom pozadí pre Tesseract).
    """
    if color_threshold_factor is None:
        color_threshold_factor = config.OCR_THRESHOLD

    if not sct_img:
        return None
    try:
        img_original_color = Image.frombytes("RGB", sct_img.size, sct_img.rgb)
        channel_thresh_abs = int(color_threshold_factor * 255)
        r_band, g_band, b_band = img_original_color.split()
        r_thresholded = r_band.point(lambda p: 255 if p >= channel_thresh_abs else 0)
        g_thresholded = g_band.point(lambda p: 255 if p >= channel_thresh_abs else 0)
        b_thresholded = b_band.point(lambda p: 255 if p >= channel_thresh_abs else 0)
        temp_mask = ImageChops.darker(r_thresholded, g_thresholded)
        img_binary_white_text_on_black = ImageChops.darker(temp_mask, b_thresholded)
        enhancer = ImageEnhance.Contrast(img_binary_white_text_on_black)
        img_bw_contrasted = enhancer.enhance(final_contrast_factor)
        img_inverted_for_tesseract = ImageOps.invert(img_bw_contrasted)

        if config.SAVE_IMAGES:
            try:
                timestamp = time.strftime("%Y%m%d_%H%M%S_%f")
                base_path = config.IMAGE_SAVE_DIR / f"ocr_image_optimized_{timestamp}"
                img_inverted_for_tesseract.save(f"{base_path}_4_inverted_Btext_TESSERACT.png")
                logging.info(f"Optimalizované obrázky (končiace na _4_inverted...) uložené do: {config.IMAGE_SAVE_DIR}")
            except Exception as save_err:
                logging.error(f"CHYBA: Nepodarilo sa uložiť obrázok: {save_err}")

        return img_inverted_for_tesseract

    except Exception as e:
        logging.error(f"CHYBA: Neočakávaná chyba v optimalizovanej preprocess_image_for_ocr: {e}")
        return None

def perform_ocr_with_psm(processed_pillow_image, psm_mode, lang=None):
    """Vykoná OCR s konkrétnym PSM režimom."""
    if lang is None:
        lang = config.OCR_LANGUAGE
    if not processed_pillow_image:
        return None

    try:
        custom_config = f'--oem 3 --psm {psm_mode}'
        if config.TESSDATA_PREFIX:
            custom_config += f' --tessdata-dir "{config.TESSDATA_PREFIX}"'

        ocr_data = pytesseract.image_to_data(processed_pillow_image, lang=lang, config=custom_config, output_type=pytesseract.Output.DICT)
        image_width = processed_pillow_image.width
        screen_center_x = image_width / 2

        # Fáza 1: Zoskupenie VŠETKÝCH slov do riadkov (bez ohľadu na istotu)
        all_lines = {}
        for i in range(len(ocr_data['text'])):
            text = ocr_data['text'][i].strip()
            if text:
                line_num = ocr_data['line_num'][i]
                if line_num not in all_lines:
                    all_lines[line_num] = []
                all_lines[line_num].append({
                    'text': text,
                    'left': ocr_data['left'][i],
                    'width': ocr_data['width'][i],
                    'conf': int(ocr_data['conf'][i])
                })

        if not all_lines:
            return ""

        # Fáza 2: Analýza riadkov s vysokou istotou na nájdenie "kotvy"
        best_anchor_line = None
        max_score = -1

        for line_num, words in all_lines.items():
            # Pre účely nájdenia kotvy pracujeme len so slovami s vysokou istotou
            high_conf_words = [w for w in words if w['conf'] > 25]  # Znížený threshold pre krátke texty

            if len(high_conf_words) < config.MIN_WORDS_PER_LINE_FOR_ANCHOR:
                continue

            line_start_x = min(w['left'] for w in high_conf_words)
            line_end_x = max(w['left'] + w['width'] for w in high_conf_words)
            line_center_x = line_start_x + (line_end_x - line_start_x) / 2

            deviation = abs(line_center_x - screen_center_x)
            deviation_percent = deviation / image_width

            if deviation_percent <= config.MAX_DEVIATION_PERCENT_FOR_ANCHOR:
                score = len(high_conf_words)
                if score > max_score:
                    max_score = score
                    best_anchor_line = line_num

        # Fáza 3: Rekonštrukcia textu z kotvy a susedov (už bez filtra istoty)
        if best_anchor_line is not None:
            final_lines = {}

            # Pridáme kotviaci riadok (všetky slová)
            anchor_text = " ".join(w['text'] for w in all_lines[best_anchor_line])
            final_lines[best_anchor_line] = anchor_text

            # Skontrolujeme riadok PRED kotvou
            prev_line_num = best_anchor_line - 1
            if prev_line_num in all_lines:
                prev_line_text = " ".join(w['text'] for w in all_lines[prev_line_num])
                final_lines[prev_line_num] = prev_line_text

            # Skontrolujeme riadok ZA kotvou
            next_line_num = best_anchor_line + 1
            if next_line_num in all_lines:
                next_line_text = " ".join(w['text'] for w in all_lines[next_line_num])
                final_lines[next_line_num] = next_line_text

            # Zoradíme riadky podľa ich čísla a spojíme ich
            recognized_text = "\n".join(final_lines[key] for key in sorted(final_lines.keys()))
            return recognized_text

        return "" # Nenájdená žiadna vhodná kotva

    except Exception as e:
        logging.debug(f"OCR s PSM {psm_mode} zlyhalo: {e}")
        return None

def analyze_static_text_layout(processed_pillow_image, lang=None):
    """
    Performs an initial OCR pass to analyze text layout for static mode,
    determine if it's horizontally centered, and estimate line count.
    Returns (is_centered: bool, estimated_psm: int, recognized_text: str).
    """
    if lang is None:
        lang = config.OCR_LANGUAGE
    if not processed_pillow_image:
        return False, None, ""

    # Use a general PSM for initial layout analysis (e.g., PSM 6 for block)
    # We need line_num and word_num for layout analysis
    custom_config = f'--oem 3 --psm 6'
    if config.TESSDATA_PREFIX:
        custom_config += f' --tessdata-dir "{config.TESSDATA_PREFIX}"'

    try:
        ocr_data = pytesseract.image_to_data(processed_pillow_image, lang=lang, config=custom_config, output_type=pytesseract.Output.DICT)
        image_width = processed_pillow_image.width
        screen_center_x = image_width / 2

        lines_info = {}
        all_text_words = []
        for i in range(len(ocr_data['text'])):
            text = ocr_data['text'][i].strip()
            if text:
                line_num = ocr_data['line_num'][i]
                left = ocr_data['left'][i]
                width = ocr_data['width'][i]
                conf = int(ocr_data['conf'][i])

                if line_num not in lines_info:
                    lines_info[line_num] = {'words': [], 'min_x': float('inf'), 'max_x': float('-inf')}
                
                lines_info[line_num]['words'].append({'text': text, 'left': left, 'width': width, 'conf': conf})
                lines_info[line_num]['min_x'] = min(lines_info[line_num]['min_x'], left)
                lines_info[line_num]['max_x'] = max(lines_info[line_num]['max_x'], left + width)
                all_text_words.append(text)

        if not lines_info:
            return False, None, ""

        # Check centering of the overall text block
        min_overall_x = min(line_data['min_x'] for line_data in lines_info.values())
        max_overall_x = max(line_data['max_x'] for line_data in lines_info.values())
        overall_center_x = min_overall_x + (max_overall_x - min_overall_x) / 2
        
        deviation = abs(overall_center_x - screen_center_x)
        deviation_percent = deviation / image_width

        is_centered = deviation_percent <= config.MAX_DEVIATION_PERCENT_FOR_ANCHOR

        # Estimate PSM for the second pass based on line count
        estimated_psm = None
        if len(lines_info) == 1:
            estimated_psm = 7  # Single line
        elif len(lines_info) > 1:
            estimated_psm = 6  # Multiple lines / block
        
        return is_centered, estimated_psm, " ".join(all_text_words)

    except Exception as e:
        logging.error(f"CHYBA: Chyba pri analýze rozloženia textu: {e}")
        return False, None, ""

def perform_static_ocr(processed_pillow_image, lang=None):
    """
    Performs OCR using a two-pass strategy:
    1. Analyze layout to determine centering and estimate line count.
    2. Perform targeted OCR with an appropriate PSM based on the analysis.
    """
    if not processed_pillow_image:
        return ""

    # First pass: Analyze layout
    is_centered, estimated_psm, initial_recognized_text = analyze_static_text_layout(processed_pillow_image, lang)

    if not is_centered:
        logging.debug(f"Text nie je horizontálne v strede (odchýlka > {config.MAX_DEVIATION_PERCENT_FOR_ANCHOR*100}%). OCR preskočené.")
        return "" # Return empty string if not centered

    

    if estimated_psm is None:
        logging.debug("Nepodarilo sa odhadnúť PSM z rozloženia textu. Používam predvolené PSM.")
        # Fallback to original PSM modes if layout analysis failed to estimate
        psm_modes_to_try = [7, 6, 13, 8]
    else:
        # Prioritize the estimated PSM, then fall back to others
        psm_modes_to_try = [estimated_psm] + [p for p in [7, 6, 13, 8] if p != estimated_psm]

    best_result = ""
    best_psm = None

    for psm in psm_modes_to_try:
        logging.debug(f"Skúšam OCR s PSM {psm} (druhý prechod). Vyhodnotený text: '{initial_recognized_text}'")
        result = perform_ocr_with_psm(processed_pillow_image, psm, lang)

        if result and len(result.strip()) > len(best_result.strip()):
            best_result = result
            best_psm = psm
            logging.debug(f"PSM {psm} dal lepší výsledok v druhom prechode: '{result}'")
        
        # If we got a good result with the estimated PSM, we can break early
        if estimated_psm is not None and psm == estimated_psm and best_result:
            break

    if best_result:
        logging.debug(f"OCR Rozpoznaný text (najlepší PSM {best_psm} po dvoch prechodoch): '{best_result}'")

    return best_result

def perform_dynamic_ocr(processed_pillow_image, lang=None):
    """
    Performs a simplified and faster OCR specifically for dynamic mode.
    It directly uses PSM 6, which is optimal for single blocks of text like subtitles.
    """
    if not processed_pillow_image:
        return ""

    if lang is None:
        lang = config.DYNAMIC_OCR_LANGUAGE

    try:
        # Ak je aktívna detekcia ľavej strany dynamických titulkov, spracujeme ju
        if config.dynamic_subtitle_detection_active:
            # Uložíme dočasný obrázok pre detekciu ľavej strany
            import tempfile
            import os
            temp_fd, temp_path = tempfile.mkstemp(suffix=f"_ocr_temp_{get_next_cycle_id()}.png")
            os.close(temp_fd)  # Close the file descriptor, we only need the path
            processed_pillow_image.save(temp_path)
            subtitle_area_detector.process_dynamic_subtitle_sample(temp_path)
            # Vyčistíme dočasný súbor
            try:
                import os
                os.remove(temp_path)
            except:
                pass

        # Ak máme detekovanú ľavú stranu dynamických titulkov a filtrovanie je zapnuté, použijeme OCR s filtrovaním
        if config.DYNAMIC_SUBTITLE_FILTERING_ENABLED and config.dynamic_subtitle_left_x is not None:
            return perform_dynamic_ocr_with_left_filtering(processed_pillow_image, lang)
        else:
            # Štandardný OCR bez filtrovania
            return perform_dynamic_ocr_standard(processed_pillow_image, lang)

    except Exception as e:
        logging.error(f"CHYBA: Chyba v perform_dynamic_ocr: {e}")
        return ""

def perform_dynamic_ocr_standard(processed_pillow_image, lang):
    """Štandardný dynamic OCR bez filtrovania oblasti."""
    try:
        custom_config = f'--oem 3 --psm 11'
        if config.TESSDATA_PREFIX:
            custom_config += f' --tessdata-dir "{config.TESSDATA_PREFIX}"'

        recognized_text = pytesseract.image_to_string(processed_pillow_image, lang=lang, config=custom_config)

        if recognized_text:
            logging.debug(f"DYNAMIC OCR (PSM 6): Rozpoznaný text: '{recognized_text.strip()}'")

        return recognized_text

    except Exception as e:
        logging.error(f"CHYBA: Chyba v perform_dynamic_ocr_standard: {e}")
        return ""

def perform_dynamic_ocr_with_left_filtering(processed_pillow_image, lang):
    """Dynamic OCR s filtrovaním podľa ľavej strany dynamických titulkov."""
    try:
        custom_config = f'--oem 3 --psm 11'
        if config.TESSDATA_PREFIX:
            custom_config += f' --tessdata-dir "{config.TESSDATA_PREFIX}"'

        # Experimentálne: Skúsime najprv detekciu na úrovni riadkov (textlines)
        if hasattr(config, 'USE_TESSERACT_TEXTLINES') and config.USE_TESSERACT_TEXTLINES:
            return perform_dynamic_ocr_with_textlines(processed_pillow_image, lang)

        # Získame pozičné údaje z OCR na úrovni slov
        ocr_data = pytesseract.image_to_data(processed_pillow_image, lang=lang, config=custom_config, output_type=pytesseract.Output.DICT)

        # Vytvoríme textové bloky s pozíciami
        text_blocks = []
        for i in range(len(ocr_data['text'])):
            text = ocr_data['text'][i].strip()
            conf = int(ocr_data['conf'][i])

            # Nižšia dôvera pre krátke slová, vyššia pre dlhé
            min_confidence = 10 if len(text) <= 2 else 20
            if text and conf > min_confidence:
                text_blocks.append({
                    'text': text,
                    'x': ocr_data['left'][i],
                    'y': ocr_data['top'][i],
                    'width': ocr_data['width'][i],
                    'height': ocr_data['height'][i],
                    'confidence': conf
                })

        # Filtrujeme bloky podľa ľavej strany dynamických titulkov
        filtered_blocks = subtitle_area_detector.is_text_left_of_dynamic_subtitles(text_blocks)

        # Zostavíme text z filtrovaných blokov
        if filtered_blocks:
            # Najprv zoskupíme bloky do riadkov podľa Y-pozície s väčšou toleranciou
            lines_dict = {}  # {y_group: [blocks]}
            Y_TOLERANCE = 25  # Väčšia tolerancia pre zoskupenie do riadkov

            for block in filtered_blocks:
                block_y = block['y']

                # Nájdeme existujúci riadok alebo vytvoríme nový
                found_line = False
                for line_y in lines_dict.keys():
                    if abs(block_y - line_y) <= Y_TOLERANCE:
                        lines_dict[line_y].append(block)
                        found_line = True
                        break

                if not found_line:
                    lines_dict[block_y] = [block]

            # Zoradíme riadky zhora nadol a v rámci riadkov zľava doprava
            lines = []
            for line_y in sorted(lines_dict.keys()):
                line_blocks = lines_dict[line_y]
                # Zoradíme slová v riadku zľava doprava
                line_blocks.sort(key=lambda b: b['x'])
                line_text = ' '.join(block['text'] for block in line_blocks)
                lines.append(line_text)

            recognized_text = '\n'.join(lines)

            # Debug logovanie pre analýzu problémov
            logging.debug(f"DYNAMIC OCR (s filtrovaním ľavej strany): Rozpoznaný text: '{recognized_text.strip()}'")
            logging.debug(f"DYNAMIC OCR: Filtrované bloky: {len(filtered_blocks)}/{len(text_blocks)}")

            # Vytvoríme debug string mimo f-string kvôli spätným lomkám
            debug_lines = []
            for y, blocks in lines_dict.items():
                block_texts = [b['text'] for b in blocks]
                debug_lines.append(f"Y={y}: {block_texts}")
            logging.debug(f"DYNAMIC OCR: Riadky: {len(lines_dict)}, Obsah riadkov: {debug_lines}")

            return recognized_text
        else:
            logging.debug("DYNAMIC OCR: Žiadny text v oblasti dynamických titulkov")
            return ""

    except Exception as e:
        logging.error(f"CHYBA: Chyba v perform_dynamic_ocr_with_left_filtering: {e}")
        # Fallback na štandardný OCR
        return perform_dynamic_ocr_standard(processed_pillow_image, lang)

def perform_dynamic_ocr_with_textlines(processed_pillow_image, lang):
    """
    Experimentálna funkcia pre OCR s detekciou celých textových riadkov.
    Používa Tesseract na úrovni textlines namiesto jednotlivých slov.
    """
    try:
        custom_config = f'--oem 3 --psm 6'
        if config.TESSDATA_PREFIX:
            custom_config += f' --tessdata-dir "{config.TESSDATA_PREFIX}"'

        # Získame údaje na úrovni textových riadkov (level 4 = textlines)
        ocr_data = pytesseract.image_to_data(processed_pillow_image, lang=lang, config=custom_config, output_type=pytesseract.Output.DICT)

        # Filtrujeme len textové riadky (level 4)
        textlines = []
        all_levels_debug = []

        for i in range(len(ocr_data['text'])):
            level = int(ocr_data['level'][i])
            text = ocr_data['text'][i].strip()
            conf = int(ocr_data['conf'][i])

            # Debug informácie o všetkých leveloch
            if text:
                all_levels_debug.append(f"L{level}:'{text}'({conf})")

            if level == 4 and text and conf > 10:  # Level 4 = textline, nižšia dôvera
                textlines.append({
                    'text': text,
                    'x': ocr_data['left'][i],
                    'y': ocr_data['top'][i],
                    'width': ocr_data['width'][i],
                    'height': ocr_data['height'][i],
                    'confidence': conf
                })

        # Debug logovanie
        logging.debug(f"DYNAMIC OCR (textlines): Všetky detekcie: {all_levels_debug[:10]}...")  # Prvých 10

        if not textlines:
            logging.debug("DYNAMIC OCR (textlines): Žiadne textové riadky detekované")
            return ""

        # Filtrujeme textové riadky podľa ľavej strany
        if config.dynamic_subtitle_left_x is not None:
            filtered_textlines = []
            LEFT_TOLERANCE = config.LEFT_TOLERANCE  # Nastaviteľné cez GUI
            RIGHT_TOLERANCE = config.RIGHT_TOLERANCE  # Nastaviteľné cez GUI

            for line in textlines:
                distance_from_left = line['x'] - config.dynamic_subtitle_left_x

                if -LEFT_TOLERANCE <= distance_from_left <= RIGHT_TOLERANCE:
                    filtered_textlines.append(line)
                    logging.debug(f"[DYNAMIC_TEXTLINE] Riadok je TITULKOVÝ: X={line['x']} (offset={distance_from_left:+d}px), text: '{line['text']}'")
                else:
                    logging.debug(f"[DYNAMIC_TEXTLINE] Riadok je MIMO titulkov: X={line['x']} (offset={distance_from_left:+d}px), text: '{line['text']}'")

            textlines = filtered_textlines

        # Zoradíme riadky zhora nadol
        textlines.sort(key=lambda line: line['y'])

        # Spojíme texty riadkov
        recognized_text = '\n'.join(line['text'] for line in textlines)

        logging.debug(f"DYNAMIC OCR (textlines): Rozpoznaný text: '{recognized_text.strip()}'")
        logging.debug(f"DYNAMIC OCR (textlines): Detekované riadky: {len(textlines)}")

        return recognized_text

    except Exception as e:
        logging.error(f"CHYBA: Chyba v perform_dynamic_ocr_with_textlines: {e}")
        # Fallback na štandardný OCR
        return perform_dynamic_ocr_standard(processed_pillow_image, lang)