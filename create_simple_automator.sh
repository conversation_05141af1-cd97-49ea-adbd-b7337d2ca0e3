#!/bin/bash

# 🤖 Jednoduchý script na vytvorenie Automator aplikácie

echo "🤖 Vytváram Automator workflow súbor..."

WORKFLOW_DIR="SubtitleReader.app"
CURRENT_DIR="$(pwd)"

# Vymaž starú aplikáciu ak existuje
if [ -d "$WORKFLOW_DIR" ]; then
    echo "🗑️ Mažem starú aplikáciu..."
    rm -rf "$WORKFLOW_DIR"
fi

# Vytvor štruktúru Automator aplikácie
mkdir -p "$WORKFLOW_DIR/Contents"
mkdir -p "$WORKFLOW_DIR/Contents/document.wflow/Contents"

# Vytvor Info.plist pre Automator aplikáciu
cat > "$WORKFLOW_DIR/Contents/Info.plist" << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>English</string>
	<key>CFBundleExecutable</key>
	<string>Application Stub</string>
	<key>CFBundleIconFile</key>
	<string>AutomatorApplet</string>
	<key>CFBundleIdentifier</key>
	<string>com.apple.automator.SubtitleReader</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>SubtitleReader</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>1.0</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>1.0</string>
	<key>LSMinimumSystemVersion</key>
	<string>10.5</string>
	<key>LSUIElement</key>
	<false/>
	<key>NSAppleScriptEnabled</key>
	<true/>
	<key>NSMainNibFile</key>
	<string>ApplicationStub</string>
	<key>NSPrincipalClass</key>
	<string>NSApplication</string>
</dict>
</plist>
EOF

# Skopíruj Automator stub
cp -R "/System/Library/CoreServices/Automator Launcher.app/Contents/MacOS" "$WORKFLOW_DIR/Contents/"
cp -R "/System/Library/CoreServices/Automator Launcher.app/Contents/Resources" "$WORKFLOW_DIR/Contents/"

# Vytvor workflow dokument
cat > "$WORKFLOW_DIR/Contents/document.wflow/Contents/document.wflow" << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>AMApplicationBuild</key>
	<string>521</string>
	<key>AMApplicationVersion</key>
	<string>2.10</string>
	<key>AMDocumentVersion</key>
	<string>2</string>
	<key>actions</key>
	<array>
		<dict>
			<key>action</key>
			<dict>
				<key>AMAccepts</key>
				<dict>
					<key>Container</key>
					<string>List</string>
					<key>Optional</key>
					<true/>
					<key>Types</key>
					<array>
						<string>com.apple.cocoa.string</string>
					</array>
				</dict>
				<key>AMActionVersion</key>
				<string>2.0.3</string>
				<key>AMApplication</key>
				<array>
					<string>Automator</string>
				</array>
				<key>AMParameterProperties</key>
				<dict>
					<key>COMMAND_STRING</key>
					<dict/>
					<key>CheckedForUserDefaultShell</key>
					<dict/>
					<key>inputMethod</key>
					<dict/>
					<key>shell</key>
					<dict/>
					<key>source</key>
					<dict/>
				</dict>
				<key>AMProvides</key>
				<dict>
					<key>Container</key>
					<string>List</string>
					<key>Types</key>
					<array>
						<string>com.apple.cocoa.string</string>
					</array>
				</dict>
				<key>ActionBundlePath</key>
				<string>/System/Library/Automator/Run Shell Script.action</string>
				<key>ActionName</key>
				<string>Run Shell Script</string>
				<key>ActionParameters</key>
				<dict>
					<key>COMMAND_STRING</key>
					<string># Get the directory where this app is located
APP_DIR="$(dirname "$0")"
cd "$APP_DIR"

# Find Python - tested path
PYTHON_CMD="/Library/Frameworks/Python.framework/Versions/3.10/bin/python3"

# Try alternatives if not found
if [ ! -f "$PYTHON_CMD" ]; then
    if command -v python3 &> /dev/null; then
        PYTHON_CMD="python3"
    elif command -v python &> /dev/null; then
        PYTHON_CMD="python"
    else
        exit 1
    fi
fi

# Check if main file exists
if [ ! -f "main_qt.py" ]; then
    exit 1
fi

# Launch Subtitle Reader
exec $PYTHON_CMD main_qt.py</string>
					<key>CheckedForUserDefaultShell</key>
					<true/>
					<key>inputMethod</key>
					<integer>0</integer>
					<key>shell</key>
					<string>/bin/bash</string>
					<key>source</key>
					<string></string>
				</dict>
				<key>BundleIdentifier</key>
				<string>com.apple.RunShellScript</string>
				<key>CFBundleVersion</key>
				<string>2.0.3</string>
				<key>CanShowSelectedItemsWhenRun</key>
				<false/>
				<key>CanShowWhenRun</key>
				<true/>
				<key>Category</key>
				<array>
					<string>AMCategoryUtilities</string>
				</array>
				<key>Class Name</key>
				<string>RunShellScriptAction</string>
				<key>InputUUID</key>
				<string>A339D0B4-2B0D-4C0F-9F8E-3F3F3F3F3F3F</string>
				<key>Keywords</key>
				<array>
					<string>Shell</string>
					<string>Script</string>
					<string>Command</string>
					<string>Run</string>
					<string>Unix</string>
				</array>
				<key>OutputUUID</key>
				<string>B339D0B4-2B0D-4C0F-9F8E-3F3F3F3F3F3F</string>
				<key>UUID</key>
				<string>C339D0B4-2B0D-4C0F-9F8E-3F3F3F3F3F3F</string>
				<key>UnlocalizedApplications</key>
				<array>
					<string>Automator</string>
				</array>
				<key>arguments</key>
				<dict>
					<key>0</key>
					<dict>
						<key>default value</key>
						<integer>0</integer>
						<key>name</key>
						<string>inputMethod</string>
						<key>required</key>
						<string>0</string>
						<key>type</key>
						<string>0</string>
						<key>uuid</key>
						<string>0</string>
					</dict>
					<key>1</key>
					<dict>
						<key>default value</key>
						<string>/bin/sh</string>
						<key>name</key>
						<string>shell</string>
						<key>required</key>
						<string>0</string>
						<key>type</key>
						<string>0</string>
						<key>uuid</key>
						<string>1</string>
					</dict>
					<key>2</key>
					<dict>
						<key>default value</key>
						<string></string>
						<key>name</key>
						<string>source</string>
						<key>required</key>
						<string>0</string>
						<key>type</key>
						<string>0</string>
						<key>uuid</key>
						<string>2</string>
					</dict>
				</dict>
				<key>isViewVisible</key>
				<true/>
				<key>location</key>
				<string>449.000000:316.000000</string>
				<key>nibPath</key>
				<string>/System/Library/Automator/Run Shell Script.action/Contents/Resources/Base.lproj/main.nib</string>
			</dict>
			<key>isViewVisible</key>
			<true/>
		</dict>
	</array>
	<key>connectors</key>
	<array/>
	<key>workflowMetaData</key>
	<dict>
		<key>workflowTypeIdentifier</key>
		<string>com.apple.Automator.application</string>
	</dict>
</dict>
</plist>
EOF

if [ -d "$WORKFLOW_DIR" ]; then
    echo "✅ Automator aplikácia vytvorená: $WORKFLOW_DIR"
    echo "🚀 Táto aplikácia sa spustí BEZ dialógov!"
    echo "📱 Dvojklik na $WORKFLOW_DIR v Finderi"
else
    echo "❌ Chyba pri vytváraní aplikácie"
fi
