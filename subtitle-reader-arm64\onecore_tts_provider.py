#!/usr/bin/env python3
"""
OneCore TTS Provider for Windows
Provides high-quality OneCore speech synthesis
"""

import asyncio
import logging
import tempfile
import threading
from pathlib import Path
from typing import List, Dict, Any, Optional
import subprocess
import time

class OneCoreVoice:
    """Reprezentácia OneCore hlasu."""
    
    def __init__(self, voice_info):
        self.voice_info = voice_info
        self.id = voice_info.id
        self.display_name = voice_info.display_name
        self.language = voice_info.language
        self.gender = self._parse_gender(voice_info.gender)
        self.quality = 'premium'  # OneCore hlasy sú vždy premium kvalita
    
    def _parse_gender(self, gender_value):
        """Parse gender from WinRT enum."""
        # VoiceGender enum: 0 = Male, 1 = Female
        if gender_value == 0:
            return 'male'
        elif gender_value == 1:
            return 'female'
        else:
            return 'unknown'
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format."""
        return {
            'id': self.id,
            'name': self.display_name,
            'language': self.language,
            'gender': self.gender,
            'quality': self.quality
        }

class OneCoreProvider:
    """OneCore TTS Provider."""

    def __init__(self):
        self.synthesizer = None
        self.voices = []
        self.current_voice = None
        self._audio_thread = None
        self._player_process = None
        self._stop_event = threading.Event()
        self._init_onecore()
    
    def _init_onecore(self):
        """Initialize OneCore TTS."""
        try:
            from winrt.windows.media.speechsynthesis import SpeechSynthesizer
            self.synthesizer = SpeechSynthesizer()
            self._load_voices()
            logging.info("✅ OneCore TTS provider initialized")
        except ImportError as e:
            logging.error(f"❌ OneCore not available: {e}")
            self.synthesizer = None
        except Exception as e:
            logging.error(f"❌ OneCore initialization failed: {e}")
            self.synthesizer = None
    
    def _load_voices(self):
        """Load available OneCore voices."""
        if not self.synthesizer:
            return
        
        try:
            from winrt.windows.media.speechsynthesis import SpeechSynthesizer as SS
            winrt_voices = SS.all_voices
            
            self.voices = []
            for voice_info in winrt_voices:
                voice = OneCoreVoice(voice_info)
                self.voices.append(voice)
                logging.debug(f"🎤 OneCore Voice: {voice.display_name} ({voice.language}) - {voice.gender}")
            
            logging.info(f"🎤 Found {len(self.voices)} OneCore voices")
            
        except Exception as e:
            logging.error(f"❌ Error loading OneCore voices: {e}")
            self.voices = []
    
    def is_available(self) -> bool:
        """Check if OneCore is available."""
        return self.synthesizer is not None and len(self.voices) > 0
    
    def get_available_voices(self) -> List[Dict[str, Any]]:
        """Get list of available OneCore voices."""
        return [voice.to_dict() for voice in self.voices]
    
    def set_voice(self, voice_name: str) -> bool:
        """Set active voice by name."""
        if not self.synthesizer:
            return False

        try:
            # Clean voice name (remove quality indicators)
            clean_name = voice_name.replace(" (Premium)", "").replace(" (Standard)", "").replace(" (Enhanced)", "")

            # Find voice by name
            target_voice = None
            for voice in self.voices:
                if (voice.display_name == voice_name or
                    voice.display_name == clean_name or
                    voice_name in voice.display_name or
                    clean_name in voice.display_name):
                    target_voice = voice
                    break

            if not target_voice:
                logging.warning(f"⚠️ OneCore voice not found: {voice_name} (cleaned: {clean_name})")
                return False
            
            # Set voice in synthesizer
            self.synthesizer.voice = target_voice.voice_info
            self.current_voice = target_voice

            # Debug: Verify voice was set correctly
            current_voice_info = self.synthesizer.voice
            logging.info(f"✅ OneCore voice set: {target_voice.display_name}")
            logging.info(f"🔍 OneCore synthesizer voice: {current_voice_info.display_name} ({current_voice_info.language})")
            return True
            
        except Exception as e:
            logging.error(f"❌ Error setting OneCore voice: {e}")
            return False
    
    def speak(self, text: str, voice_name: Optional[str] = None, rate: int = 200, volume: float = 1.0) -> bool:
        """Speak text using OneCore TTS."""
        if not self.synthesizer:
            logging.error("❌ OneCore synthesizer not available")
            return False

        try:
            # Set voice if specified
            if voice_name and voice_name != (self.current_voice.display_name if self.current_voice else None):
                if not self.set_voice(voice_name):
                    logging.warning(f"⚠️ Could not set voice {voice_name}, using current")

            # Stop any current playback
            self.stop()

            # Start synthesis in background thread
            self._stop_event.clear()
            self._audio_thread = threading.Thread(
                target=self._synthesize_and_play,
                args=(text, rate, volume),
                daemon=True
            )
            self._audio_thread.start()

            return True
            
        except Exception as e:
            logging.error(f"❌ OneCore speak error: {e}")
            return False
    
    def _synthesize_and_play(self, text: str, rate: int, volume: float = 1.0):
        """Synthesize and play audio in background thread."""
        try:
            # Run async synthesis in thread
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            try:
                loop.run_until_complete(self._async_synthesize_and_play(text, rate, volume))
            finally:
                loop.close()

        except Exception as e:
            logging.error(f"❌ OneCore synthesis thread error: {e}")

    async def _async_synthesize_and_play(self, text: str, rate: int, volume: float = 1.0):
        """Async synthesis and playback."""
        try:
            # Create SSML with rate control (OneCore doesn't support volume in prosody)
            rate_multiplier = max(0.5, min(2.0, rate / 200.0))

            # Get current voice language for SSML
            voice_language = "en-US"  # default
            if self.current_voice and hasattr(self.current_voice, 'language'):
                voice_language = self.current_voice.language
            elif self.synthesizer and hasattr(self.synthesizer.voice, 'language'):
                voice_language = self.synthesizer.voice.language

            # Use SSML for rate control with correct language
            ssml = f'''<speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis" xml:lang="{voice_language}">
                <prosody rate="{rate_multiplier}">{text}</prosody>
            </speak>'''

            logging.info(f"🔊 OneCore TTS SSML: rate={rate} (multiplier={rate_multiplier:.2f}), lang={voice_language}")

            # Synthesize SSML to stream
            stream = await self.synthesizer.synthesize_ssml_to_stream_async(ssml)

            if self._stop_event.is_set():
                return

            # Read stream data
            from winrt.windows.storage.streams import DataReader
            reader = DataReader(stream)
            await reader.load_async(stream.size)
            data = bytearray(stream.size)
            reader.read_bytes(data)

            if self._stop_event.is_set():
                return

            # Save to temporary file
            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
                temp_file.write(data)
                temp_path = temp_file.name

            if self._stop_event.is_set():
                Path(temp_path).unlink(missing_ok=True)
                return

            # Play using system player
            self._play_audio_file(temp_path)

            # Cleanup
            Path(temp_path).unlink(missing_ok=True)

        except Exception as e:
            logging.error(f"❌ OneCore async synthesis error: {e}")
    
    def _play_audio_file(self, file_path: str):
        """Play audio file using a controllable process (allow stop)."""
        try:
            # Spustíme PowerShell SoundPlayer ako proces, ktorý vieme ukončiť
            cmd = [
                'powershell', '-NoProfile', '-NonInteractive', '-WindowStyle', 'Hidden', '-c',
                f'$p = New-Object Media.SoundPlayer "{file_path}"; $p.PlaySync()'
            ]
            self._player_process = subprocess.Popen(
                cmd,
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL
            )

            # Čakáme na dokončenie alebo stop
            while self._player_process.poll() is None and not self._stop_event.is_set():
                time.sleep(0.05)

            # Ak bol vyžiadaný stop a proces ešte beží, ukončíme ho
            if self._stop_event.is_set() and self._player_process and self._player_process.poll() is None:
                try:
                    self._player_process.terminate()
                    self._player_process.wait(timeout=0.5)
                except Exception:
                    try:
                        self._player_process.kill()
                    except Exception:
                        pass
        except Exception as e:
            logging.error(f"❌ Audio playback error: {e}")
        finally:
            self._player_process = None
    
    def stop(self) -> bool:
        """Stop current TTS playback."""
        try:
            self._stop_event.set()

            # Pokus o ukončenie procesu prehrávania, ak ešte beží
            try:
                if self._player_process and self._player_process.poll() is None:
                    self._player_process.terminate()
                    try:
                        self._player_process.wait(timeout=0.5)
                    except Exception:
                        self._player_process.kill()
            except Exception:
                pass

            if self._audio_thread and self._audio_thread.is_alive():
                self._audio_thread.join(timeout=1.0)

            return True

        except Exception as e:
            logging.error(f"❌ OneCore stop error: {e}")
            return False

    def is_speaking(self) -> bool:
        """Return True if OneCore is currently playing audio."""
        try:
            if self._audio_thread and self._audio_thread.is_alive():
                return True
            if self._player_process and self._player_process.poll() is None:
                return True
            return False
        except Exception:
            return False
    
    def get_voice_for_language(self, language_code: str) -> Optional[str]:
        """Get best voice for language."""
        # Language mapping
        lang_map = {
            'sk': 'sk-SK',
            'en': 'en-US',
            'cs': 'cs-CZ',
            'de': 'de-DE',
            'fr': 'fr-FR'
        }
        
        target_lang = lang_map.get(language_code, language_code)
        
        # Find voice for language
        for voice in self.voices:
            if voice.language.lower().startswith(target_lang.lower()):
                return voice.display_name
        
        # Fallback to first available voice
        if self.voices:
            return self.voices[0].display_name
        
        return None

# Global instance
_onecore_provider = None

def get_onecore_provider() -> OneCoreProvider:
    """Get global OneCore provider instance."""
    global _onecore_provider
    if _onecore_provider is None:
        _onecore_provider = OneCoreProvider()
    return _onecore_provider
