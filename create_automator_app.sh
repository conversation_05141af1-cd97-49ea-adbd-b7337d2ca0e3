#!/bin/bash

# 🤖 Automatické vytvorenie Automator aplikácie pre Subtitle Reader
# Táto aplikácia sa spustí bez dialógov

echo "🤖 Vytváram Automator aplikáciu automaticky..."

APP_NAME="SubtitleReader"
CURRENT_DIR="$(pwd)"
APP_PATH="$CURRENT_DIR/${APP_NAME}.app"

# Vymaž starú aplikáciu ak existuje
if [ -d "$APP_PATH" ]; then
    echo "🗑️ Mažem starú aplikáciu..."
    rm -rf "$APP_PATH"
fi

# Vytvor shell script ktorý bude súčasťou Automator aplikácie
SHELL_SCRIPT='# Get the directory where this app is located
APP_DIR="$(dirname "$0")"
cd "$APP_DIR"

# Find Python - tested path
PYTHON_CMD="/Library/Frameworks/Python.framework/Versions/3.10/bin/python3"

# Try alternatives if not found
if [ ! -f "$PYTHON_CMD" ]; then
    if command -v python3 &> /dev/null; then
        PYTHON_CMD="python3"
    elif command -v python &> /dev/null; then
        PYTHON_CMD="python"
    else
        exit 1
    fi
fi

# Check if main file exists
if [ ! -f "main_qt.py" ]; then
    exit 1
fi

# Launch Subtitle Reader
exec $PYTHON_CMD main_qt.py'

# Vytvor AppleScript ktorý vytvorí Automator aplikáciu
cat > "/tmp/create_automator.applescript" << EOF
tell application "Automator"
    activate
    set newDoc to make new document with properties {name:"Application"}
    tell newDoc
        set newAction to make new action with properties {name:"Run Shell Script"}
        tell newAction
            set value of setting "shell" to "/bin/bash"
            set value of setting "inputMethod" to 0
            set value of setting "source" to "$SHELL_SCRIPT"
        end tell
    end tell
    save newDoc in POSIX file "$APP_PATH"
    close newDoc
    quit
end tell
EOF

echo "🔄 Spúšťam Automator na vytvorenie aplikácie..."

# Spusti AppleScript
osascript "/tmp/create_automator.applescript"

# Počkaj chvíľu na dokončenie
sleep 3

# Skontroluj či sa aplikácia vytvorila
if [ -d "$APP_PATH" ]; then
    echo "✅ Automator aplikácia vytvorená: $APP_NAME.app"
    echo "🚀 Táto aplikácia sa spustí BEZ dialógov!"
    echo "📱 Dvojklik na $APP_NAME.app v Finderi"
    echo ""
    echo "🎯 Výhody:"
    echo "   ✅ Žiadne dialógy pri spustení"
    echo "   ✅ Natívny macOS vzhľad"
    echo "   ✅ Ikona v Docku"
    echo "   ✅ Správa sa ako normálna aplikácia"
else
    echo "❌ Chyba pri vytváraní aplikácie"
    echo "💡 Skús manuálny postup z AUTOMATOR_NAVOD.md"
fi

# Vyčisti dočasné súbory
rm -f "/tmp/create_automator.applescript"
