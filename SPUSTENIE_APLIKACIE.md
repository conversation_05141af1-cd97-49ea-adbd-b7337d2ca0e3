# 🚀 Spustenie Subtitle Reader aplikácie na macOS

## ✅ Stav aplikácie
Aplikácia je **plne funkč<PERSON>** a otestovaná! Všetky závislosti sú nainštalované a aplikácia sa spúšťa bez problémov.

## 🎯 Spôsoby spustenia

### 1. 🔇 **SpustiTicho** (NAJLEPŠIE - BEZ DIALÓGOV)
**Úplne tichý spúšťač**
- Dvojklik na súbor `SpustiTicho` v Finderi
- **ŽIADNE dialógy ani terminály**
- Spustí sa úplne na pozadí
- Najrýchlejší a najčistejší spôsob

### 2. 🤖 **Automator aplikácia** (ODPORÚČANÉ)
**Vytvor si vlastnú macOS aplikáciu**
- Postupuj podľa `AUTOMATOR_NAVOD.md`
- <PERSON>ytvorí sa `SubtitleReader.app` bez dialógov
- Natívna macOS aplikácia
- Ikona v Docku, správa sa ako normálna aplikácia

### 3. 🚀 **SpustiSubtitleReader.command**
**Jednoduchý spúšťač**
- Dvojklik na súbor v Finderi
- Automaticky zatvorí terminál
- Zobrazí notifikácie o stave
- Kontroluje závislosti

### 4. 🔧 **SpustiApp.command**
**Pokročilý spúšťač s diagnostikou**
- Dvojklik na súbor v Finderi
- Podrobná kontrola závislostí
- Automatická inštalácia chýbajúcich balíkov
- Zatvorí terminál po spustení

### 5. 📱 **SubtitleReader.command**
**Základný spúšťač**
- Dvojklik na súbor v Finderi
- Terminál zostane otvorený
- Vhodné pre debugging

### ⚠️ **SubtitleReader.app** (AppleScript)
**Má problém s dialógmi**
- Zobrazuje dialóg "Ak chcete spustiť tento skript..."
- Nie je odporúčané používať

## 🔧 Technické detaily

### Testovaná konfigurácia:
- **Python**: `/Library/Frameworks/Python.framework/Versions/3.10/bin/python3`
- **Závislosti**: PyQt6, pytesseract, pillow, pynput (všetky nainštalované)
- **Tesseract**: v5.5.0 (nainštalovaný a funkčný)
- **TTS**: macOS `say` command (funguje s hlasmi Laura, Zuzana, atď.)

### Funkcie aplikácie:
- ✅ GUI sa načítava správne
- ✅ OCR rozpoznáva text z obrazovky
- ✅ TTS číta text slovensky
- ✅ Detekuje 163 jazykov pre OCR
- ✅ Detekuje 221 TTS hlasov
- ✅ Hotkeys fungujú (Cmd+Alt+Ctrl+...)

## 🎮 Ovládanie aplikácie

### Klávesové skratky:
- **Cmd+Alt+Ctrl+R**: Zapnúť/vypnúť čítanie
- **Cmd+Alt+Ctrl+S**: Statický režim
- **Cmd+Alt+Ctrl+D**: Dynamický režim
- **Cmd+Alt+Ctrl+A**: Automatický režim

### Režimy čítania:
1. **Statický**: Čítanie na požiadanie
2. **Dynamický**: Kontinuálne čítanie nových titulkov
3. **Automatický**: Inteligentná detekcia titulkov

## 🛠️ Riešenie problémov

### Ak sa aplikácia nespustí:
1. Skontroluj, či máš nainštalovaný Python 3.10+
2. Spusti v termináli: `pip3 install -r requirements.txt`
3. Skontroluj, či máš nainštalovaný Tesseract: `brew install tesseract`

### Ak chýbajú závislosti:
```bash
pip3 install PyQt6 pytesseract pillow pynput
```

### Ak nefunguje TTS:
- Skontroluj systémové nastavenia hlasu v macOS
- Aplikácia používa natívny macOS `say` command

## 📝 Poznámky

- Aplikácia beží v demo režime (20 minút denne)
- Pre plnú verziu je potrebná licencia
- Všetky spustiteľné súbory majú nastavené správne práva
- Aplikácia automaticky detekuje a používa správnu cestu k Pythonu

## 🎯 Odporúčanie

**Najlepší spôsob spustenia**: Dvojklik na `SpustiTicho`
- **Úplne bez dialógov a terminálov**
- Spustí sa okamžite na pozadí
- Najrýchlejší a najčistejší spôsob
- Žiadne otravné okná

**Alternatíva**: Vytvor si Automator aplikáciu
- Postupuj podľa `AUTOMATOR_NAVOD.md`
- Získaš natívnu macOS aplikáciu
- Ikona v Docku, profesionálny vzhľad
