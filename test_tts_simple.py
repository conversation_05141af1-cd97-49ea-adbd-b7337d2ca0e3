#!/usr/bin/env python3
"""
Jednoduchý test TTS pre Windows - diagnostika problému s hlasmi
"""

import logging
import sys
import os

# Nastavenie logovanie
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('tts_test.log', encoding='utf-8')
    ]
)

def test_pyttsx3():
    """Test základného pyttsx3 TTS."""
    print("=== TEST PYTTSX3 ===")
    try:
        import pyttsx3
        engine = pyttsx3.init()
        
        # Zoznam hlasov
        voices = engine.getProperty('voices')
        print(f"Počet hlasov: {len(voices)}")
        for i, voice in enumerate(voices):
            print(f"  {i}: {voice.name} ({voice.id})")
        
        # Test reči
        print("Testujem reč...")
        engine.setProperty('rate', 200)
        engine.setProperty('volume', 1.0)
        engine.say("Hello, this is a test of Windows TTS.")
        engine.runAndWait()
        print("✅ pyttsx3 test dokončený")
        return True
        
    except Exception as e:
        print(f"❌ pyttsx3 test zlyhal: {e}")
        logging.error(f"pyttsx3 error: {e}")
        return False

def test_onecore():
    """Test OneCore TTS."""
    print("\n=== TEST ONECORE ===")
    try:
        from onecore_tts_provider import get_onecore_provider
        
        provider = get_onecore_provider()
        if not provider.is_available():
            print("❌ OneCore provider nie je dostupný")
            return False
            
        voices = provider.get_available_voices()
        print(f"OneCore hlasy: {len(voices)}")
        for voice in voices:
            print(f"  - {voice}")
            
        # Test reči
        print("Testujem OneCore reč...")
        success = provider.speak("Hello, this is a test of OneCore TTS.", rate=200, volume=1.0)
        if success:
            print("✅ OneCore test spustený")
            # Počkaj na dokončenie
            import time
            time.sleep(3)
        else:
            print("❌ OneCore test zlyhal")
        return success
        
    except Exception as e:
        print(f"❌ OneCore test zlyhal: {e}")
        logging.error(f"OneCore error: {e}")
        return False

def test_platform_provider():
    """Test platform TTS provider."""
    print("\n=== TEST PLATFORM PROVIDER ===")
    try:
        from platform_utils import get_tts_provider
        
        provider = get_tts_provider()
        print(f"Provider: {provider.__class__.__name__}")
        
        # Test reči
        print("Testujem platform provider reč...")
        success = provider.speak("Hello, this is a test of platform TTS provider.", rate=200, volume=1.0)
        if success:
            print("✅ Platform provider test spustený")
            # Počkaj na dokončenie
            import time
            time.sleep(3)
        else:
            print("❌ Platform provider test zlyhal")
        return success
        
    except Exception as e:
        print(f"❌ Platform provider test zlyhal: {e}")
        logging.error(f"Platform provider error: {e}")
        return False

def main():
    print("🔊 Windows TTS Diagnostika")
    print("=" * 50)
    
    # Test všetkých TTS systémov
    pyttsx3_ok = test_pyttsx3()
    onecore_ok = test_onecore()
    platform_ok = test_platform_provider()
    
    print("\n" + "=" * 50)
    print("📊 VÝSLEDKY:")
    print(f"  pyttsx3: {'✅' if pyttsx3_ok else '❌'}")
    print(f"  OneCore: {'✅' if onecore_ok else '❌'}")
    print(f"  Platform Provider: {'✅' if platform_ok else '❌'}")
    
    if not any([pyttsx3_ok, onecore_ok, platform_ok]):
        print("\n❌ Žiadny TTS systém nefunguje!")
        return 1
    else:
        print("\n✅ Aspoň jeden TTS systém funguje")
        return 0

if __name__ == "__main__":
    sys.exit(main())
