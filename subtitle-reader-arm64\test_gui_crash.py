#!/usr/bin/env python3
"""
Test na zachytenie GUI crashu
"""

import sys
import os
import logging
import traceback
from PyQt6 import QtWidgets, QtCore

def setup_logging():
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('gui_crash_test.log', encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )

def test_gui_creation():
    """Test vytvorenia GUI s detailným error handlingom"""
    
    print("🔍 Testovanie GUI vytvorenia...")
    logging.info("🔍 Testovanie GUI vytvorenia...")
    
    try:
        print("1. Vytváram QApplication...")
        app = QtWidgets.QApplication(sys.argv)
        print("✅ QApplication vytvorená")
        
        print("2. Testovanie základného okna...")
        test_window = QtWidgets.QMainWindow()
        test_window.setWindowTitle("Test Window")
        test_window.setGeometry(100, 100, 400, 300)
        test_window.show()
        print("✅ Základné okno funguje")
        test_window.close()
        
        print("3. Testovanie importov aplikácie...")
        
        # Test importov postupne
        try:
            print("   - common_config...")
            import common_config
            print("   ✅ common_config OK")
        except Exception as e:
            print(f"   ❌ common_config: {e}")
            traceback.print_exc()
            
        try:
            print("   - i18n_manager...")
            from i18n_manager import get_translator, get_language_manager
            print("   ✅ i18n_manager OK")
        except Exception as e:
            print(f"   ❌ i18n_manager: {e}")
            traceback.print_exc()
            
        try:
            print("   - qt_gui...")
            from qt_gui import SubtitleReaderQtGUI
            print("   ✅ qt_gui import OK")
        except Exception as e:
            print(f"   ❌ qt_gui import: {e}")
            traceback.print_exc()
            return
        
        print("4. Vytváram SubtitleReaderQtGUI...")
        
        # Dummy callbacks
        def dummy_on_closing():
            print("dummy_on_closing called")
            
        def dummy_toggle_reading():
            print("dummy_toggle_reading called")
            
        def dummy_set_reading_mode(mode):
            print(f"dummy_set_reading_mode called: {mode}")
        
        try:
            gui = SubtitleReaderQtGUI(
                dummy_on_closing,
                dummy_toggle_reading, 
                dummy_set_reading_mode
            )
            print("✅ SubtitleReaderQtGUI vytvorené!")
            
            # Test zobrazenia
            print("5. Testovanie zobrazenia GUI...")
            gui.show()
            gui.raise_()
            gui.activateWindow()
            print("✅ GUI zobrazené!")
            
            # Krátky test behu
            print("6. Spúšťam Qt event loop na 3 sekundy...")
            QtCore.QTimer.singleShot(3000, app.quit)  # Ukončí po 3 sekundách
            
            result = app.exec()
            print(f"✅ Qt event loop skončil s kódom: {result}")
            
        except Exception as e:
            print(f"❌ Chyba pri vytváraní SubtitleReaderQtGUI: {e}")
            traceback.print_exc()
            
    except Exception as e:
        print(f"❌ Kritická chyba: {e}")
        traceback.print_exc()

def main():
    setup_logging()
    
    print("🚀 GUI Crash Test")
    print("=" * 50)
    
    test_gui_creation()
    
    print("=" * 50)
    print("🎯 Test dokončený - skontrolujte gui_crash_test.log")

if __name__ == "__main__":
    main()
