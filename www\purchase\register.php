<?php
session_start();
require_once '../config/database.php';

$hardware_id = $_GET['hw'] ?? '';
$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $first_name = trim($_POST['first_name'] ?? '');
    $last_name = trim($_POST['last_name'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $password_confirm = $_POST['password_confirm'] ?? '';
    $terms_accepted = isset($_POST['terms_accepted']);
    
    // Validácia
    if (empty($first_name) || empty($last_name) || empty($email) || empty($password)) {
        $error = 'Všechna pole jsou povinná.';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'Neplatná emailová adresa.';
    } elseif (strlen($password) < 8) {
        $error = 'Heslo musí mít alespoň 8 znaků.';
    } elseif ($password !== $password_confirm) {
        $error = 'Hesla se neshodují.';
    } elseif (!$terms_accepted) {
        $error = 'Musíte souhlasit s obchodními podmínkami.';
    } else {
        try {
            // Kontrola existencie emailu
            $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
            $stmt->execute([$email]);
            if ($stmt->fetch()) {
                $error = 'Uživatel s tímto emailem již existuje.';
            } else {
                // Vytvorenie užívateľa
                $user_id = createUser($email, $password, $first_name, $last_name);
                
                // Vytvorenie verification tokenu
                $token = createEmailVerificationToken($user_id);
                
                // Odoslanie verification emailu
                $verification_url = APP_URL . "/purchase/verify-email.php?token=" . $token;
                $email_body = "
                    <h2>Ověření emailové adresy - VOXO LOXO</h2>
                    <p>Dobrý den {$first_name},</p>
                    <p>děkujeme za registraci. Pro dokončení registrace klikněte na následující odkaz:</p>
                    <p><a href='{$verification_url}'>Ověřit emailovou adresu</a></p>
                    <p>Odkaz je platný 24 hodin.</p>
                    <p>S pozdravem,<br>Tým VOXO LOXO</p>
                ";
                
                if (sendEmail($email, 'Ověření emailové adresy - VOXO LOXO', $email_body)) {
                    $_SESSION['user_id'] = $user_id;
                    $_SESSION['email'] = $email;
                    $_SESSION['hardware_id'] = $hardware_id;
                    
                    header('Location: verify-email.php');
                    exit();
                } else {
                    $error = 'Chyba při odesílání ověřovacího emailu.';
                }
            }
        } catch (Exception $e) {
            error_log("Registration error: " . $e->getMessage());
            $error = 'Došlo k chybě při registraci. Zkuste to prosím znovu.';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registrace - VOXO LOXO</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .register-container {
            max-width: 500px;
            margin: 50px auto;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .logo {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo i {
            font-size: 3rem;
            color: #667eea;
        }
        .price-highlight {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
        }
        .btn-primary:hover {
            background: linear-gradient(45deg, #5a6fd8, #6a4190);
        }
    </style>
</head>
<body class="bg-light">
    <div class="container">
        <div class="register-container bg-white">
            <div class="logo">
                <i class="fas fa-eye"></i>
                <h2 class="mt-2">VOXO LOXO</h2>
                <p class="text-muted">Cross-platform registrace pro Windows, macOS a Linux</p>
            </div>
            
            <div class="price-highlight">
                <h3><i class="fas fa-crown"></i> Cross-Platform licence na rok</h3>
                <div class="display-6">299 Kč</div>
                <small>Windows • macOS • Linux • Neomezené používání • Premium podpora</small>
            </div>
            
            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i> <?= htmlspecialchars($error) ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check"></i> <?= htmlspecialchars($success) ?>
                </div>
            <?php endif; ?>
            
            <form method="POST">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="first_name" class="form-label">Jméno *</label>
                        <input type="text" class="form-control" id="first_name" name="first_name" 
                               value="<?= htmlspecialchars($_POST['first_name'] ?? '') ?>" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="last_name" class="form-label">Příjmení *</label>
                        <input type="text" class="form-control" id="last_name" name="last_name" 
                               value="<?= htmlspecialchars($_POST['last_name'] ?? '') ?>" required>
                    </div>
                </div>
                
                <div class="mb-3">
                    <label for="email" class="form-label">Emailová adresa *</label>
                    <input type="email" class="form-control" id="email" name="email" 
                           value="<?= htmlspecialchars($_POST['email'] ?? '') ?>" required>
                    <div class="form-text">Na tento email vám pošleme licenční klíč.</div>
                </div>
                
                <div class="mb-3">
                    <label for="password" class="form-label">Heslo *</label>
                    <input type="password" class="form-control" id="password" name="password" 
                           minlength="8" required>
                    <div class="form-text">Minimálně 8 znaků.</div>
                </div>
                
                <div class="mb-3">
                    <label for="password_confirm" class="form-label">Potvrzení hesla *</label>
                    <input type="password" class="form-control" id="password_confirm" name="password_confirm" 
                           minlength="8" required>
                </div>
                
                <?php if ($hardware_id): ?>
                    <div class="mb-3">
                        <div class="alert alert-info">
                            <i class="fas fa-desktop"></i> 
                            <strong>Počítač:</strong> <?= htmlspecialchars(substr($hardware_id, 0, 8)) ?>...
                            <br><small>Licence bude vázána na tento počítač.</small>
                        </div>
                    </div>
                <?php endif; ?>
                
                <div class="mb-3 form-check">
                    <input type="checkbox" class="form-check-input" id="terms_accepted" name="terms_accepted" required>
                    <label class="form-check-label" for="terms_accepted">
                        Souhlasím s <a href="../terms.php" target="_blank">obchodními podmínkami</a> 
                        a <a href="../privacy.php" target="_blank">zásadami ochrany osobních údajů</a> *
                    </label>
                </div>
                
                <button type="submit" class="btn btn-primary btn-lg w-100">
                    <i class="fas fa-user-plus"></i> Registrovat a pokračovat k platbě
                </button>
            </form>
            
            <div class="text-center mt-4">
                <p class="text-muted">
                    Již máte účet? <a href="login.php">Přihlaste se</a>
                </p>
                <p class="text-muted">
                    <small>
                        <i class="fas fa-shield-alt"></i> 
                        Bezpečná platba přes Stripe • SSL šifrování
                    </small>
                </p>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Validácia hesiel
        document.getElementById('password_confirm').addEventListener('input', function() {
            const password = document.getElementById('password').value;
            const confirm = this.value;
            
            if (password !== confirm) {
                this.setCustomValidity('Hesla se neshodují');
            } else {
                this.setCustomValidity('');
            }
        });
    </script>
</body>
</html>
