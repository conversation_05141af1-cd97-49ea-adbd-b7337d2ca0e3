#!/bin/bash

# 🍎 Vytvorenie natívnej macOS aplikácie bez dialógov
# Tento script vytvorí .app bundle ktorý sa spustí priamo bez dialógov

echo "🍎 Vytváram natívnu macOS aplikáciu..."

# Názov aplikácie
APP_NAME="SubtitleReader_Native"
CURRENT_DIR="$(pwd)"
APP_PATH="$CURRENT_DIR/${APP_NAME}.app"

# Vymaž starú aplikáciu ak existuje
if [ -d "$APP_PATH" ]; then
    echo "🗑️ Mažem starú aplikáciu..."
    rm -rf "$APP_PATH"
fi

# Vytvor štruktúru .app bundle
mkdir -p "$APP_PATH/Contents/MacOS"
mkdir -p "$APP_PATH/Contents/Resources"

# Vytvor Info.plist
cat > "$APP_PATH/Contents/Info.plist" << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>CFBundleExecutable</key>
    <string>SubtitleReader_Native</string>
    <key>CFBundleIconFile</key>
    <string>icon</string>
    <key>CFBundleIdentifier</key>
    <string>com.subtitlereader.native</string>
    <key>CFBundleName</key>
    <string>Subtitle Reader</string>
    <key>CFBundlePackageType</key>
    <string>APPL</string>
    <key>CFBundleShortVersionString</key>
    <string>1.0</string>
    <key>CFBundleVersion</key>
    <string>1.0</string>
    <key>LSMinimumSystemVersion</key>
    <string>10.15</string>
    <key>NSHighResolutionCapable</key>
    <true/>
    <key>LSUIElement</key>
    <false/>
</dict>
</plist>
EOF

# Vytvor hlavný spustiteľný súbor
cat > "$APP_PATH/Contents/MacOS/${APP_NAME}" << 'EOF'
#!/bin/bash

# Získaj adresár kde je .app súbor
APP_BUNDLE_DIR="$(dirname "$(dirname "$(dirname "$0")")")"
PARENT_DIR="$(dirname "$APP_BUNDLE_DIR")"
cd "$PARENT_DIR"

# Nájdi Python - testovaná cesta
PYTHON_CMD="/Library/Frameworks/Python.framework/Versions/3.10/bin/python3"

# Ak sa nenašiel, skús alternatívy
if [ ! -f "$PYTHON_CMD" ]; then
    if command -v python3 &> /dev/null; then
        PYTHON_CMD="python3"
    elif command -v python &> /dev/null; then
        PYTHON_CMD="python"
    else
        # Tichá chyba - len ukončí
        exit 1
    fi
fi

# Kontrola hlavného súboru
if [ ! -f "main_qt.py" ]; then
    exit 1
fi

# Spusti aplikáciu na pozadí
exec $PYTHON_CMD main_qt.py
EOF

# Nastav spustiteľné práva
chmod +x "$APP_PATH/Contents/MacOS/${APP_NAME}"

echo "✅ Natívna aplikácia vytvorená: ${APP_NAME}.app"
echo "🚀 Táto aplikácia sa spustí priamo bez dialógov!"
echo "📱 Dvojklik na ${APP_NAME}.app v Finderi"
