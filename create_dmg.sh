#!/bin/bash

# 💿 Script na vytvorenie DMG inštalátora pre Subtitle Reader
# Vytvorí profesionálny DMG súbor s drag-and-drop inštaláciou

set -e

echo "💿 Subtitle Reader - DMG Creator"
echo "================================"

# Farby
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Nastavenia
APP_NAME="Subtitle Reader"
APP_PATH="dist/${APP_NAME}.app"
DMG_NAME="SubtitleReader-macOS-Universal"
DMG_PATH="dist/${DMG_NAME}.dmg"
TEMP_DMG="temp_${DMG_NAME}.dmg"
VOLUME_NAME="Subtitle Reader Installer"

# Kontrola či existuje aplikácia
if [ ! -d "$APP_PATH" ]; then
    print_error "Aplikácia nenájdená: $APP_PATH"
    print_info "Najprv spusti: ./build_macos_app.sh"
    exit 1
fi

print_info "Vytváram DMG inštalátor..."

# Vymaž starý DMG
rm -f "$DMG_PATH"
rm -f "$TEMP_DMG"

# Vytvor dočasný adresár pre DMG obsah
TEMP_DIR=$(mktemp -d)
print_info "Dočasný adresár: $TEMP_DIR"

# Skopíruj aplikáciu
print_info "Kopírujem aplikáciu..."
cp -R "$APP_PATH" "$TEMP_DIR/"

# Vytvor symlink na Applications
print_info "Vytváram odkaz na Applications..."
ln -s /Applications "$TEMP_DIR/Applications"

# Vytvor README súbor pre DMG
cat > "$TEMP_DIR/README.txt" << 'EOF'
Subtitle Reader - macOS Installer
==================================

Inštalácia:
1. Pretiahni "Subtitle Reader.app" do priečinka "Applications"
2. Spusti aplikáciu z Launchpadu alebo Applications

Systémové požiadavky:
- macOS 10.15 (Catalina) alebo novší
- ARM (Apple Silicon) alebo Intel Mac

Funkcie:
- OCR čítanie titulkov z obrazovky
- Text-to-Speech (TTS) vo viacerých jazykoch
- Automatická detekcia textu
- Podpora slovenčiny, angličtiny a ďalších jazykov

Pre viac informácií navštív: https://subtitlereader.com

© 2025 Subtitle Reader Team
EOF

# Priprav pozadie DMG (voliteľné)
BG_SOURCE="${DMG_BG_IMAGE:-assets/dmg/background.png}"
APPLESCRIPT_BG=""
if [ -f "$BG_SOURCE" ]; then
    print_info "Pridávam pozadie DMG z '$BG_SOURCE'"
    mkdir -p "$TEMP_DIR/.background"
    cp "$BG_SOURCE" "$TEMP_DIR/.background/background.png"
    APPLESCRIPT_BG='set background picture of viewOptions to file ".background:background.png"'
else
    print_warning "Pozadie pre DMG nenájdené ($BG_SOURCE). Pokračujem bez pozadia."
fi

# Vypočítaj veľkosť pre DMG
SIZE_MB=$(du -sm "$TEMP_DIR" | cut -f1)
SIZE_MB=$((SIZE_MB + 50))  # Pridaj 50MB rezervu

print_info "Vytváram DMG súbor (${SIZE_MB}MB)..."

# Vytvor DMG
hdiutil create -srcfolder "$TEMP_DIR" \
    -volname "$VOLUME_NAME" \
    -fs HFS+ \
    -fsargs "-c c=64,a=16,e=16" \
    -format UDRW \
    -size ${SIZE_MB}m \
    "$TEMP_DMG"

# Pripoj DMG pre úpravu
print_info "Pripájam DMG pre úpravu..."
DEVICE=$(hdiutil attach -readwrite -noverify -noautoopen "$TEMP_DMG" | egrep '^/dev/' | sed 1q | awk '{print $1}')
MOUNT_POINT="/Volumes/$VOLUME_NAME"

# Počkaj na pripojenie
sleep 2

# Nastav pozície ikon v Finderi (AppleScript)
print_info "Nastavujem rozloženie ikon..."
osascript << EOF
tell application "Finder"
    tell disk "$VOLUME_NAME"
        open
        set current view of container window to icon view
        set toolbar visible of container window to false
        set statusbar visible of container window to false
        set the bounds of container window to {400, 100, 920, 440}
        set viewOptions to the icon view options of container window
        set arrangement of viewOptions to not arranged
        set icon size of viewOptions to 72
        ${APPLESCRIPT_BG}
        set position of item "$APP_NAME.app" of container window to {160, 205}
        set position of item "Applications" of container window to {360, 205}
        set position of item "README.txt" of container window to {260, 300}
        close
        open
        update without registering applications
        delay 2
    end tell
end tell
EOF

# Odpoj DMG
print_info "Odpájam DMG..."
hdiutil detach "$DEVICE"

# Konvertuj na finálny komprimovaný DMG
print_info "Komprimujem DMG..."
hdiutil convert "$TEMP_DMG" \
    -format UDZO \
    -imagekey zlib-level=9 \
    -o "$DMG_PATH"

# Vyčisti dočasné súbory
rm -f "$TEMP_DMG"
rm -rf "$TEMP_DIR"

# Kontrola výsledku
if [ -f "$DMG_PATH" ]; then
    DMG_SIZE=$(du -sh "$DMG_PATH" | cut -f1)
    print_success "DMG vytvorený: $DMG_PATH"
    print_info "Veľkosť DMG: $DMG_SIZE"
    
    # Test DMG
    print_info "Testujem DMG..."
    if hdiutil verify "$DMG_PATH" >/dev/null 2>&1; then
        print_success "DMG je v poriadku"
    else
        print_warning "DMG test zlyhal"
    fi
    
    echo ""
    print_success "🎉 DMG inštalátor je pripravený!"
    echo ""
    print_info "📋 Súbor: $DMG_PATH"
    print_info "📋 Veľkosť: $DMG_SIZE"
    print_info "📋 Typ: Universal Binary (ARM + Intel)"
    echo ""
    print_info "🚀 Môžeš ho distribuovať používateľom macOS!"
    print_info "💡 Pre otvorenie: open '$DMG_PATH'"
    
else
    print_error "DMG sa nevytvoril!"
    exit 1
fi

print_success "Hotovo! 💿"
