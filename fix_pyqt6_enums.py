#!/usr/bin/env python3
"""
Script na opravu PyQt6 enum problémov v qt_gui.py
"""

import re

def fix_pyqt6_enums():
    """Opraví všetky PyQt6 enum problémy v qt_gui.py"""
    
    with open('qt_gui.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Zoznam opráv
    fixes = [
        # Horizontal orientation
        (r'QtWidgets\.QSlider\(QtCore\.Qt\.Horizontal\)', 
         r'QtWidgets.QSlider(QtCore.Qt.Orientation.Horizontal)'),
        
        # Alignment
        (r'QtCore\.Qt\.AlignCenter', 
         r'QtCore.Qt.AlignmentFlag.AlignCenter'),
        
        # Window modality
        (r'QtCore\.Qt\.WindowModal', 
         r'QtCore.Qt.WindowModality.WindowModal'),
        
        # ScrollBar policies (už opravené, ale pre istotu)
        (r'QtCore\.Qt\.ScrollBarAsNeeded', 
         r'QtCore.Qt.ScrollBarPolicy.ScrollBarAsNeeded'),
        
        # Text interaction flags (už opravené)
        (r'QtCore\.Qt\.TextSelectableByMouse', 
         r'QtCore.Qt.TextInteractionFlag.TextSelectableByMouse'),
        
        # Window flags (už opravené)
        (r'QtCore\.Qt\.WindowType\.WindowStaysOnTopHint', 
         r'QtCore.Qt.WindowType.WindowStaysOnTopHint'),
    ]
    
    original_content = content
    
    for pattern, replacement in fixes:
        content = re.sub(pattern, replacement, content)
    
    # Zapíš opravený obsah
    with open('qt_gui.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    # Spočítaj zmeny
    changes = len(re.findall(r'\n', original_content)) - len(re.findall(r'\n', content))
    
    print(f"✅ PyQt6 enum opravy dokončené")
    print(f"📝 Súbor qt_gui.py bol aktualizovaný")
    
    # Ukáž, čo sa zmenilo
    for pattern, replacement in fixes:
        matches = len(re.findall(pattern, original_content))
        if matches > 0:
            print(f"   🔧 {matches}x: {pattern} → {replacement}")

if __name__ == "__main__":
    fix_pyqt6_enums()
