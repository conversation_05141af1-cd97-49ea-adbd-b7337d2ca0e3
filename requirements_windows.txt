# Windows-specific requirements for Subtitle Reader
# Cross-platform dependencies (same as macOS)
Levenshtein==0.27.1
mss==10.0.0
packaging==25.0
pillow==11.3.0
pynput==1.7.6
pytesseract==0.3.13
RapidFuzz==3.13.0
six==1.17.0

# GUI Framework
PyQt6==6.7.0

# TTS for Windows
pyttsx3==2.98

# Windows-specific system integration
pywin32==306

# Optional: WMI for advanced hardware detection
WMI==1.5.1

# HTTP requests
requests==2.32.3
python-dotenv==1.0.1

# Data processing
numpy==1.26.4
tqdm==4.66.4

# Web interface (if needed)
pywebview

# Note: pyobjc and all pyobjc-framework-* packages are NOT included
# as they are macOS-specific and will cause installation failures on Windows

# Installation instructions for Windows:
# 1. Install Python 3.10+ from python.org
# 2. Create virtual environment: python -m venv .venv
# 3. Activate virtual environment: .venv\Scripts\activate
# 4. Install dependencies: pip install -r requirements_windows.txt
# 5. Install Tesseract OCR from: https://github.com/UB-Mannheim/tesseract/wiki
# 6. Add Tesseract to PATH or set TESSERACT_CMD environment variable

# Optional dependencies for enhanced functionality:
# - For CUDA support (if you have NVIDIA GPU):
#   torch==2.2.2+cu118 torchvision==0.17.2+cu118 torchaudio==2.2.2+cu118 --index-url https://download.pytorch.org/whl/cu118
# - For CPU-only PyTorch:
#   torch==2.2.2 torchvision==0.17.2 torchaudio==2.2.2 --index-url https://download.pytorch.org/whl/cpu
