#!/usr/bin/env python3
"""
<PERSON>no<PERSON><PERSON>ý test Mac M1 setupu
"""

print("🚀 Test Mac M1 setupu")
print("=" * 40)

# Test PyTorch
try:
    import torch
    print(f"✅ PyTorch {torch.__version__}")
    print(f"✅ MPS dostupné: {torch.backends.mps.is_available()}")
except Exception as e:
    print(f"❌ PyTorch: {e}")

# Test Transformers
try:
    import transformers
    print(f"✅ Transformers {transformers.__version__}")
except Exception as e:
    print(f"❌ Transformers: {e}")

# Test CTranslate2
try:
    import ctranslate2
    print(f"✅ CTranslate2 {ctranslate2.__version__}")
except Exception as e:
    print(f"❌ CTranslate2: {e}")

# Test translation_manager
try:
    import translation_manager
    print(f"✅ Translation Manager načítaný")
    
    # Test nastavení
    modes = translation_manager.TRANSLATOR_MODES
    print(f"✅ Dostupné módy: {list(modes.keys())}")
    
    # Test globálnych konštánt
    print(f"✅ MAX_LENGTH: {translation_manager.MAX_LENGTH}")
    print(f"✅ REPETITION_PENALTY: {translation_manager.REPETITION_PENALTY}")
    
except Exception as e:
    print(f"❌ Translation Manager: {e}")

print("=" * 40)
print("🎉 Test dokončený!")
