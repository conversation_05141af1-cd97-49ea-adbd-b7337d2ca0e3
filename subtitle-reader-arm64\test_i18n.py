#!/usr/bin/env python3

import sys
import logging

# Nastavenie logovania
logging.basicConfig(level=logging.INFO, format='%(levelname)s:%(name)s:%(message)s')

try:
    print("🔍 Test 1: Import language_detector")
    from language_detector import get_language_detector
    print('✅ language_detector importovaný')

    print("🔍 Test 2: Vytvorenie detector inštancie")
    detector = get_language_detector()
    print('✅ detector vytvorený')

    print("🔍 Test 3: Detekcia jazykov")
    langs = detector.detect_all_languages()
    print(f'✅ Jazyky detekované: {langs}')

    print("🔍 Test 4: Import i18n_manager")
    from i18n_manager import get_language_manager
    print('✅ i18n_manager importovaný')

    print("🔍 Test 5: Vytvorenie LanguageManager")
    lm = get_language_manager()
    print('✅ LanguageManager vytvorený')

    print("🔍 Test 6: Dostupné jazyky")
    app_langs = lm.get_available_app_languages()
    reading_langs = lm.get_available_reading_languages()

    print(f"📱 Jazyky aplikácie: {app_langs}")
    print(f"📖 Jazyky čítania: {reading_langs}")

except Exception as e:
    print(f'❌ Chyba: {e}')
    import traceback
    traceback.print_exc()
