#!/usr/bin/env python3
"""
Minimálna verzia GUI pre testovanie
"""

import sys
import os
from PyQt6 import QtWidgets, QtCore, QtGui

class MinimalSubtitleReader(QtWidgets.QMainWindow):
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        self.setWindowTitle("Subtitle Reader - Minimal Test")
        self.setGeometry(100, 100, 600, 400)
        
        # Centrálny widget
        central_widget = QtWidgets.QWidget()
        self.setCentralWidget(central_widget)
        
        # Layout
        layout = QtWidgets.QVBoxLayout()
        central_widget.setLayout(layout)
        
        # Nadpis
        title = QtWidgets.QLabel("🎬 Subtitle Reader")
        title.setStyleSheet("font-size: 24px; font-weight: bold; padding: 20px;")
        title.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)
        
        # Status
        self.status_label = QtWidgets.QLabel("✅ Aplikácia je pripravená na testovanie!")
        self.status_label.setStyleSheet("font-size: 14px; padding: 10px; background-color: #2d2d2d; color: #00ff00; border-radius: 5px;")
        self.status_label.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.status_label)
        
        # Tlačidlá
        button_layout = QtWidgets.QHBoxLayout()
        
        self.start_button = QtWidgets.QPushButton("▶️ Start Reading")
        self.start_button.setStyleSheet("font-size: 16px; padding: 10px; background-color: #4CAF50; color: white; border: none; border-radius: 5px;")
        self.start_button.clicked.connect(self.start_reading)
        button_layout.addWidget(self.start_button)
        
        self.stop_button = QtWidgets.QPushButton("⏹️ Stop Reading")
        self.stop_button.setStyleSheet("font-size: 16px; padding: 10px; background-color: #f44336; color: white; border: none; border-radius: 5px;")
        self.stop_button.clicked.connect(self.stop_reading)
        button_layout.addWidget(self.stop_button)
        
        layout.addLayout(button_layout)
        
        # Test area
        self.test_area = QtWidgets.QTextEdit()
        self.test_area.setPlaceholderText("Tu sa budú zobrazovať rozpoznané titulky...")
        self.test_area.setStyleSheet("font-size: 12px; padding: 10px; background-color: #1e1e1e; color: #ffffff; border: 1px solid #555;")
        layout.addWidget(self.test_area)
        
        # Tmavý štýl
        self.setStyleSheet("""
            QMainWindow {
                background-color: #2b2b2b;
                color: #ffffff;
            }
            QLabel {
                color: #ffffff;
            }
        """)
        
        # Zobraz okno
        self.show()
        self.raise_()
        self.activateWindow()
        
        print("✅ Minimálne GUI vytvorené a zobrazené")
    
    def start_reading(self):
        self.status_label.setText("🔄 Čítanie spustené...")
        self.status_label.setStyleSheet("font-size: 14px; padding: 10px; background-color: #2d2d2d; color: #ffff00; border-radius: 5px;")
        self.test_area.append("📖 Simulácia čítania titulkov...")
        print("Start reading clicked")
    
    def stop_reading(self):
        self.status_label.setText("⏹️ Čítanie zastavené")
        self.status_label.setStyleSheet("font-size: 14px; padding: 10px; background-color: #2d2d2d; color: #ff0000; border-radius: 5px;")
        self.test_area.append("⏹️ Čítanie zastavené")
        print("Stop reading clicked")

def main():
    print("🚀 Spúšťam minimálnu GUI aplikáciu...")
    
    # Vytvor aplikáciu
    app = QtWidgets.QApplication(sys.argv)
    
    # Nastav aplikáciu
    app.setApplicationName("Subtitle Reader")
    app.setApplicationVersion("1.0")
    
    # Vytvor hlavné okno
    window = MinimalSubtitleReader()
    
    print("🖥️ GUI okno by sa malo zobraziť")
    print("📍 Ak nevidíte okno, skontrolujte taskbar")
    
    # Spusti event loop
    try:
        sys.exit(app.exec())
    except KeyboardInterrupt:
        print("👋 Aplikácia ukončená")
        sys.exit(0)

if __name__ == "__main__":
    main()
