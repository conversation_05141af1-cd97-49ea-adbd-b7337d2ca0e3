#!/bin/bash

# 🚀 Spúšťač Subtitle Reader aplikácie pre macOS
# Vytvorené: 2025-09-25
# Tento .command súbor sa spustí v Termináli pri dvojkliku a zatvorí terminál

echo "🍎 Spúšťam Subtitle Reader aplikáciu..."

# Získaj adresár kde sa nachádza tento script
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
APP_DIR="$SCRIPT_DIR"

echo "📁 Adresár aplikácie: $APP_DIR"

# Prejdi do adresára aplikácie
cd "$APP_DIR"

# Kontrola či existuje main_qt.py
if [ ! -f "main_qt.py" ]; then
    echo "❌ Chyba: main_qt.py sa nenašiel v adresári $APP_DIR"
    echo "💡 Skontroluj, či si v správnom adresári"
    read -p "Stlač Enter pre ukončenie..."
    exit 1
fi

# Nájdi správny Python - použijem plnú cestu ktorá funguje
PYTHON_CMD="/Library/Frameworks/Python.framework/Versions/3.10/bin/python3"

# Ak sa nenašiel, skús alternatívy
if [ ! -f "$PYTHON_CMD" ]; then
    if command -v python3 &> /dev/null; then
        PYTHON_CMD="python3"
    elif command -v python &> /dev/null; then
        PYTHON_CMD="python"
    else
        echo "❌ Chyba: Python nie je nainštalovaný alebo nie je v PATH"
        echo "💡 Nainštaluj Python z https://python.org"
        read -p "Stlač Enter pre ukončenie..."
        exit 1
    fi
fi

echo "🐍 Používam Python: $PYTHON_CMD"

# Kontrola virtual environment
if [ -d "venv" ]; then
    echo "🔧 Aktivujem virtual environment..."
    source venv/bin/activate
    echo "✅ Virtual environment aktivovaný"
elif [ -d ".venv" ]; then
    echo "🔧 Aktivujem virtual environment (.venv)..."
    source .venv/bin/activate
    echo "✅ Virtual environment aktivovaný"
else
    echo "ℹ️ Virtual environment sa nenašiel, používam systémový Python"
fi

# Kontrola závislostí
echo "📦 Kontrolujem závislosti..."
if ! $PYTHON_CMD -c "import PyQt6" 2>/dev/null; then
    echo "⚠️ Chýbajú závislosti. Pokúšam sa ich nainštalovať..."
    if [ -f "requirements.txt" ]; then
        pip3 install -r requirements.txt
    else
        echo "❌ requirements.txt sa nenašiel"
        echo "💡 Nainštaluj závislosti manuálne: pip3 install PyQt6 pytesseract pillow pynput"
        read -p "Stlač Enter pre pokračovanie aj tak..."
    fi
fi

echo "🚀 Spúšťam aplikáciu..."
echo "----------------------------------------"

# Spusti aplikáciu na pozadí a zatvor terminál
echo "🚀 Spúšťam GUI aplikáciu..."
echo "📱 Terminál sa zatvorí za 3 sekundy, aplikácia pobeží na pozadí"

# Spusti aplikáciu na pozadí
nohup $PYTHON_CMD main_qt.py > /dev/null 2>&1 &

# Počkaj chvíľu na spustenie
sleep 3

echo "✅ Aplikácia spustená! Zatváram terminál..."

# Zatvor terminálové okno
osascript -e 'tell application "Terminal" to close first window' > /dev/null 2>&1 &

# Ukončenie scriptu
exit 0
