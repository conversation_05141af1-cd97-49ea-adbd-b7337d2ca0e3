import time
import subprocess
import threading
import queue
import logging

import common_config as config
import csv_logger
# import translation_manager  # ODSTRÁNENÉ

# Cross-platform TTS support
try:
    from platform_utils import get_tts_provider, IS_MACOS, IS_WINDOWS, IS_LINUX
    CROSS_PLATFORM_TTS_AVAILABLE = True

    # Initialize TTS provider
    tts_provider = get_tts_provider()
    logging.info(f"🎤 TTS Provider initialized: {tts_provider.__class__.__name__}")

except ImportError as e:
    CROSS_PLATFORM_TTS_AVAILABLE = False
    tts_provider = None
    logging.warning(f"⚠️ Cross-platform TTS not available: {e}")
    logging.warning("⚠️ Falling back to legacy macOS-only implementation")

# --- <PERSON><PERSON> pre TTS ---

def get_available_voices():
    """Get list of available TTS voices from the cross-platform provider."""
    if CROSS_PLATFORM_TTS_AVAILABLE and tts_provider:
        try:
            voices = tts_provider.get_available_voices()
            logging.info(f"🎤 Retrieved {len(voices)} voices from TTS provider")
            return voices
        except Exception as e:
            logging.error(f"❌ Error getting voices from TTS provider: {e}")
            return []
    else:
        logging.warning("⚠️ Cross-platform TTS not available, no voices to return")
        return []

def is_speaking() -> bool:
    """Check if TTS is currently speaking."""
    if CROSS_PLATFORM_TTS_AVAILABLE and tts_provider:
        try:
            return tts_provider.is_speaking()
        except Exception as e:
            logging.error(f"❌ Error checking speaking status: {e}")
            return False
    return False

def stop_speaking():
    """Stop current TTS speech and wait for it to actually stop."""
    if CROSS_PLATFORM_TTS_AVAILABLE and tts_provider:
        try:
            tts_provider.stop()
            logging.info("🛑 TTS speech stopped")

            # Počkáme, kým sa TTS skutočne nezastaví
            import time
            max_wait = 0.25  # Skrátené max čakanie na 250ms pre rýchlejšiu odozvu
            wait_step = 0.05  # Kontrola každých 50ms
            waited = 0

            while waited < max_wait and is_speaking():
                time.sleep(wait_step)
                waited += wait_step
                logging.debug(f"⏳ Čakám na zastavenie TTS... ({waited:.2f}s)")

            if is_speaking():
                logging.warning("⚠️ TTS sa nezastavil v očakávanom čase")
            else:
                logging.debug("✅ TTS úspešne zastavený")

        except Exception as e:
            logging.error(f"❌ Error stopping TTS: {e}")

def set_voice(voice_name: str) -> bool:
    """Set the TTS voice by name."""
    if CROSS_PLATFORM_TTS_AVAILABLE and tts_provider:
        try:
            # Update config with new voice
            config.TTS_VOICE = voice_name
            config.READING_TTS_VOICE = voice_name
            logging.info(f"🎤 TTS voice set to: {voice_name}")
            return True
        except Exception as e:
            logging.error(f"❌ Error setting TTS voice: {e}")
            return False
    else:
        logging.warning("⚠️ Cross-platform TTS not available, cannot set voice")
        return False

def adjust_tts_rate():
    """Dynamically adjusts TTS_RATE based on the tts_command_queue size."""
    # OPRAVA: Vždy používaj USER_TTS_RATE ako základnú rýchlosť (nastavenie z posuvníka)
    original_rate = config.USER_TTS_RATE

    queue_size = config.tts_command_queue.qsize()

    if queue_size == 2:
        new_rate = int(original_rate * 1.1)
    elif queue_size == 3:
        new_rate = int(original_rate * 1.2)
    elif queue_size >= 4:
        new_rate = int(original_rate * 1.3)
    else:
        # Vrátiť na používateľom nastavenú rýchlosť
        new_rate = original_rate

    # Clamp the new_rate to the allowed range
    new_rate = max(config.MIN_TTS_RATE, min(config.MAX_TTS_RATE, new_rate))

    if new_rate != config.TTS_RATE:
        old_rate = config.TTS_RATE
        config.TTS_RATE = new_rate
        if queue_size == 0 or queue_size == 1:
            logging.info(f"TTS queue size is {queue_size}, spomaľujem TTS rate z {old_rate} na {config.TTS_RATE} (base: {original_rate})")
        else:
            logging.info(f"TTS queue size is {queue_size}, zrýchľujem TTS rate z {old_rate} na {config.TTS_RATE} (base: {original_rate})")
        if config.gui_instance:
            config.gui_instance.update_all_sliders_and_labels()

def tts_worker():
    logging.info("Spúšťam TTS worker thread (režim neblokujúceho spracovania).")

    while not config.tts_stop_event.is_set():
        # 🔧 OPRAVA: Kontrolujeme cross-platform TTS provider namiesto config.current_say_process
        tts_is_speaking = False
        if CROSS_PLATFORM_TTS_AVAILABLE and tts_provider:
            try:
                tts_is_speaking = tts_provider.is_speaking()
            except Exception as e:
                logging.error(f"❌ Chyba pri kontrole TTS stavu: {e}")
        else:
            # Fallback na starú logiku
            tts_is_speaking = config.current_say_process and config.current_say_process.poll() is None

        # Ak je aktívny TTS proces, čakáme na jeho dokončenie alebo na nový trigger
        if tts_is_speaking:
            # Čakáme buď na dokončenie TTS alebo na nový trigger (max 10ms)
            config.tts_trigger_event.wait(timeout=0.01)
            config.tts_trigger_event.clear()
            continue

        # Ak nie je aktívny TTS proces, okamžite spracujeme frontu
        try:
            command = config.tts_command_queue.get(timeout=0.001)  # Veľmi krátky timeout
            action = command.get('action')

            if action == 'say':
                text_to_say = command.get('text', '')
                cycle_id = command.get('cycle_id')

                # skip_translation = command.get('skip_translation', False)  # ODSTRÁNENÉ

                if text_to_say:
                    adjust_tts_rate()  # Dynamically adjust TTS rate
                    config.current_tts_cycle_id = cycle_id

                    # Vyber hlas pre čítanie (všetky hlášky používajú hlas jazyka čítania)
                    selected_voice = config.TTS_VOICE
                    try:
                        # Všetky hlášky (systémové aj titulky) používajú hlas pre jazyk čítania
                        selected_voice = getattr(config, 'READING_TTS_VOICE', selected_voice)
                        if cycle_id is None:
                            logging.debug(f"🔊 Systémová hláška - používam hlas pre jazyk čítania: {selected_voice}")
                        else:
                            logging.debug(f"📖 Titulok - používam hlas pre jazyk čítania: {selected_voice}")
                    except Exception as e:
                        logging.error(f"Chyba pri výbere hlasu: {e}")
                        pass

                    rate_val = config.TTS_RATE
                    volume_val = getattr(config, 'TTS_VOLUME', 1.0)

                    # DEBUG: Zobraz aktuálny hlas
                    logging.info(f"🔍 TTS DEBUG: selected_voice='{selected_voice}', config.TTS_VOICE='{config.TTS_VOICE}', TTS_LANGUAGE='{config.TTS_LANGUAGE}'")
                    logging.debug(f"TTS Queue size: {config.tts_command_queue.qsize()}, Next commands: {[cmd.get('text', '') for cmd in list(config.tts_command_queue.queue)[:5]]}")
                    logging.info(f"Executing TTS command for cycle_id {cycle_id}: voice='{selected_voice}', rate={rate_val}, volume={volume_val}")

                    # Use cross-platform TTS if available
                    if CROSS_PLATFORM_TTS_AVAILABLE and tts_provider:
                        success = tts_provider.speak(text_to_say, selected_voice, rate_val, volume_val)
                        if success:
                            logging.info(f"✅ Cross-platform TTS executed successfully")
                        else:
                            logging.error(f"❌ Cross-platform TTS failed")
                    else:
                        # Fallback to legacy macOS implementation
                        success = _legacy_macos_tts(text_to_say, selected_voice, rate_val)
                        if not success:
                            logging.error(f"❌ Legacy macOS TTS failed")

                    logging.info(f"[PERF_TRACE] CycleID={cycle_id}: TTS worker začína hovoriť.")

                    # Nespúšťaj OCR pri ukončovacej hlášky alebo systémových hlášok
                    if cycle_id is not None and not text_to_say.startswith("Končím") and not text_to_say.startswith("Začínám"):
                        config.ocr_trigger_event.set()
                        logging.info("[PERF_TRACE] OCR spustený proaktívne signálom od TTS.")
                    else:
                        logging.debug(f"OCR trigger preskočený pre systémovú hlášku: '{text_to_say}'")

                    logging.info(f"TTS command started for text: '{text_to_say}' (cycle_id: {cycle_id})")

            elif action == 'stop':
                logging.info("Received stop command, clearing any pending TTS commands.")

                # Use cross-platform TTS stop if available
                if CROSS_PLATFORM_TTS_AVAILABLE and tts_provider:
                    tts_provider.stop()
                    logging.info("✅ Cross-platform TTS stopped")
                else:
                    # Legacy macOS stop implementation
                    if config.current_say_process and config.current_say_process.poll() is None:
                        logging.info("Terminating active 'say' process.")
                        config.current_say_process.terminate()
                        config.current_say_process.wait(timeout=0.5)
                        if config.current_say_process.poll() is None:
                            logging.warning("'say' process did not terminate, killing it.")
                            config.current_say_process.kill()

                while not config.tts_command_queue.empty():
                    try:
                        config.tts_command_queue.get_nowait()
                    except queue.Empty:
                        break
                    finally:
                        config.tts_command_queue.task_done()
                config.current_tts_cycle_id = None

            config.tts_command_queue.task_done()

        except queue.Empty:
            config.current_tts_cycle_id = None
            time.sleep(0.001)  # Veľmi krátka pauza (1ms) namiesto 10ms
            continue
        except Exception as e_worker:
            logging.error(f"CHYBA [TTS Worker]: Neočakávaná chyba: {e_worker}")
            config.current_tts_cycle_id = None
            time.sleep(0.1)

    logging.info("TTS worker thread sa ukončuje.")

def init_tts_worker():
    config.tts_stop_event.clear()
    config.tts_worker_thread = threading.Thread(target=tts_worker, daemon=True)
    config.tts_worker_thread.start()

def enqueue_tts_command(action: str, text: str = "", cycle_id: int = None):
    try:
        config.tts_command_queue.put({'action': action, 'text': text, 'cycle_id': cycle_id})
        # Okamžite prebudíme TTS worker pre spracovanie nového príkazu
        config.tts_trigger_event.set()
        logging.debug(f"TTS_TRIGGER: Prebudený TTS worker pre akciu '{action}'")
    except Exception as e:
        logging.error(f"Nepodarilo sa pridať príkaz '{action}' do fronty: {e}")

def speak_text(text, cycle_id: int = None):
    if not text:
        csv_logger.log_tts_event(cycle_id, "", "TTS_SKIPPED", "Prázdny text")
        return

    # Prekladač bol odstránený - používame len pôvodný text
    logging.info(f"AUDIT_SPEAK_EXEC: Text='{text}', CycleID={cycle_id}")
    csv_logger.log_tts_event(cycle_id, text, "TTS_SENT")
    enqueue_tts_command('say', text, cycle_id)

def stop_current_tts():
    logging.info("Stopping current TTS process and clearing queue.")

    # Use cross-platform TTS stop if available
    if CROSS_PLATFORM_TTS_AVAILABLE and tts_provider:
        tts_provider.stop()
        logging.info("✅ Cross-platform TTS stopped")
    else:
        # Legacy macOS stop implementation
        if config.current_say_process and config.current_say_process.poll() is None:
            try:
                logging.info("Terminating active 'say' process.")
                config.current_say_process.terminate() # Pošli SIGTERM
                config.current_say_process.wait(timeout=0.5) # Daj mu čas na ukončenie
                if config.current_say_process.poll() is None:
                    logging.warning("'say' process did not terminate gracefully, killing it.")
                    config.current_say_process.kill() # Pošli SIGKILL ak SIGTERM zlyhal
            except Exception as e:
                logging.error(f"Chyba pri ukončovaní 'say' procesu: {e}")

    enqueue_tts_command('stop')

def stop_tts_worker():
    config.tts_stop_event.set()

    # Use cross-platform TTS stop if available
    if CROSS_PLATFORM_TTS_AVAILABLE and tts_provider:
        tts_provider.stop()
        logging.info("✅ Cross-platform TTS stopped during worker shutdown")
    else:
        # Legacy macOS stop implementation
        if config.current_say_process and config.current_say_process.poll() is None:
            try:
                logging.info("Terminating active 'say' process during worker shutdown.")
                config.current_say_process.terminate()
                config.current_say_process.wait(timeout=0.5)
                if config.current_say_process.poll() is None:
                    logging.warning("'say' process did not terminate gracefully during shutdown, killing it.")
                    config.current_say_process.kill()
            except Exception as e:
                logging.error(f"Chyba pri ukončovaní 'say' procesu počas vypínania workera: {e}")

    if config.tts_worker_thread and config.tts_worker_thread.is_alive():
        config.tts_worker_thread.join(timeout=1.0)
        if config.tts_worker_thread.is_alive():
            logging.warning("TTS worker thread sa neukončil včas.")

def set_tts_rate(rate: int):
    config.TTS_RATE = max(config.MIN_TTS_RATE, min(config.MAX_TTS_RATE, rate))
    config.USER_TTS_RATE = config.TTS_RATE
    if config.gui_instance:
        config.gui_instance.update_all_sliders_and_labels()

def set_tts_volume(volume: float):
    config.TTS_VOLUME = max(config.MIN_TTS_VOLUME, min(config.MAX_TTS_VOLUME, volume))
    if config.gui_instance:
        config.gui_instance.update_all_sliders_and_labels()

def reset_tts_rate_to_original():
    """
    Resetuje TTS rýchlosť na pôvodnú hodnotu po zastavení čítania.
    Toto opravuje problém, kde dynamické zrýchľovanie zostávalo aktívne aj po zastavení.
    """
    if config.MANUAL_TTS_RATE_ADJUSTMENT:
        # Ak používateľ manuálne nastavil rýchlosť, vrátime na jeho nastavenie
        original_rate = config.USER_TTS_RATE
        logging.info(f"Resetujem TTS rýchlosť na používateľom nastavenú hodnotu: {original_rate}")
    else:
        # Inak vrátime na predvolenú rýchlosť
        original_rate = config.DEFAULT_TTS_RATE
        logging.info(f"Resetujem TTS rýchlosť na predvolenú hodnotu: {original_rate}")

    config.TTS_RATE = original_rate

    # Aktualizácia GUI ak existuje
    if config.gui_instance:
        config.gui_instance.update_all_sliders_and_labels()

    logging.info(f"TTS rýchlosť resetovaná na {original_rate}")

# Legacy macOS TTS implementation (fallback)
def _legacy_macos_tts(text: str, voice: str, rate: int) -> bool:
    """Legacy macOS TTS implementation using 'say' command."""
    try:
        # Normalizácia hlasu pre macOS
        def _normalize_voice_name(v: str) -> str:
            if not v:
                return 'Samantha'

            # Apple TTS podporuje hlasy s kvalitami, takže ich ponechávame
            v2 = v.strip()

            # Preklad slovenských/českých názvov kvalít späť na anglické
            v2 = v2.replace('(vylepšený)', '(Enhanced)')
            v2 = v2.replace('(prémiový)', '(Premium)')
            v2 = v2.replace('(enhanced)', '(Enhanced)')
            v2 = v2.replace('(premium)', '(Premium)')

            # 🔧 OPRAVA: Apple TTS logika pre hlasy
            # Standard hlasy = bez prípony (napr. "Laura" namiesto "Laura (Standard)")
            # Enhanced/Premium hlasy = s príponou (napr. "Laura (Enhanced)")
            if v2.endswith(' (Standard)'):
                v2 = v2.replace(' (Standard)', '')  # Odstráň (Standard) - Apple TTS ho neočakáva
            # Enhanced a Premium hlasy ponechaj s príponou - Apple TTS ich rozpoznáva

            # Odstránime locale informácie ak sú na konci
            if v2.endswith(')') and ' (' in v2:
                parts = v2.split(' (')
                if len(parts) >= 2:
                    last_part = parts[-1].rstrip(')')
                    if '-' in last_part and len(last_part) == 5:  # napr. "cs-CZ"
                        v2 = ' ('.join(parts[:-1])

            return v2 or 'Samantha'

        safe_voice = _normalize_voice_name(voice)
        cmd_list = ['/usr/bin/say', '-v', safe_voice, '-r', str(rate), '--', text]

        config.current_say_process = subprocess.Popen(
            cmd_list,
            stdout=subprocess.DEVNULL,
            stderr=subprocess.PIPE
        )

        stderr_output = config.current_say_process.communicate()[1].decode().strip()
        if stderr_output:
            logging.error(f"Chyba z 'say' príkazu: {stderr_output}")
            # Fallback: ak hlas nebol nájdený, skús bez -v
            if 'not find' in stderr_output.lower() or 'not found' in stderr_output.lower():
                try:
                    logging.warning(f"Fallback na systémový defaultný hlas, safe_voice='{safe_voice}' zlyhal")
                    cmd_list_fallback = ['/usr/bin/say', '-r', str(rate), '--', text]
                    config.current_say_process = subprocess.Popen(
                        cmd_list_fallback,
                        stdout=subprocess.DEVNULL,
                        stderr=subprocess.PIPE
                    )
                except Exception as e:
                    logging.error(f"Fallback 'say' bez hlasu zlyhal: {e}")
                    return False

        return True

    except Exception as e:
        logging.error(f"❌ Legacy macOS TTS error: {e}")
        return False