# Subtitle Reader - Windows Installation Guide

## 🎯 Overview

Subtitle Reader is now cross-platform! This guide covers installation and setup on Windows systems while maintaining full compatibility with macOS.

## 📋 Prerequisites

### Required Software

1. **Python 3.10 or higher**
   - Download from: https://python.org
   - ⚠️ **Important**: Check "Add Python to PATH" during installation

2. **Tesseract OCR**
   - Download from: https://github.com/UB-Mannheim/tesseract/wiki
   - Install with default settings
   - The installer should automatically add Tesseract to PATH

3. **Visual C++ Redistributable** (usually already installed)
   - Download from Microsoft if needed

### Optional Software

- **Git** (for cloning the repository)
- **Windows Terminal** (better command line experience)

## 🚀 Quick Installation

### Option 1: Automated Setup (Recommended)

1. **Download or clone the repository**
   ```cmd
   git clone <repository-url>
   cd subtitle-reader
   ```

2. **Run the Windows setup script**
   ```cmd
   setup_windows.bat
   ```

3. **Follow the on-screen instructions**

### Option 2: Manual Installation

1. **Create virtual environment**
   ```cmd
   python -m venv .venv
   .venv\Scripts\activate.bat
   ```

2. **Install dependencies**
   ```cmd
   pip install -r requirements_windows.txt
   ```

3. **Verify Tesseract installation**
   ```cmd
   tesseract --version
   ```

## 🎮 Running the Application

1. **Activate virtual environment** (if not already active)
   ```cmd
   .venv\Scripts\activate.bat
   ```

2. **Run the application**
   ```cmd
   python main_qt.py
   ```

3. **Test cross-platform functionality**
   ```cmd
   python test_cross_platform.py
   ```

## ⌨️ Hotkeys on Windows

The application automatically converts macOS hotkeys to Windows equivalents:

| Function | macOS | Windows |
|----------|-------|---------|
| Toggle Reading | `Cmd+Alt+Ctrl+V` | `Ctrl+Alt+Ctrl+V` |
| Volume Down | `Cmd+Alt+Ctrl+←` | `Ctrl+Alt+Ctrl+←` |
| Volume Up | `Cmd+Alt+Ctrl+→` | `Ctrl+Alt+Ctrl+→` |
| Rate Down | `Cmd+Alt+Ctrl+↓` | `Ctrl+Alt+Ctrl+↓` |
| Rate Up | `Cmd+Alt+Ctrl+↑` | `Ctrl+Alt+Ctrl+↑` |
| Report Anomaly | `Cmd+Alt+Ctrl+I` | `Ctrl+Alt+Ctrl+I` |

## 🔧 Configuration

### Tesseract Configuration

If Tesseract is not found automatically, you can:

1. **Add to PATH manually**
   - Add `C:\Program Files\Tesseract-OCR` to your system PATH

2. **Set environment variable**
   ```cmd
   set TESSERACT_CMD=C:\Program Files\Tesseract-OCR\tesseract.exe
   ```

### TTS Configuration

Windows uses the built-in SAPI voices. To install additional voices:

1. **Open Settings** → **Time & Language** → **Speech**
2. **Add voices** for your preferred languages
3. **Restart the application** to detect new voices

## 🐛 Troubleshooting

### Common Issues

**1. "Python not found"**
- Reinstall Python with "Add to PATH" checked
- Restart command prompt after installation

**2. "Tesseract not found"**
- Verify installation: `tesseract --version`
- Check PATH or set TESSERACT_CMD environment variable

**3. "Permission denied" for global hotkeys**
- Run as Administrator (right-click → "Run as administrator")
- Allow through Windows Defender if prompted

**4. "No TTS voices found"**
- Install additional Windows voices through Settings
- Restart the application

**5. "Import errors"**
- Ensure virtual environment is activated
- Reinstall dependencies: `pip install -r requirements_windows.txt`

### Performance Tips

1. **Disable Windows Defender real-time scanning** for the application folder (optional)
2. **Run as Administrator** for better hotkey responsiveness
3. **Close unnecessary applications** to free up system resources

## 🔄 Cross-Platform Features

### What Works Cross-Platform

✅ **OCR Text Recognition** - Full Tesseract integration  
✅ **Text-to-Speech** - Windows SAPI voices  
✅ **Global Hotkeys** - Automatic key mapping  
✅ **GUI Interface** - Complete PyQt6 interface  
✅ **License Management** - Full commercial licensing  
✅ **Settings & Configuration** - All preferences  
✅ **Hardware Fingerprinting** - Windows WMI integration  

### Platform-Specific Differences

| Feature | macOS | Windows | Status |
|---------|-------|---------|--------|
| TTS Engine | `say` command | SAPI/pyttsx3 | ✅ Full |
| Voice Quality | Enhanced/Premium | Standard/SAPI | ✅ Mapped |
| Active App Detection | AppKit | win32gui | ✅ Full |
| Browser URL Detection | AppleScript | Limited | ⚠️ Basic |
| Hardware ID | IOKit | WMI | ✅ Full |
| System Language | defaults | Registry/Locale | ✅ Full |

## 📁 File Structure

```
subtitle-reader/
├── platform_utils.py          # Cross-platform abstraction
├── platform_windows.py        # Windows implementations
├── platform_macos.py          # macOS implementations  
├── platform_linux.py          # Linux implementations
├── requirements_windows.txt    # Windows dependencies
├── setup_windows.bat          # Windows setup script
├── test_cross_platform.py     # Cross-platform tests
└── README_WINDOWS.md          # This file
```

## 🆘 Getting Help

1. **Run the test script** to diagnose issues:
   ```cmd
   python test_cross_platform.py
   ```

2. **Check the logs** in the application data directory:
   ```
   %APPDATA%\SubtitleReader\logs\
   ```

3. **Verify all dependencies** are installed:
   ```cmd
   pip list
   ```

## 🔄 Updating

To update to a newer version:

1. **Backup your settings** (optional)
2. **Pull latest changes**:
   ```cmd
   git pull
   ```
3. **Update dependencies**:
   ```cmd
   pip install -r requirements_windows.txt --upgrade
   ```

## 📝 Notes

- The application maintains full backward compatibility with macOS
- All user data and settings are stored in platform-appropriate locations
- License files work across all platforms
- Performance may vary between platforms due to different TTS engines

---

**Need help?** Check the main README.md for general usage instructions or run the test script for diagnostics.
