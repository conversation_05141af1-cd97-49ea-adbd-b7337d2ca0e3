# 🧭 macOS: Debug → Build → Distribúcia (Subtitle Reader)

Praktický návod, ako sme uviedli aplikáciu do chodu na macOS, vybuildili univerzálnu binárku (M1 + Intel) a pripravili inštalačný súbor ready na distribúciu mimo App Store.

---

## 1) Debug: spustenie GUI a fix drobnej chyby

- Aktivovali sme lokálne virtuálne prostredie:
  - `source subtitle_reader_env/bin/activate`
- Nainštalovali sme závislosti:
  - `pip install -r requirements.txt`
- Spustenie GUI priamo:
  - `python main_qt.py`
- Odhalená a opravená chyba v `main_qt.py` (duplicitné `else:` bloky okolo riadkov 39–41), následne sa app úspešne spustila s GUI.
- Overenie: logy ukázali načítani<PERSON> j<PERSON>ov, Tesseract, TTS hlasy a inicializáciu GUI.

Tipy:
- Najtichšie spustenie bez okien: dvojklik na `SpustiTicho`
- Sp<PERSON><PERSON><PERSON><PERSON><PERSON> s GUI: `SpustiSubtitleReader.command` (volá `main_qt.py`).

---

## 2) Build: univerzálna binárka (arm64 + x86_64)

Používame py2app. Cieľ: jedna `.app` pre Apple Silicon (M1/M2) aj Intel.

Predpoklady:
- `py2app` v aktívnom venv (bolo OK)
- Python 3.10 (projekt používa testovanú systémovú inštaláciu v `/Library/Frameworks/.../3.10`)

Úprava buildu:
- V `setup.py` je zapnuté `OPTIONS['arch'] = 'universal2'` (pre obe architektúry).

Build príkaz:
- `source subtitle_reader_env/bin/activate && python setup.py py2app`

Výsledok:
- `.app` v `dist/Subtitle Reader.app`
- Overenie architektúr:
  - `file "dist/Subtitle Reader.app/Contents/MacOS/Subtitle Reader"`
  - Výstup: Mach-O universal binary (x86_64 + arm64)

---

## 3) Vytvorenie DMG inštalátora

Možnosti:
1) Jednoduchý, spoľahlivý DMG (bez pozadia/rozloženia):
   - `hdiutil create -volname "Subtitle Reader" -srcfolder "dist/Subtitle Reader.app" -ov -format UDZO "dist/SubtitleReader-macOS-Universal.dmg"`
2) Pokročilý (s pozadím a layoutom): `./create_dmg.sh` (ak chcete custom vzhľad; pri absencii podkladového obrázka môže zlyhať AppleScript krok – dá sa doladiť tým, že pridáme background asset).

Odporúčanie: pre prvé releasy použite jednoduchý DMG.
### 3.2 Polished DMG (pozadie + layout + symlink)

Ak chceš „pekne“ vyzerajúci inštalátor s pozadím a rozložením ikon, použi skript `create_dmg.sh`.

Kroky:
1) Priprav pozadie (voliteľné, odporúčané):
   - Umiestni obrázok na `assets/dmg/background.png` (alebo nastav cestu env premennou `DMG_BG_IMAGE=/absolutna/cesta/obrazok.png`)
2) Spusti build `.app` a dokonči podpis+notarizáciu+staple (sekcia 4)
3) Spusti skript:
   - `bash ./create_dmg.sh`

Čo skript spraví:
- skopíruje app do dočasného adresára,
- vytvorí symlink `Applications`,
- pridá `README.txt`,
- ak nájde `assets/dmg/background.png` (alebo cestu v `DMG_BG_IMAGE`), vloží ho do `.background/background.png` v DMG,
- vytvorí DMG, pripojí ho, nastaví layout (AppleScript), odpojí a zkomprimuje na finálne UDZO DMG.

Poznámky:
- Cestu k pozadiu môžeš prebiť: `DMG_BG_IMAGE=./moje_pozadie.png bash ./create_dmg.sh`
- Ak pozadie chýba, skript pokračuje bez neho.
- DMG je v `dist/SubtitleReader-macOS-Universal.dmg`.


---

## 4) Distribúcia mimo App Store: podpis → notarizácia → staple

Aby app prešla Gatekeeperom bez varovaní, potrebujete podpis (Developer ID), notarizáciu (Apple cloud kontrola) a “staple” notárskeho tiketu do .app.

### 4.1 Prerequisites (jednorazovo)
- Apple Developer Program (platené)
- Xcode CLT: `xcode-select --install`
- V Keychaine certifikát „Developer ID Application: Vaše meno (TEAMID)“
- Nájdite presný názov identity:
  - `security find-identity -v -p codesigning`

### 4.2 Podpis aplikácie (codesign)
- `APP="dist/Subtitle Reader.app"`
- `IDENTITY='Developer ID Application: Vase Meno (TEAMID)'`
- Podpis s Hardened Runtime:
  - `codesign --deep --force --options runtime --sign "$IDENTITY" "$APP"`
- Overenie podpisu:
  - `codesign --verify --deep --strict --verbose=2 "$APP"`
  - `spctl -a -vv "$APP"` (pred notarizáciou môže ukázať “rejected” – je OK)

Poznámka: Ak by Apple vyžadoval entitlements, doplníme minimálny plist a podpíšeme znova.

### 4.3 Notarizácia (xcrun notarytool)
- Uložte prihlasovanie (Apple ID, TeamID) do Keychain profilu (jednorazovo):
  - `xcrun notarytool store-credentials "SubtitleReaderNotary" --apple-id "<EMAIL>" --team-id "TEAMID" --password "app-specific-password"`
- Zabaľte `.app` do ZIP (notarizuje sa ZIP, nie DMG):
  - `ditto -c -k --keepParent "dist/Subtitle Reader.app" "dist/SubtitleReader-macOS-Universal.zip"`
- Odoslanie na notarizáciu a čakanie:
  - `xcrun notarytool submit "dist/SubtitleReader-macOS-Universal.zip" --keychain-profile "SubtitleReaderNotary" --wait`
- Očakávaný výsledok: `Accepted`.

### 4.4 „Staple“ tiketu do appky
- `xcrun stapler staple "dist/Subtitle Reader.app"`
- Overenie:
  - `xcrun stapler validate "dist/Subtitle Reader.app"`

### 4.5 Finálny inštalátor
- DMG vytvorte až z „stapled“ `.app`:
  - `hdiutil create -volname "Subtitle Reader" -srcfolder "dist/Subtitle Reader.app" -ov -format UDZO "dist/SubtitleReader-macOS-Universal.dmg"`
- Tento DMG pošlite používateľom (funguje na M1 aj Intel, Gatekeeper pustí bez varovania). Pri prvom spustení si systém vyžiada oprávnenia pre Accessibility a Screen Recording (očakávané kvôli hotkeys/OCR).

---

## 5) FAQ

- „Je notarizácia len formalita?“ – Nie. Apple automaticky skenuje app (malware, podpisy, Hardened Runtime, entitlements). Je to rýchle (minúty), ale reálna kontrola.
- „Musím ísť cez Mac App Store?“ – Nie. Bežne sa distribuuje mimo App Store cez Developer ID + notarizáciu. App Store znamená sandbox a manuálnu review – pre tento typ nástroja je jednoduchší Developer ID flow.

---

## 6) Rýchly checklist

1) Aktivuj venv: `source subtitle_reader_env/bin/activate`
2) Build: `python setup.py py2app`
3) Over architektúru: `file dist/.../Subtitle\ Reader`
4) Podpíš: `codesign --deep --force --options runtime -s "Developer ID Application: ..." dist/Subtitle\ Reader.app`
5) ZIP: `ditto -c -k --keepParent "dist/Subtitle Reader.app" dist/SubtitleReader-macOS-Universal.zip`
6) Notarizuj: `xcrun notarytool submit dist/SubtitleReader-macOS-Universal.zip --keychain-profile SubtitleReaderNotary --wait`
7) Staple: `xcrun stapler staple dist/Subtitle\ Reader.app`
8) DMG: `hdiutil create -volname "Subtitle Reader" -srcfolder "dist/Subtitle Reader.app" -ov -format UDZO dist/SubtitleReader-macOS-Universal.dmg`

---

## 7) Poznámky a odporúčania

- Bundle ID (`com.subtitlereader.app`) nech je konzistentné.
- Pre „polished“ DMG upravíme `create_dmg.sh` a pridáme pozadie `.background/background.png` a ikonový layout.
- Ak notarizácia zlyhá, pozrite log v odpovedi notarytool; podľa chyby doplníme entitlements/podpis.
- Aktualizácie: mimo App Store riešite vlastným updaterom alebo informovaním o nových verziách.

