"""Initializačný modul pre full_automatic_mode balík."""

# Exportuj kľúč<PERSON> komponenty, aby importy fungovali konzistentne
from .full_automatic_logic import (
    start_full_automatic_detection_processing,
    stop_full_automatic_detection_processing,
    process_text,
    log_text_change_to_csv,
)
from .full_auto_dynamic_detector import analyze_dynamic_mode_state as analyze_dynamic_subtitle_state
from .full_auto_dynamic_detector import analyze_dynamic_mode_state
from .full_auto_stability_detector import reset_stability_detection

__all__ = [
    'start_full_automatic_detection_processing',
    'stop_full_automatic_detection_processing',
    'process_text',
    'log_text_change_to_csv',
    'analyze_dynamic_subtitle_state',
    'analyze_dynamic_mode_state',
    'reset_stability_detection',
]
