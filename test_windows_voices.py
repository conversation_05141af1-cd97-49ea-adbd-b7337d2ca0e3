#!/usr/bin/env python3
"""
Test Windows TTS hlasov s vylep<PERSON><PERSON>u detekciou
"""

import sys
import logging
from typing import List, Dict, Any

def setup_logging():
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('windows_voices_test.log', encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )

def test_sapi_voices():
    """Test priamej SAPI detekcie hlasov"""
    print("🔍 Testovanie Windows SAPI hlasov...")
    
    try:
        import win32com.client
        
        # Create SAPI voice object
        sapi_voices = win32com.client.Dispatch("SAPI.SpVoice")
        voice_tokens = sapi_voices.GetVoices()
        
        print(f"✅ Nájdených {voice_tokens.Count} SAPI hlasov:")
        
        for i in range(voice_tokens.Count):
            voice_token = voice_tokens.Item(i)
            
            # Get basic info
            voice_name = voice_token.GetDescription()
            voice_id = voice_token.Id
            
            print(f"\n🎤 Hlas {i+1}:")
            print(f"   📝 Názov: {voice_name}")
            print(f"   🆔 ID: {voice_id}")
            
            # Try to get language attribute
            try:
                lang_attr = voice_token.GetAttribute("Language")
                if lang_attr:
                    lcid = int(lang_attr, 16) if isinstance(lang_attr, str) else lang_attr
                    print(f"   🌍 LCID: {hex(lcid)} ({lcid})")
                else:
                    print(f"   🌍 LCID: Nedostupný")
            except Exception as e:
                print(f"   🌍 LCID chyba: {e}")
            
            # Try to get other attributes
            try:
                gender_attr = voice_token.GetAttribute("Gender")
                if gender_attr:
                    print(f"   👤 Pohlavie: {gender_attr}")
            except:
                print(f"   👤 Pohlavie: Nedostupné")
            
            try:
                age_attr = voice_token.GetAttribute("Age")
                if age_attr:
                    print(f"   🎂 Vek: {age_attr}")
            except:
                print(f"   🎂 Vek: Nedostupný")
                
    except Exception as e:
        print(f"❌ SAPI test zlyhal: {e}")
        import traceback
        traceback.print_exc()

def test_platform_windows():
    """Test našej platform_windows implementácie"""
    print("\n" + "="*50)
    print("🔍 Testovanie platform_windows implementácie...")
    
    try:
        from platform_windows import WindowsTTSProvider
        
        tts_provider = WindowsTTSProvider()
        voices = tts_provider.get_available_voices()
        
        print(f"✅ Nájdených {len(voices)} hlasov cez platform_windows:")
        
        for i, voice in enumerate(voices):
            print(f"\n🎤 Hlas {i+1}:")
            print(f"   📝 Názov: {voice.get('name', 'Unknown')}")
            print(f"   🌍 Jazyk: {voice.get('language', 'Unknown')}")
            print(f"   👤 Pohlavie: {voice.get('gender', 'Unknown')}")
            print(f"   ⭐ Kvalita: {voice.get('quality', 'Unknown')}")
            print(f"   🆔 ID: {voice.get('id', 'Unknown')[:50]}...")
            
    except Exception as e:
        print(f"❌ platform_windows test zlyhal: {e}")
        import traceback
        traceback.print_exc()

def test_pyttsx3_voices():
    """Test pyttsx3 hlasov pre porovnanie"""
    print("\n" + "="*50)
    print("🔍 Testovanie pyttsx3 hlasov...")
    
    try:
        import pyttsx3
        
        engine = pyttsx3.init()
        voices = engine.getProperty('voices')
        
        print(f"✅ Nájdených {len(voices)} pyttsx3 hlasov:")
        
        for i, voice in enumerate(voices):
            print(f"\n🎤 Hlas {i+1}:")
            print(f"   📝 Názov: {voice.name}")
            print(f"   🆔 ID: {voice.id[:50]}...")
            
            # Try to get languages
            try:
                languages = getattr(voice, 'languages', [])
                if languages:
                    print(f"   🌍 Jazyky: {languages}")
                else:
                    print(f"   🌍 Jazyky: Nedostupné")
            except:
                print(f"   🌍 Jazyky: Chyba")
            
            # Try to get gender
            try:
                gender = getattr(voice, 'gender', 'unknown')
                print(f"   👤 Pohlavie: {gender}")
            except:
                print(f"   👤 Pohlavie: Nedostupné")
                
    except Exception as e:
        print(f"❌ pyttsx3 test zlyhal: {e}")
        import traceback
        traceback.print_exc()

def test_voice_mapping():
    """Test mapovania jazykov"""
    print("\n" + "="*50)
    print("🔍 Testovanie mapovania jazykov...")
    
    try:
        from platform_windows import WindowsTTSProvider
        
        provider = WindowsTTSProvider()
        
        # Test LCID mappings
        test_lcids = [
            (0x0409, 'en-US'),
            (0x0809, 'en-GB'),
            (0x040c, 'fr-FR'),
            (0x0407, 'de-DE'),
            (0x041b, 'sk-SK'),
            (0x0405, 'cs-CZ'),
        ]
        
        print("📍 Test LCID mapovaní:")
        for lcid, expected in test_lcids:
            result = provider._lcid_to_language(lcid)
            status = "✅" if result == expected else "❌"
            print(f"   {status} LCID {hex(lcid)}: {result} (očakávané: {expected})")
        
        # Test name extraction
        test_names = [
            ("Microsoft David Desktop", "en-US"),
            ("Microsoft Zira Desktop", "en-US"),
            ("Microsoft Hedda Desktop", "de-DE"),
            ("Microsoft Jakub Desktop", "cs-CZ"),
            ("Microsoft Filip Desktop", "sk-SK"),
        ]
        
        print("\n📍 Test extrahovania z názvov:")
        for name, expected in test_names:
            result = provider._extract_language_from_name(name)
            status = "✅" if result == expected else "❌"
            print(f"   {status} '{name}': {result} (očakávané: {expected})")
            
    except Exception as e:
        print(f"❌ Mapping test zlyhal: {e}")
        import traceback
        traceback.print_exc()

def main():
    setup_logging()
    
    print("🚀 Windows TTS Voices Test")
    print("=" * 60)
    
    # Test všetky metódy
    test_sapi_voices()
    test_platform_windows()
    test_pyttsx3_voices()
    test_voice_mapping()
    
    print("\n" + "=" * 60)
    print("🎯 Test dokončený - skontrolujte windows_voices_test.log")

if __name__ == "__main__":
    main()
