# Subtitle Reader - x86 Installation Guide

## 🎯 Automatick<PERSON> inštalácia pre x86/x64 Windows

Tento balíček obsahuje všetko potrebné pre spustenie Subtitle Reader aplikácie na x86/x64 Windows systémoch.

## 📦 <PERSON><PERSON><PERSON> bal<PERSON>

```
subtitle-reader-x86/
├── install_x86.bat              # 🔧 Hlavný inštalačný script
├── run_app_x86.bat             # 🚀 Spúšťací script aplikácie
├── test_x86_installation.py    # 🧪 Test script
├── requirements_x86.txt        # 📋 Zoznam závislostí
├── README_X86_INSTALLATION.md  # 📖 Tento súbor
├── main_qt.py                  # 🖥️ Hlavná aplikácia
├── common_config.py            # ⚙️ Konfigurácia
├── platform_windows.py        # 🪟 Windows implementácia
├── onecore_tts_provider.py     # 🎤 OneCore TTS
├── language_detector.py       # 🌍 Detekcia jazykov
├── i18n_manager.py            # 🗣️ Správa jazykov
└── [všetky ostatné .py súbory] # 📁 Kompletná aplikácia
```

## 🚀 Jednoduchá inštalácia (3 kroky)

### 1️⃣ Rozbaľte balíček
Rozbaľte ZIP súbor do ľubovoľného adresára, napr.:
```
C:\subtitle-reader\
```

### 2️⃣ Spustite inštalátor
Kliknite pravým tlačidlom na `install_x86.bat` → **"Spustiť ako správca"**

**Alebo** otvorte Command Prompt ako správca a spustite:
```cmd
cd C:\subtitle-reader
install_x86.bat
```

### 3️⃣ Hotovo!
Po dokončení inštalácie sa aplikácia automaticky spustí.

## 🔧 Čo inštalátor robí

### Automaticky nainštaluje:
- ✅ **Python 3.11** (ak nie je nainštalovaný)
- ✅ **Virtuálne prostredie** (.venv)
- ✅ **Všetky Python závislosti** (PyQt6, pytesseract, pyttsx3, atď.)
- ✅ **Tesseract OCR v5.3.3** (ak nie je nainštalovaný)
- ✅ **Český jazyk pre OCR** (ces.traineddata)
- ✅ **Windows Runtime** (OneCore TTS)

### Otestuje funkcionality:
- 🐍 Python environment
- 🖥️ PyQt6 GUI framework
- 🔍 Tesseract OCR
- 🔊 Text-to-Speech engines
- 🎤 OneCore TTS hlasy

## 🎮 Používanie aplikácie

### Spustenie
Po inštalácii môžete aplikáciu spustiť pomocou:
```cmd
run_app_x86.bat
```

### Základné nastavenie
1. **Vyberte oblasť** na obrazovke s titulkami
2. **Nastavte jazyk čítania** (čeština/slovenčina/angličtina)
3. **Vyberte TTS hlas** (SAPI alebo OneCore)
4. **Spustite čítanie** titulkov

### Klávesové skratky
- `Alt+X` - zapnúť/vypnúť čítanie titulkov
- `Alt+C` - čítanie označeného textu (inteligentné)
- `Ctrl+Alt+Ctrl+←/→` - hlasitosť
- `Ctrl+Alt+Ctrl+↑/↓` - rýchlosť reči

#### 🎯 Inteligentné čítanie (Alt+C):
- **Ak je text označený** → číta označený text
- **Ak nie je text označený a niečo sa číta** → zastaví čítanie
- **Ak nie je text označený a nič sa nečíta** → nič sa nestane

## 🧪 Testovanie inštalácie

Pre overenie správnej inštalácie spustite:
```cmd
python test_x86_installation.py
```

Test overí:
- ✅ Systémové informácie
- ✅ Python závislosti
- ✅ Windows Runtime
- ✅ Tesseract OCR
- ✅ TTS engines
- ✅ GUI framework
- ✅ Súbory aplikácie

## 🔧 Systémové požiadavky

### Minimálne:
- **Windows 10** (64-bit) alebo **Windows 11**
- **4GB RAM** (odporúčané 8GB+)
- **2GB voľného miesta** na disku
- **Internetové pripojenie** (pre inštaláciu)

### Podporované architektúry:
- ✅ **x86_64** (AMD64)
- ✅ **x86** (32-bit)
- ❌ ARM64 (použite pôvodný balíček)

## 🌍 Podporované jazyky

### OCR (Tesseract):
- ✅ **Čeština** (ces) - automaticky nainštalovaný
- ✅ **Angličtina** (eng) - predvolene dostupný
- ✅ **Slovenčina** (slk) - ak je dostupná
- 🔧 **Ďalšie jazyky** - možno pridať manuálne

### TTS hlasy:
- 🎤 **OneCore hlasy** - moderné, vysoká kvalita
- 🔊 **SAPI hlasy** - tradičné Windows hlasy
- 🌍 **Všetky nainštalované Windows hlasy**

### GUI jazyk:
- 🇨🇿 Čeština
- 🇸🇰 Slovenčina  
- 🇺🇸 Angličtina
- 🇩🇪 Nemčina
- 🇫🇷 Francúzština

## 🐛 Riešenie problémov

### Inštalácia zlyhala
1. **Spustite ako správca** - inštalátor potrebuje admin práva
2. **Skontrolujte internetové pripojenie** - potrebné pre sťahovanie
3. **Dočasne vypnite antivírus** - môže blokovať inštaláciu
4. **Uvoľnite miesto na disku** - potrebné minimálne 2GB

### Aplikácia sa nespustí
1. **Skontrolujte app.log** - obsahuje chybové hlášky
2. **Spustite test script**:
   ```cmd
   python test_x86_installation.py
   ```
3. **Overte virtuálne prostredie**:
   ```cmd
   .venv\Scripts\activate.bat
   python --version
   ```

### OCR nefunguje
1. **Overte Tesseract**:
   ```cmd
   tesseract --version
   tesseract --list-langs
   ```
2. **Skontrolujte cestu**:
   ```cmd
   echo %TESSERACT_CMD%
   ```
3. **Manuálna inštalácia Tesseract**:
   - Stiahnite z: https://github.com/UB-Mannheim/tesseract/releases
   - Nainštalujte do: `C:\Program Files\Tesseract-OCR\`

### TTS nefunguje
1. **Test SAPI hlasov**:
   ```python
   import pyttsx3
   engine = pyttsx3.init()
   voices = engine.getProperty('voices')
   print(f"SAPI hlasy: {len(voices)}")
   ```
2. **Test OneCore hlasov** - spustite test script
3. **Skontrolujte Windows hlasy** v Nastavenia → Čas a jazyk → Reč

## 📞 Podpora

### Log súbory:
- `app.log` - hlavný log aplikácie
- `test_x86.log` - log test scriptu
- `test_results_x86.txt` - výsledky testov

### Užitočné príkazy:
```cmd
# Kontrola Python verzie
python --version

# Kontrola nainštalovaných balíčkov
pip list

# Kontrola Tesseract
tesseract --version

# Test TTS
python -c "import pyttsx3; pyttsx3.init().say('test'); pyttsx3.init().runAndWait()"
```

## 🎉 Úspešná inštalácia

Po úspešnej inštalácii by ste mali vidieť:
```
========================================================
  🎉 INŠTALÁCIA DOKONČENÁ!
========================================================

🚀 Spúšťam Subtitle Reader aplikáciu...
```

**Aplikácia sa automaticky spustí a je pripravená na použitie!**

---

## 📋 Technické detaily

### Verzie závislostí:
- Python: 3.11+
- PyQt6: 6.5.0+
- Tesseract: 5.3.3+
- pytesseract: 0.3.10+
- pyttsx3: 2.90+

### Výkon na x86:
- **OCR spracovanie**: ~100-500ms
- **TTS generovanie**: ~300-1000ms
- **GUI odozva**: Natívna x86 rýchlosť
- **Pamäťová spotreba**: ~150-400MB

**Subtitle Reader je teraz pripravený na vašom x86 Windows systéme!** 🚀
