#!/usr/bin/env python3
"""
Test načítania hlasov v hlavnej aplikácii
"""

import sys
import logging
from typing import List, Dict, Any

def setup_logging():
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('app_voices_test.log', encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )

def test_language_manager_voices():
    """Test načítania hlasov cez LanguageManager"""
    print("🔍 Testovanie LanguageManager hlasov...")
    
    try:
        from language_manager import LanguageManager
        
        # Vytvoríme LanguageManager
        lang_manager = LanguageManager()
        
        # Spustíme detekciu jazykov
        lang_manager.detect_languages()
        
        # Získame dostupné jazyky
        available_languages = lang_manager.get_available_languages()
        
        print(f"✅ Dostupné jazyky:")
        for category, languages in available_languages.items():
            print(f"   📂 {category}: {sorted(languages)}")
        
        # Test TTS hlasov pre rôzne jazyky
        test_languages = ['en', 'sk', 'cs', 'de', 'fr']
        
        for lang in test_languages:
            print(f"\n🎤 Testovanie hlasov pre jazyk: {lang}")
            
            # Získame hlasy pre jazyk
            voices = lang_manager.get_voices_for_language(lang)
            
            if voices:
                print(f"   ✅ Nájdených {len(voices)} hlasov:")
                for voice in voices:
                    print(f"      🎵 {voice.get('name', 'Unknown')} ({voice.get('language', 'Unknown')}) - {voice.get('quality', 'standard')}")
            else:
                print(f"   ❌ Žiadne hlasy pre jazyk {lang}")
                
    except Exception as e:
        print(f"❌ LanguageManager test zlyhal: {e}")
        import traceback
        traceback.print_exc()

def test_tts_provider_directly():
    """Test priameho TTS providera"""
    print("\n" + "="*50)
    print("🔍 Testovanie TTS providera priamo...")

    try:
        from platform_loader import get_tts_function
        get_available_voices = get_tts_function('get_available_voices')
        set_voice = get_tts_function('set_voice')
        
        # Získame dostupné hlasy
        voices = get_available_voices()

        print(f"✅ TTS Manager nájdených {len(voices)} hlasov:")

        for i, voice in enumerate(voices):
            print(f"\n🎤 Hlas {i+1}:")
            print(f"   📝 Názov: {voice.get('name', 'Unknown')}")
            print(f"   🌍 Jazyk: {voice.get('language', 'Unknown')}")
            print(f"   👤 Pohlavie: {voice.get('gender', 'Unknown')}")
            print(f"   ⭐ Kvalita: {voice.get('quality', 'Unknown')}")

        # Test nastavenia hlasu
        if voices:
            test_voice = voices[0]
            print(f"\n🔧 Testovanie nastavenia hlasu: {test_voice.get('name')}")

            success = set_voice(test_voice.get('name', ''))
            if success:
                print(f"   ✅ Hlas úspešne nastavený")
            else:
                print(f"   ❌ Chyba pri nastavovaní hlasu")
                
    except Exception as e:
        print(f"❌ TTS Manager test zlyhal: {e}")
        import traceback
        traceback.print_exc()

def test_cross_platform_provider():
    """Test cross-platform TTS providera"""
    print("\n" + "="*50)
    print("🔍 Testovanie cross-platform TTS providera...")
    
    try:
        from tts_cross_platform import CrossPlatformTTSProvider
        
        # Vytvoríme cross-platform provider
        provider = CrossPlatformTTSProvider()
        
        # Získame dostupné hlasy
        voices = provider.get_available_voices()
        
        print(f"✅ Cross-platform provider nájdených {len(voices)} hlasov:")
        
        for i, voice in enumerate(voices):
            print(f"\n🎤 Hlas {i+1}:")
            print(f"   📝 Názov: {voice.get('name', 'Unknown')}")
            print(f"   🌍 Jazyk: {voice.get('language', 'Unknown')}")
            print(f"   👤 Pohlavie: {voice.get('gender', 'Unknown')}")
            print(f"   ⭐ Kvalita: {voice.get('quality', 'Unknown')}")
            print(f"   🏷️ Provider: {voice.get('provider', 'Unknown')}")
            
        # Test mapovania jazykov
        print(f"\n🗺️ Test mapovania jazykov:")
        test_locales = ['en-US', 'sk-SK', 'cs-CZ', 'de-DE', 'fr-FR']
        
        for locale in test_locales:
            voices_for_locale = [v for v in voices if v.get('language') == locale]
            print(f"   🌍 {locale}: {len(voices_for_locale)} hlasov")
            
    except Exception as e:
        print(f"❌ Cross-platform provider test zlyhal: {e}")
        import traceback
        traceback.print_exc()

def main():
    setup_logging()
    
    print("🚀 App Voices Test")
    print("=" * 60)
    
    # Test všetky komponenty
    test_language_manager_voices()
    test_tts_provider_directly()
    test_cross_platform_provider()
    
    print("\n" + "=" * 60)
    print("🎯 Test dokončený - skontrolujte app_voices_test.log")

if __name__ == "__main__":
    main()
