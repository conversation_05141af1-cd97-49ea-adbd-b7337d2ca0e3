#!/usr/bin/env python3
"""
Test Alt+C functionality manually
"""

import sys
import os
import logging

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import hotkey manager
from hotkey_manager import handle_read_selected_text

# Setup logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_alt_c():
    """Test Alt+C functionality manually"""
    print("🧪 Testing Alt+C functionality...")
    
    try:
        # Test the function directly
        print("📋 Calling handle_read_selected_text()...")
        handle_read_selected_text()
        print("✅ Function call completed")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_alt_c()
