#!/usr/bin/env python3
"""
Test importov pre diagnostiku problémov
"""

import sys
import traceback

def test_import(module_name, description=""):
    """Test importu modulu"""
    try:
        __import__(module_name)
        print(f"✅ {module_name} {description}")
        return True
    except Exception as e:
        print(f"❌ {module_name} {description}")
        print(f"   Chyba: {e}")
        if "--verbose" in sys.argv:
            traceback.print_exc()
        return False

def main():
    print("🔍 Testovanie importov pre Windows...")
    print()
    
    # Základné moduly
    test_import("sys", "- základný Python modul")
    test_import("os", "- operačný systém")
    test_import("logging", "- logovanie")
    test_import("platform", "- platforma")
    
    print()
    print("📦 GUI a závislosti:")
    test_import("PyQt6", "- GUI framework")
    test_import("PyQt6.QtWidgets", "- Qt widgety")
    test_import("PyQt6.QtCore", "- Qt core")
    
    print()
    print("🔧 OCR a TTS:")
    test_import("pytesseract", "- Tesseract OCR")
    test_import("PIL", "- Pillow obrázky")
    test_import("mss", "- screenshot")
    test_import("pyttsx3", "- Windows TTS")
    
    print()
    print("🖥️ Windows špecifické:")
    test_import("pywin32", "- Windows API")
    test_import("win32gui", "- Windows GUI API")
    test_import("WMI", "- Windows Management")
    
    print()
    print("🌐 Cross-platform moduly:")
    success = True
    success &= test_import("platform_utils", "- cross-platform utilities")
    
    if success:
        success &= test_import("platform_windows", "- Windows implementácie")
    
    print()
    print("📱 Aplikačné moduly:")
    test_import("common_config", "- konfigurácia")
    test_import("common_utils", "- utility funkcie")
    
    # Test problematických modulov
    print()
    print("⚠️ Problematické moduly:")
    test_import("hotkey_manager", "- globálne hotkeys")
    test_import("app_logic", "- aplikačná logika")
    test_import("ocr_core", "- OCR jadro")
    test_import("qt_gui", "- Qt GUI")
    
    print()
    print("🎯 Test dokončený!")

if __name__ == "__main__":
    main()
