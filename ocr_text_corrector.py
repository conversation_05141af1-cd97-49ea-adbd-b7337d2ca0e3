#!/usr/bin/env python3
"""
🔧 OCR Text Corrector - Inteligentná oprava častých OCR chýb
Špecializované na opravu chýbajúcich písmen na začiatku riadkov
"""

import re
import logging

class OCRTextCorrector:
    """Inteligentný korektor OCR chýb."""
    
    def __init__(self):
        """Inicializácia korektora."""
        self.logger = logging.getLogger(__name__)
        
        # 🔧 Časté OCR chyby na začiatku viet
        self.start_corrections = {
            # Pipe symbol namiesto I
            r'^(\s*)\|(\s+)': r'\1I\2',
            r'^(\s*)l(\s+)': r'\1I\2',  # malé l namiesto I
            r'^(\s*)1(\s+)': r'\1I\2',  # číslo 1 namiesto I
            
            # Iné časté chyby
            r'^(\s*)0(\s+)': r'\1O\2',  # nula namiesto O
            r'^(\s*)5(\s+)': r'\1S\2',  # päťka namiesto S
            r'^(\s*)8(\s+)': r'\1B\2',  # osmička namiesto B
        }
        
        # 🔧 Kontextové opravy pre anglické vety
        self.context_corrections = {
            # Typické začiatky anglických viet
            r'^(\s*)\|(\s+)(merely|just|only|simply|really|think|believe|know|see|hear|feel|want|need|have|am|was|will|would|could|should|can|may|might)': r'\1I\2\3',
            r'^(\s*)l(\s+)(merely|just|only|simply|really|think|believe|know|see|hear|feel|want|need|have|am|was|will|would|could|should|can|may|might)': r'\1I\2\3',
            r'^(\s*)1(\s+)(merely|just|only|simply|really|think|believe|know|see|hear|feel|want|need|have|am|was|will|would|could|should|can|may|might)': r'\1I\2\3',
            
            # Oprava "| heard" -> "I heard"
            r'^(\s*)\|(\s+)(heard|saw|said|told|asked|thought|felt|knew|went|came|did|had|was|were)': r'\1I\2\3',
            r'^(\s*)l(\s+)(heard|saw|said|told|asked|thought|felt|knew|went|came|did|had|was|were)': r'\1I\2\3',
            r'^(\s*)1(\s+)(heard|saw|said|told|asked|thought|felt|knew|went|came|did|had|was|were)': r'\1I\2\3',
        }
        
        # 🔧 Opravy v strede textu
        self.middle_corrections = {
            # Oprava častých slov
            r'\bthat\s+\|(\s+)': r'that I\1',
            r'\band\s+\|(\s+)': r'and I\1',
            r'\bbut\s+\|(\s+)': r'but I\1',
            r'\bso\s+\|(\s+)': r'so I\1',
            r'\bif\s+\|(\s+)': r'if I\1',
            r'\bwhen\s+\|(\s+)': r'when I\1',
            r'\bwhere\s+\|(\s+)': r'where I\1',
            r'\bwhy\s+\|(\s+)': r'why I\1',
            r'\bhow\s+\|(\s+)': r'how I\1',
        }
        
        # 🔧 Špeciálne opravy pre konkrétne prípady
        self.special_corrections = {
            # Konkrétny prípad z logu
            r'^\|(\s+)merely\s+say': r'I\1merely say',
            r'^l(\s+)merely\s+say': r'I\1merely say',
            r'^1(\s+)merely\s+say': r'I\1merely say',
            
            # Ďalšie časté prípady
            r'^\|(\s+)think\s+that': r'I\1think that',
            r'^\|(\s+)believe\s+that': r'I\1believe that',
            r'^\|(\s+)know\s+that': r'I\1know that',
            r'^\|(\s+)heard\s+that': r'I\1heard that',
        }
    
    def correct_text(self, text: str, source_language: str = None) -> str:
        """
        Opraví časté OCR chyby v texte - LEN PRE ANGLIČTINU.

        Args:
            text: Pôvodný text z OCR
            source_language: Zdrojový jazyk (napr. 'en', 'sk', 'cs')

        Returns:
            Opravený text
        """
        if not text or not isinstance(text, str):
            return text

        # 🚨 KOREKTOR FUNGUJE LEN PRE ANGLIČTINU
        if source_language and source_language.lower() not in ['en', 'eng', 'english']:
            self.logger.debug(f"🔧 OCR korektor preskočený pre jazyk: {source_language}")
            return text
        
        original_text = text
        corrected_text = text
        corrections_made = []
        
        try:
            # 1. Špeciálne opravy (najšpecifickejšie)
            for pattern, replacement in self.special_corrections.items():
                new_text = re.sub(pattern, replacement, corrected_text, flags=re.IGNORECASE | re.MULTILINE)
                if new_text != corrected_text:
                    corrections_made.append(f"Special: '{pattern}' -> '{replacement}'")
                    corrected_text = new_text
            
            # 2. Kontextové opravy
            for pattern, replacement in self.context_corrections.items():
                new_text = re.sub(pattern, replacement, corrected_text, flags=re.IGNORECASE | re.MULTILINE)
                if new_text != corrected_text:
                    corrections_made.append(f"Context: '{pattern}' -> '{replacement}'")
                    corrected_text = new_text
            
            # 3. Základné opravy na začiatku
            for pattern, replacement in self.start_corrections.items():
                new_text = re.sub(pattern, replacement, corrected_text, flags=re.MULTILINE)
                if new_text != corrected_text:
                    corrections_made.append(f"Start: '{pattern}' -> '{replacement}'")
                    corrected_text = new_text
            
            # 4. Opravy v strede textu
            for pattern, replacement in self.middle_corrections.items():
                new_text = re.sub(pattern, replacement, corrected_text, flags=re.IGNORECASE)
                if new_text != corrected_text:
                    corrections_made.append(f"Middle: '{pattern}' -> '{replacement}'")
                    corrected_text = new_text
            
            # Logovanie opráv
            if corrections_made:
                self.logger.info(f"🔧 OCR OPRAVY: '{original_text}' -> '{corrected_text}'")
                for correction in corrections_made:
                    self.logger.debug(f"   {correction}")
            
            return corrected_text
            
        except Exception as e:
            self.logger.error(f"❌ Chyba pri oprave OCR textu: {e}")
            return original_text
    
    def correct_line_start_I(self, text: str, source_language: str = None) -> str:
        """
        Špecializovaná oprava pre chýbajúce "I" na začiatku riadku - LEN PRE ANGLIČTINU.

        Args:
            text: Text na opravu
            source_language: Zdrojový jazyk (napr. 'en', 'sk', 'cs')

        Returns:
            Opravený text
        """
        if not text:
            return text

        # 🚨 KOREKTOR FUNGUJE LEN PRE ANGLIČTINU
        if source_language and source_language.lower() not in ['en', 'eng', 'english']:
            return text
        
        # Rozdelenie na riadky
        lines = text.split('\n')
        corrected_lines = []
        
        for line in lines:
            corrected_line = line
            
            # Kontrola či riadok začína pipe symbolom alebo podobným
            if re.match(r'^\s*[\|l1]\s+\w', line):
                # Skús identifikovať či ide o "I" na základe kontextu
                if re.match(r'^\s*[\|l1]\s+(merely|just|only|think|believe|know|heard|saw|said|am|was|will|would|could|should|can|may|might)', line, re.IGNORECASE):
                    corrected_line = re.sub(r'^(\s*)[\|l1](\s+)', r'\1I\2', line)
                    self.logger.info(f"🔧 I-OPRAVA: '{line}' -> '{corrected_line}'")
            
            corrected_lines.append(corrected_line)
        
        return '\n'.join(corrected_lines)

# Globálna inštancia korektora
ocr_corrector = OCRTextCorrector()

def correct_ocr_text(text: str, source_language: str = None) -> str:
    """
    Hlavná funkcia pre opravu OCR textu - LEN PRE ANGLIČTINU.

    Args:
        text: Text z OCR na opravu
        source_language: Zdrojový jazyk (napr. 'en', 'sk', 'cs')

    Returns:
        Opravený text
    """
    return ocr_corrector.correct_text(text, source_language)

def correct_missing_I(text: str, source_language: str = None) -> str:
    """
    Špecializovaná funkcia pre opravu chýbajúceho "I" - LEN PRE ANGLIČTINU.

    Args:
        text: Text na opravu
        source_language: Zdrojový jazyk (napr. 'en', 'sk', 'cs')

    Returns:
        Opravený text
    """
    return ocr_corrector.correct_line_start_I(text, source_language)

# Test funkcie
if __name__ == "__main__":
    # Test prípady
    test_cases = [
        "| merely say that the cause lies here at the clinic.",
        "| heard that you failed a medical student.",
        "l think that this is wrong.",
        "1 believe you are right.",
        "| am going to the store.",
        "but | think otherwise.",
        "and | heard the news.",
        "Normal text without errors.",
    ]
    
    print("🧪 Test OCR Text Corrector:")
    print("=" * 50)
    
    for test_text in test_cases:
        corrected = correct_ocr_text(test_text)
        if corrected != test_text:
            print(f"✅ '{test_text}' -> '{corrected}'")
        else:
            print(f"⚪ '{test_text}' (bez zmeny)")
    
    print("\n🧪 Test Missing I Corrector:")
    print("=" * 50)
    
    for test_text in test_cases:
        corrected = correct_missing_I(test_text)
        if corrected != test_text:
            print(f"✅ '{test_text}' -> '{corrected}'")
        else:
            print(f"⚪ '{test_text}' (bez zmeny)")
