import logging
import threading
import queue
import sys
import time
import subprocess
import os

import common_config as config
import ocr_core
import ocr_processing
import static_mode.static_logic
import dynamic_mode.dynamic_logic
import common_utils
from tts_manager import init_tts_worker, enqueue_tts_command, stop_tts_worker, set_tts_rate, set_tts_volume, stop_current_tts, speak_text, reset_tts_rate_to_original
from full_automatic_mode.full_automatic_logic import log_text_change_to_csv
from youtube_utils import check_youtube_context, check_youtube_context
import csv_logger
import subtitle_area_detector
from i18n_manager import get_tts_message
from license_manager import can_start_reading, start_reading_session, stop_reading_session

def unified_speech_processor_worker():
    """
    Centrálny spracovateľ pre všetky texty určené na čítanie.
    Beží v jednom vlákne a spracováva unified_speech_queue.
    """
    logging.info("Unified speech processor worker started.")
    while config.is_reading:
        try:
            item = config.unified_speech_queue.get(timeout=0.1)
            if item:
                speak_text(item['text'], item['cycle_id'])
                log_text_change_to_csv(item['cycle_id'], 'TTS_SPEAK', 1.0, '', item['text'], item['text'])
            config.unified_speech_queue.task_done()
        except queue.Empty:
            continue
        except Exception as e:
            logging.error(f"Chyba v unified_speech_processor: {e}")
            time.sleep(0.01)

    logging.info("Unified speech processor worker sa ukončuje.")

def ocr_dispatcher_worker():
    """
    Dispečer pre OCR dáta - číta surové OCR dáta z config.raw_ocr_queue
    a presmerováva ich na príslušnú spracovateľskú funkciu podľa aktuálneho režimu.
    """
    while config.is_reading:
        try:
            # Čaká na surové OCR dáta s veľmi krátkym timeoutom pre rýchlejšie spracovanie
            raw_ocr_data = config.raw_ocr_queue.get(timeout=0.01)

            # Logovanie surových dát pre neskoršiu analýzu
            raw_text_for_log = raw_ocr_data.get('text', '')
            cycle_id_for_log = raw_ocr_data.get('cycle_id', -1)
            # Použijeme formát, ktorý sa dá ľahko strojovo spracovať (napr. CSV)
            sanitized_text_for_log = raw_text_for_log.replace(';', '|').replace('\n', '\\n')
            logging.info(f"RAW_OCR_DATA;{cycle_id_for_log};{sanitized_text_for_log}")
            logging.info(f"[PERF_TRACE] CycleID={cycle_id_for_log}: Dispečer prijal text z raw_ocr_queue.")

            if raw_ocr_data:
                text = raw_ocr_data.get('text', '')
                cycle_id = raw_ocr_data.get('cycle_id', -1)

                # Presmerovanie na príslušnú spracovateľskú funkciu podľa režimu
                if config.reading_mode == 'static':
                    static_mode.static_logic.handle_text_comparison_static(text, cycle_id)
                elif config.reading_mode == 'dynamic':
                    dynamic_mode.dynamic_logic.handle_text_comparison_dynamic(text, cycle_id)
                elif config.reading_mode == 'automatic':
                    import automatic_mode.automatic_logic
                    automatic_mode.automatic_logic.handle_automatic_detection(raw_ocr_data)
                elif config.reading_mode == 'full_automatic':
                    logging.debug(f"[DISPATCHER] Volám full_automatic process_text s textom: '{text}', CycleID={cycle_id}")
                    import full_automatic_mode.full_automatic_logic
                    full_automatic_mode.full_automatic_logic.process_text(raw_ocr_data)
                else:
                    logging.warning(f"Neznámy reading_mode: {config.reading_mode}")

                config.raw_ocr_queue.task_done()

        except queue.Empty:
            continue
        except Exception as e:
            logging.error(f"Chyba v ocr_dispatcher_worker: {e}")
            time.sleep(0.1)

    logging.info("Vlákno ocr_dispatcher_worker sa ukončuje.")

def start_reading():
    """Spustí proces čítania (OCR alebo YouTube synchronizácia)."""
    if config.is_reading:
        return

    # Kontrola licencie/demo času
    if not can_start_reading():
        logging.warning("⚠️ Nemožno spustiť čítanie - demo čas vypršal alebo licencia neplatná")
        if config.gui_instance and hasattr(config.gui_instance, 'show_warning_dialog_threadsafe'):
            # 🔧 OPRAVA: Použij thread-safe dialog
            config.gui_instance.show_warning_dialog_threadsafe(
                "Demo čas vypršal",
                "Váš denný demo čas vypršal.\n\n"
                "Pre pokračovanie v používaní si zakúpte plnú verziu na rok.\n\n"
                "Demo čas sa obnoví zajtra."
            )
        return

    # Spustenie licenčnej session
    if not start_reading_session():
        logging.error("❌ Nepodarilo sa spustiť reading session")
        return

    # Synchronizácia režimu s GUI pred spustením
    if config.gui_instance:
        # Kontrola, či mode_var existuje
        if hasattr(config.gui_instance, 'mode_var'):
            gui_mode = config.gui_instance.mode_var.get()
            logging.info(f"[DEBUG] Kontrola režimov: config.reading_mode='{config.reading_mode}', gui_mode='{gui_mode}'")
            if config.reading_mode != gui_mode:
                logging.info(f"Synchronizujem režim z GUI: '{config.reading_mode}' → '{gui_mode}'")
                config.reading_mode = gui_mode
            else:
                logging.info(f"Režimy sú synchronizované: '{config.reading_mode}'")
        else:
            logging.warning("⚠️ mode_var nie je inicializovaný, používam config.reading_mode")

    config.is_reading = True
    config.status_message = "Čítanie spustené..."
    logging.info("Reading started.")
    stop_current_tts() # Zastaví akékoľvek prebiehajúce TTS
    time.sleep(0.1) # Dáme TTS workeru čas na spracovanie 'stop' a vyčistenie fronty

    # 🔧 REMOVED: Vyčistenie TTS histórie - spôsobovalo preskakovanie textov
    # História sa teraz zachováva medzi čítaniami pre lepšiu kontinuitu

    # Reset TTS rýchlosti na pôvodnú hodnotu pri štarte nového čítania
    reset_tts_rate_to_original()

    # Nastavenie intervalu OCR podľa režimu
    if config.reading_mode == "automatic":
        config.current_ocr_interval = config.DETECTION_OCR_INTERVAL
        logging.info(f"Nastavený pomalý detekčný interval OCR: {config.current_ocr_interval}s")
        tts_msg = get_tts_message("tts_detecting_subtitles")
        speak_text(tts_msg)
        log_text_change_to_csv(None, 'TTS_SPEAK', 1.0, '', tts_msg, tts_msg)
        # Spustenie automatickej detekcie
        import automatic_mode.automatic_logic
        automatic_mode.automatic_logic.start_automatic_detection_processing()
    elif config.reading_mode == "full_automatic":
        config.current_ocr_interval = config.DETECTION_OCR_INTERVAL
        logging.info(f"Nastavený rýchly čítací interval OCR pre full automatic: {config.current_ocr_interval}s")
        tts_msg = get_tts_message("tts_starting_continuous")
        speak_text(tts_msg)
        log_text_change_to_csv(None, 'TTS_SPEAK', 1.0, '', tts_msg, tts_msg)
        # Spustenie full automatic detekcie
        import full_automatic_mode.full_automatic_logic
        full_automatic_mode.full_automatic_logic.start_full_automatic_detection_processing()
    else:
        config.current_ocr_interval = config.READING_OCR_INTERVAL
        logging.info(f"Nastavený rýchly čítací interval OCR: {config.current_ocr_interval}s")
        tts_msg = get_tts_message("tts_starting_reading")
        speak_text(tts_msg)
        log_text_change_to_csv(None, 'TTS_SPEAK', 1.0, '', tts_msg, tts_msg)

    # Spustenie OCR alebo YouTube synchronizácie v závislosti od režimu
    if config.reading_mode == "static":
        config.current_ocr_interval = 0.2
        logging.info(f"Nastavený pomalší interval OCR pre statický režim: {config.current_ocr_interval}s")

    if config.reading_mode in ["static", "dynamic", "automatic", "full_automatic"]:
        # Spustenie OCR v samostatnom vlákne
        config.ocr_thread = threading.Thread(target=ocr_processing.perform_ocr_and_enqueue, daemon=True)
        config.ocr_thread.start()
        logging.info(f"{config.reading_mode.capitalize()} reading mode activated. OCR thread started.")

        # Spustenie OCR dispečera
        config.ocr_dispatcher_thread = threading.Thread(target=ocr_dispatcher_worker, daemon=True)
        config.ocr_dispatcher_thread.start()
        logging.info("OCR dispatcher thread started.")

        # Spustenie centrálneho vlákna pre spracovanie reči (pre všetky režimy)
        config.unified_speech_processor_thread = threading.Thread(target=unified_speech_processor_worker, daemon=True)
        config.unified_speech_processor_thread.start()
        logging.info("Unified speech processor thread started.")

        if config.reading_mode == "dynamic":
            # Reset premenných pre dynamický režim
            dynamic_mode.dynamic_logic.start_dynamic_mode_processing()
        elif config.reading_mode == "static":
            # Reset premenných pre statický režim
            static_mode.static_logic.start_static_mode_processing()

    elif config.reading_mode == "youtube":
        # Spustenie YouTube synchronizácie
        config.context_timer = threading.Timer(config.CONTEXT_CHECK_INTERVAL, check_youtube_context)
        config.context_timer.daemon = True
        config.context_timer.start()
        logging.info("YouTube reading mode activated. Context timer started.")

    logging.info("TTS processor is handled by a dedicated worker thread.")

    if config.gui_instance:
        config.gui_instance.update_status(config.status_message)
        config.gui_instance.update_start_button_text()


def stop_reading():
    """Zastaví proces čítania."""
    if not config.is_reading:
        return

    config.is_reading = False
    config.status_message = "Čítanie zastavené."

    # Ukončenie licenčnej session
    stop_reading_session()

    logging.info("Reading stopped.")

    # Zastavenie časovačov
    if config.ocr_timer:
        config.ocr_timer.cancel()
        config.ocr_timer = None
    if config.context_timer:
        config.context_timer.cancel()
        config.context_timer = None

    # Ukončenie vlákien podľa režimu
    if config.ocr_dispatcher_thread and config.ocr_dispatcher_thread.is_alive():
        config.ocr_dispatcher_thread.join(timeout=1.0)
        if config.ocr_dispatcher_thread.is_alive():
            logging.warning("OCR dispatcher thread sa neukončil včas.")
        config.ocr_dispatcher_thread = None


    # Reset GUI update queue (ak existuje)
    try:
        while not config.gui_update_queue.empty():
            config.gui_update_queue.get_nowait()
    except Exception:
        pass

    # Reset detekcie ľavej strany dynamických titulkov
    config.dynamic_subtitle_left_x = None
    config.dynamic_subtitle_detection_active = False
    config.dynamic_subtitle_detection_samples = []

    # Zastavenie centrálneho vlákna pre spracovanie reči
    if config.unified_speech_processor_thread and config.unified_speech_processor_thread.is_alive():
        config.unified_speech_processor_thread.join(timeout=1.0)
        if config.unified_speech_processor_thread.is_alive():
            logging.warning("Unified speech processor thread sa neukončil včas.")
        config.unified_speech_processor_thread = None

    if config.reading_mode == 'dynamic':
        dynamic_mode.dynamic_logic.stop_dynamic_mode_processing()
    elif config.reading_mode == 'static':
        static_mode.static_logic.stop_static_mode_processing()
    elif config.reading_mode == 'automatic':
        import automatic_mode.automatic_logic
        automatic_mode.automatic_logic.stop_automatic_detection_processing()
    elif config.reading_mode == 'full_automatic':
        import full_automatic_mode.full_automatic_logic
        full_automatic_mode.full_automatic_logic.stop_full_automatic_detection_processing()

    # Vyprázdnenie všetkých front
    for q in [config.unified_speech_queue, config.raw_ocr_queue, config.youtube_tts_queue, config.automatic_detection_queue, config.full_automatic_detection_queue, config.tts_command_queue]:
        while not q.empty():
            try:
                q.get_nowait()
            except queue.Empty:
                break

    # Zastavíme akékoľvek prebiehajúce TTS a vyčistíme frontu TTS príkazov
    # Toto je dôležité, aby sa predišlo prekrývaniu hlášok
    stop_current_tts()
    time.sleep(0.1) # Krátka pauza, aby sa 'say' proces stihol ukončiť

    # Reset TTS rýchlosti na pôvodnú hodnotu po zastavení čítania
    reset_tts_rate_to_original()

    # Teraz zaradíme záverečnú hlášku a dáme jej čas na prehranie
    tts_msg = get_tts_message("tts_ending_reading")
    speak_text(tts_msg)
    log_text_change_to_csv(None, 'TTS_SPEAK', 1.0, '', tts_msg, tts_msg)
    time.sleep(2.0) # Počkáme 2 sekundy, aby sa hláška prečítala

    if config.gui_instance:
        config.gui_instance.update_status(config.status_message)
        config.gui_instance.update_start_button_text()

def toggle_reading():
    """Prepína stav čítania medzi spusteným a zastaveným."""
    logging.info(f"🔄 TOGGLE_READING: Aktuálny stav is_reading={config.is_reading}")
    if config.is_reading:
        logging.info("🔄 TOGGLE_READING: Zastavujem čítanie...")
        stop_reading()
    else:
        logging.info("🔄 TOGGLE_READING: Spúšťam čítanie...")
        start_reading()

def on_closing():
    """Funkcia volaná pri zatváraní okna GUI."""
    logging.info("Closing application.")
    stop_reading()
    stop_tts_worker()

    # Vyčistenie klávesových skratiek
    try:
        from hotkey_manager import cleanup_hotkeys
        cleanup_hotkeys()
    except Exception as e:
        logging.warning(f"Chyba pri čistení hotkeys: {e}")

    # Žiadne špeciálne čistenie nie je potrebné

    if config.gui_instance:
        config.gui_instance.destroy()
    sys.exit(0)

def set_reading_mode(mode):
    """Nastaví režim čítania (static/youtube/dynamic/automatic/full_automatic)."""
    config.MANUAL_TTS_RATE_ADJUSTMENT = False
    if mode not in ["static", "youtube", "dynamic", "automatic", "full_automatic"]:
        logging.error(f"Neplatný režim čítania: {mode}")
        return

    if config.reading_mode != mode:
        old_mode = config.reading_mode
        logging.info(f"Mení sa režim čítania z '{old_mode}' na '{mode}'.")
        csv_logger.log_mode_change(old_mode, mode, details=f"Manuálna zmena režimu")
        stop_reading() # Zastaví aktuálne čítanie pred zmenou režimu

        # Reset detekcie ľavej strany dynamických titulkov pri zmene režimu
        if hasattr(config, '_dynamic_mode_initialized'):
            config._dynamic_mode_initialized = False
        config.dynamic_subtitle_left_x = None
        config.dynamic_subtitle_detection_active = False
        config.dynamic_subtitle_detection_samples = []

        config.reading_mode = mode

        # Špecifické nastavenia pre automatický režim
        if mode == "automatic":
            # Resetovanie premenných automatického režimu sa vykoná v start_automatic_detection_processing()
            config.gui_instance.update_status("Automatický režim: Pripravený na detekciu.")
        elif mode == "full_automatic":
            # Resetovanie premenných full automatic režimu sa vykoná v start_full_automatic_detection_processing()
            config.gui_instance.update_status("Full Automatic režim: Pripravený na kontinuálnu detekciu.")
        else:
            config.gui_instance.update_status(f"Režim zmenený na: {mode.capitalize()}")

        # 🔧 REMOVED: Vyčistenie TTS histórie - zachováva kontinuitu medzi režimami
        # Reset špecifických premenných pre jednotlivé režimy
        if mode == "youtube":
            config.current_youtube_url = ""
            config.subtitles = []
            config.last_subtitle_index = 0
            config.has_ocr_sync = False
            config.last_enqueued_youtube_text = ""
            config.is_youtube_no_video_page = False
            config.failed_subtitle_urls = set()
            config.expected_ocr_video_url = "" # Reset pre istotu
        # Premenné pre statický a dynamický režim sa resetujú v ich vlastných stop funkciách

    if config.gui_instance:
        config.gui_instance.update_mode_buttons(mode)
