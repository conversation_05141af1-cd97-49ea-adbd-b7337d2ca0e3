#!/usr/bin/env python3
"""
Oprava syntax error v full_auto_stability_detector.py
"""

def fix_syntax():
    file_path = "full_automatic_mode/full_auto_stability_detector.py"
    
    # <PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>bor
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # Nájdi problematické riadky a odstráň ich
    new_lines = []
    skip_lines = False
    
    for i, line in enumerate(lines):
        line_num = i + 1
        
        # Začni preskakovať od riadku 243
        if line_num == 243 and line.strip() == "":
            skip_lines = True
            continue
            
        # Preskakuj až po riadok 267
        if skip_lines and line_num <= 267:
            continue
            
        # <PERSON><PERSON><PERSON> preska<PERSON>
        if line_num == 268:
            skip_lines = False
            
        new_lines.append(line)
    
    # <PERSON><PERSON><PERSON><PERSON> opravený súbor
    with open(file_path, 'w', encoding='utf-8') as f:
        f.writelines(new_lines)
    
    print(f"Syntax error opravený v {file_path}")

if __name__ == "__main__":
    fix_syntax()
