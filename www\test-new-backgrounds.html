<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test nových pozadí - VOXO LOXO</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        /* Podporované platformy sekcia s overlay.png */
        .platforms-section {
            background: url('overlay.png') center/cover no-repeat;
            position: relative;
            min-height: 500px;
            color: white;
        }

        /* Download sekcia s download.png */
        .download-section {
            background:
                linear-gradient(rgba(248, 249, 250, 0.1), rgba(248, 249, 250, 0.1)),
                url('download.png') center/cover no-repeat;
            position: relative;
            min-height: 600px;
        }
        
        /* Fallback pre chýbajúce obrázky */
        .platforms-section.no-image {
            background:
                linear-gradient(135deg, #343a40 0%, #495057 100%),
                repeating-linear-gradient(
                    45deg,
                    transparent,
                    transparent 20px,
                    rgba(255,255,255,0.1) 20px,
                    rgba(255,255,255,0.1) 40px
                );
            color: white;
        }
        
        .download-section.no-image {
            background: 
                linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%),
                repeating-linear-gradient(
                    -45deg,
                    transparent,
                    transparent 15px,
                    rgba(0,123,255,0.1) 15px,
                    rgba(0,123,255,0.1) 30px
                );
        }
        
        /* OS detekcia styling */
        .platform-card {
            transition: all 0.3s ease;
            border-radius: 10px;
            padding: 20px;
            margin: 10px 0;
            background: rgba(128, 128, 128, 0.5);
            border: 2px solid transparent;
            color: white;
        }

        .download-card {
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .current-os-windows {
            border: 3px solid #007bff !important;
            box-shadow: 0 0 20px rgba(0, 123, 255, 0.4) !important;
            transform: scale(1.02) !important;
        }

        .current-os-macos {
            border: 3px solid #28a745 !important;
            box-shadow: 0 0 20px rgba(40, 167, 69, 0.4) !important;
            transform: scale(1.02) !important;
        }

        .current-os-linux {
            border: 3px solid #ffc107 !important;
            box-shadow: 0 0 20px rgba(255, 193, 7, 0.4) !important;
            transform: scale(1.02) !important;
        }

        /* Platform cards zvýraznenie - OS farby */
        .platform-card.current-os-windows {
            background: rgba(0, 123, 255, 0.7) !important;
            color: white !important;
        }

        .platform-card.current-os-macos {
            background: rgba(40, 167, 69, 0.7) !important;
            color: white !important;
        }

        .platform-card.current-os-linux {
            background: rgba(255, 193, 7, 0.7) !important;
            color: white !important;
        }

        /* Download cards zvýraznenie - 70% priehľadnosť + biele texty */
        .download-card.current-os-windows {
            background: rgba(0, 123, 255, 0.7) !important;
        }

        .download-card.current-os-windows .card-body,
        .download-card.current-os-windows .card-body h4,
        .download-card.current-os-windows .card-body p,
        .download-card.current-os-windows .card-body small {
            color: white !important;
        }

        .download-card.current-os-macos {
            background: rgba(40, 167, 69, 0.7) !important;
        }

        .download-card.current-os-macos .card-body,
        .download-card.current-os-macos .card-body h4,
        .download-card.current-os-macos .card-body p,
        .download-card.current-os-macos .card-body small {
            color: white !important;
        }

        .download-card.current-os-linux {
            background: rgba(255, 193, 7, 0.7) !important;
        }

        .download-card.current-os-linux .card-body,
        .download-card.current-os-linux .card-body h4,
        .download-card.current-os-linux .card-body p,
        .download-card.current-os-linux .card-body small {
            color: white !important;
        }
        
        .os-indicator {
            position: absolute;
            top: -10px;
            right: -10px;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        /* OS-specific indicator colors */
        .current-os-windows .os-indicator {
            background: #007bff;
        }

        .current-os-macos .os-indicator {
            background: #28a745;
        }

        .current-os-linux .os-indicator {
            background: #ffc107;
        }

        /* OS-specific alert colors */
        .os-alert-windows {
            background-color: #007bff !important;
            border-color: #0056b3 !important;
            color: white !important;
        }

        .os-alert-macos {
            background-color: #28a745 !important;
            border-color: #1e7e34 !important;
            color: white !important;
        }

        .os-alert-linux {
            background-color: #ffc107 !important;
            border-color: #e0a800 !important;
            color: white !important;
        }
        
        .test-controls {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 20px;
            border-radius: 10px;
            z-index: 1000;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-success { background: #28a745; }
        .status-warning { background: #ffc107; }
        .status-error { background: #dc3545; }

        /* iOS Safari fix pre všetky pozadia */
        @supports (-webkit-touch-callout: none) {
            .platforms-section,
            .download-section {
                background-attachment: scroll;
            }
        }

        @media screen and (-webkit-min-device-pixel-ratio: 2) {
            .platforms-section,
            .download-section {
                background-attachment: scroll !important;
            }
        }
    </style>
</head>
<body>
    <!-- Test ovládanie -->
    <div class="test-controls">
        <h6><i class="fas fa-cog"></i> Test ovládanie</h6>
        <div class="mb-2">
            <small>Overlay.png:</small>
            <span class="status-indicator status-success"></span>
            <span id="overlayStatus">Načítava sa...</span>
            <button class="btn btn-sm btn-outline-light ms-2" onclick="toggleOverlay()">Toggle</button>
        </div>
        <div class="mb-2">
            <small>Download.png:</small>
            <span class="status-indicator status-success"></span>
            <span id="downloadStatus">Načítava sa...</span>
            <button class="btn btn-sm btn-outline-light ms-2" onclick="toggleDownload()">Toggle</button>
        </div>
        <div class="mb-2">
            <small>OS detekcia:</small>
            <span id="osDetection">Detekuje sa...</span>
            <button class="btn btn-sm btn-outline-light ms-2" onclick="highlightCurrentOS()">Refresh</button>
        </div>
    </div>

    <!-- Platforms Section s overlay.png pozadím -->
    <section class="platforms-section py-5">
        <div class="container text-center">
            <h2 class="mb-5">🖥️ Podporované platformy</h2>
            <p class="lead mb-5">Test overlay.png pozadia s OS detekciou</p>
            <div class="row">
                <div class="col-md-4 text-center mb-4">
                    <div class="platform-card" data-os="windows">
                        <i class="fab fa-windows" style="font-size: 4rem; margin-bottom: 1rem;"></i>
                        <h4>Windows 10+</h4>
                        <ul class="list-unstyled text-center">
                            <li><i class="fas fa-check"></i> Tesseract OCR</li>
                            <li><i class="fas fa-check"></i> Windows SAPI TTS</li>
                            <li><i class="fas fa-check"></i> WMI integrace</li>
                            <li><i class="fas fa-check"></i> Ctrl+Alt+Ctrl hotkeys</li>
                        </ul>
                    </div>
                </div>
                <div class="col-md-4 text-center mb-4">
                    <div class="platform-card" data-os="macos">
                        <i class="fab fa-apple" style="font-size: 4rem; margin-bottom: 1rem;"></i>
                        <h4>macOS 10.15+</h4>
                        <ul class="list-unstyled text-center">
                            <li><i class="fas fa-check"></i> Tesseract OCR</li>
                            <li><i class="fas fa-check"></i> Apple TTS (Enhanced/Premium)</li>
                            <li><i class="fas fa-check"></i> AppKit integrace</li>
                            <li><i class="fas fa-check"></i> Cmd+Opt+Cmd hotkeys</li>
                        </ul>
                    </div>
                </div>
                <div class="col-md-4 text-center mb-4">
                    <div class="platform-card" data-os="linux">
                        <i class="fab fa-linux" style="font-size: 4rem; margin-bottom: 1rem;"></i>
                        <h4>Linux (Ubuntu 20.04+)</h4>
                        <ul class="list-unstyled text-center">
                            <li><i class="fas fa-check"></i> Tesseract OCR</li>
                            <li><i class="fas fa-check"></i> espeak/pyttsx3 TTS</li>
                            <li><i class="fas fa-check"></i> X11 integrace</li>
                            <li><i class="fas fa-check"></i> Ctrl+Alt+Ctrl hotkeys</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Download Section s download.png pozadím -->
    <section class="download-section py-5">
        <div class="container text-center">
            <h2 class="mb-4">📥 Stáhnout aplikaci</h2>
            <p class="lead mb-4">Test download.png pozadia s OS detekciou</p>
            <div class="row justify-content-center">
                <div class="col-md-4 mb-4">
                    <div class="card h-100 download-card" data-os="windows">
                        <div class="card-body">
                            <h4><i class="fab fa-windows text-primary"></i> Windows</h4>
                            <p>VOXO LOXO v2.0.0</p>
                            <a href="#" class="btn btn-primary btn-lg mb-2">
                                <i class="fas fa-download"></i> Stáhnout ZIP
                            </a>
                            <p class="text-muted">
                                <small>Windows 10+<br>
                                Obsahuje setup_windows.bat</small>
                            </p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="card h-100 border-success download-card" data-os="macos">
                        <div class="card-body">
                            <h4><i class="fab fa-apple text-success"></i> macOS</h4>
                            <p>VOXO LOXO v2.0.0</p>
                            <a href="#" class="btn btn-success btn-lg mb-2">
                                <i class="fas fa-download"></i> Stáhnout DMG
                            </a>
                            <p class="text-muted">
                                <small>macOS 10.15+, M1/M2 optimalizace<br>
                                Plne funkčná verzia</small>
                            </p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="card h-100 download-card" data-os="linux">
                        <div class="card-body">
                            <h4><i class="fab fa-linux text-warning"></i> Linux</h4>
                            <p>VOXO LOXO v2.0.0</p>
                            <a href="#" class="btn btn-warning btn-lg mb-2">
                                <i class="fas fa-download"></i> Stáhnout TAR.GZ
                            </a>
                            <p class="text-muted">
                                <small>Ubuntu 20.04+<br>
                                Plná funkcionalita</small>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // OS detekcia
        function detectOS() {
            const userAgent = navigator.userAgent.toLowerCase();
            const platform = navigator.platform.toLowerCase();

            if (userAgent.includes('win') || platform.includes('win')) {
                return 'windows';
            } else if (userAgent.includes('mac') || platform.includes('mac')) {
                return 'macos';
            } else if (userAgent.includes('linux') || platform.includes('linux') || userAgent.includes('x11')) {
                return 'linux';
            } else {
                return 'unknown';
            }
        }

        // Zvýraznenie aktuálneho OS
        function highlightCurrentOS() {
            const currentOS = detectOS();
            
            // Aktualizuj status
            document.getElementById('osDetection').textContent = currentOS || 'unknown';
            
            if (currentOS === 'unknown') return;
            
            // Vyčisti predchádzajúce zvýraznenia
            document.querySelectorAll('.platform-card, .download-card').forEach(card => {
                card.classList.remove('current-os-windows', 'current-os-macos', 'current-os-linux');
                const indicator = card.querySelector('.os-indicator');
                if (indicator) indicator.remove();
            });
            
            // Zvýrazni platform karty
            const platformCards = document.querySelectorAll('.platform-card');
            platformCards.forEach(card => {
                if (card.dataset.os === currentOS) {
                    card.classList.add(`current-os-${currentOS}`);
                    card.style.position = 'relative';
                    
                    const indicator = document.createElement('div');
                    indicator.className = 'os-indicator';
                    indicator.innerHTML = '✓';
                    indicator.title = 'Váš operačný systém';
                    card.appendChild(indicator);
                }
            });
            
            // Zvýrazni download karty
            const downloadCards = document.querySelectorAll('.download-card');
            downloadCards.forEach(card => {
                if (card.dataset.os === currentOS) {
                    card.classList.add(`current-os-${currentOS}`);
                    card.style.position = 'relative';
                    
                    const indicator = document.createElement('div');
                    indicator.className = 'os-indicator';
                    indicator.innerHTML = '✓';
                    indicator.title = 'Odporúčané pre váš OS';
                    card.appendChild(indicator);
                    
                    const recommendation = document.createElement('div');
                    recommendation.className = `alert os-alert-${currentOS} mt-2 mb-0`;
                    recommendation.innerHTML = `
                        <i class="fas fa-check-circle me-2"></i>
                        <strong>Odporúčané pre váš systém</strong>
                    `;
                    card.querySelector('.card-body').appendChild(recommendation);
                }
            });
        }

        // Test funkcie
        function checkImageExists(imagePath, statusId) {
            const img = new Image();
            img.onload = function() {
                const status = document.getElementById(statusId);
                const indicator = status.previousElementSibling;
                status.textContent = 'Obrázok načítaný úspešne';
                indicator.className = 'status-indicator status-success';
            };
            img.onerror = function() {
                const status = document.getElementById(statusId);
                const indicator = status.previousElementSibling;
                status.textContent = 'Obrázok nenájdený - použitý fallback';
                indicator.className = 'status-indicator status-warning';
                
                if (imagePath === 'overlay.png') {
                    document.querySelector('.platforms-section').classList.add('no-image');
                } else if (imagePath === 'download.png') {
                    document.querySelector('.download-section').classList.add('no-image');
                }
            };
            img.src = imagePath;
        }

        function toggleOverlay() {
            const section = document.querySelector('.platforms-section');
            section.classList.toggle('no-image');
            
            const status = document.getElementById('overlayStatus');
            const indicator = status.previousElementSibling;
            
            if (section.classList.contains('no-image')) {
                status.textContent = 'Fallback režim aktivovaný';
                indicator.className = 'status-indicator status-warning';
            } else {
                status.textContent = 'Obrázok overlay.png načítaný';
                indicator.className = 'status-indicator status-success';
            }
        }

        function toggleDownload() {
            const section = document.querySelector('.download-section');
            section.classList.toggle('no-image');
            
            const status = document.getElementById('downloadStatus');
            const indicator = status.previousElementSibling;
            
            if (section.classList.contains('no-image')) {
                status.textContent = 'Fallback režim aktivovaný';
                indicator.className = 'status-indicator status-warning';
            } else {
                status.textContent = 'Obrázok download.png načítaný';
                indicator.className = 'status-indicator status-success';
            }
        }

        // Inicializácia
        document.addEventListener('DOMContentLoaded', function() {
            checkImageExists('overlay.png', 'overlayStatus');
            checkImageExists('download.png', 'downloadStatus');
            highlightCurrentOS();
        });
    </script>
</body>
</html>
