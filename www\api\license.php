<?php
/**
 * License API pro Subtitle Reader
 * Spravuje aktiváciu a overovanie licencií
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Databázové pripojenie
require_once '../config/database.php';

// Spracovanie OPTIONS request pre CORS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Len POST requesty
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit();
}

// Načítanie JSON dát
$input = json_decode(file_get_contents('php://input'), true);
if (!$input) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'Invalid JSON']);
    exit();
}

$action = $input['action'] ?? '';

try {
    switch ($action) {
        case 'activate':
            handleActivation($input);
            break;
        case 'verify':
            handleVerification($input);
            break;
        case 'deactivate':
            handleDeactivation($input);
            break;
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => 'Unknown action']);
    }
} catch (Exception $e) {
    error_log("License API Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Internal server error']);
}

function handleActivation($input) {
    global $pdo;
    
    $license_key = $input['license_key'] ?? '';
    $hardware_id = $input['hardware_id'] ?? '';
    $user_email = $input['user_email'] ?? '';
    
    if (!$license_key || !$hardware_id || !$user_email) {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => 'Missing required fields']);
        return;
    }
    
    // Kontrola existencie licencie
    $stmt = $pdo->prepare("
        SELECT l.*, u.email 
        FROM licenses l 
        JOIN users u ON l.user_id = u.id 
        WHERE l.license_key = ? AND l.status = 'active'
    ");
    $stmt->execute([$license_key]);
    $license = $stmt->fetch();
    
    if (!$license) {
        echo json_encode(['success' => false, 'error' => 'Invalid or expired license']);
        return;
    }
    
    // Kontrola, či licencia nie je už aktivovaná na inom počítači
    if ($license['hardware_id'] && $license['hardware_id'] !== $hardware_id) {
        echo json_encode(['success' => false, 'error' => 'License already activated on another computer']);
        return;
    }
    
    // Kontrola emailu
    if ($license['email'] !== $user_email) {
        echo json_encode(['success' => false, 'error' => 'Email does not match license']);
        return;
    }
    
    // Aktivácia licencie
    $stmt = $pdo->prepare("
        UPDATE licenses 
        SET hardware_id = ?, activated_at = NOW(), last_verified = NOW() 
        WHERE license_key = ?
    ");
    $stmt->execute([$hardware_id, $license_key]);
    
    // Log aktivácie
    $stmt = $pdo->prepare("
        INSERT INTO license_logs (license_id, action, hardware_id, ip_address, created_at) 
        VALUES (?, 'activate', ?, ?, NOW())
    ");
    $stmt->execute([$license['id'], $hardware_id, $_SERVER['REMOTE_ADDR']]);
    
    echo json_encode([
        'success' => true,
        'expires_at' => $license['expires_at'],
        'purchase_id' => $license['purchase_id']
    ]);
}

function handleVerification($input) {
    global $pdo;
    
    $license_key = $input['license_key'] ?? '';
    $hardware_id = $input['hardware_id'] ?? '';
    
    if (!$license_key || !$hardware_id) {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => 'Missing required fields']);
        return;
    }
    
    // Kontrola licencie
    $stmt = $pdo->prepare("
        SELECT * FROM licenses 
        WHERE license_key = ? AND hardware_id = ? AND status = 'active' AND expires_at > NOW()
    ");
    $stmt->execute([$license_key, $hardware_id]);
    $license = $stmt->fetch();
    
    if (!$license) {
        echo json_encode(['success' => false, 'error' => 'License not found or expired']);
        return;
    }
    
    // Aktualizácia posledného overenia
    $stmt = $pdo->prepare("UPDATE licenses SET last_verified = NOW() WHERE id = ?");
    $stmt->execute([$license['id']]);
    
    // Log overenia
    $stmt = $pdo->prepare("
        INSERT INTO license_logs (license_id, action, hardware_id, ip_address, created_at) 
        VALUES (?, 'verify', ?, ?, NOW())
    ");
    $stmt->execute([$license['id'], $hardware_id, $_SERVER['REMOTE_ADDR']]);
    
    echo json_encode([
        'success' => true,
        'expires_at' => $license['expires_at'],
        'days_remaining' => ceil((strtotime($license['expires_at']) - time()) / 86400)
    ]);
}

function handleDeactivation($input) {
    global $pdo;
    
    $license_key = $input['license_key'] ?? '';
    $hardware_id = $input['hardware_id'] ?? '';
    
    if (!$license_key || !$hardware_id) {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => 'Missing required fields']);
        return;
    }
    
    // Deaktivácia licencie
    $stmt = $pdo->prepare("
        UPDATE licenses 
        SET hardware_id = NULL, deactivated_at = NOW() 
        WHERE license_key = ? AND hardware_id = ?
    ");
    $stmt->execute([$license_key, $hardware_id]);
    
    if ($stmt->rowCount() > 0) {
        // Log deaktivácie
        $stmt = $pdo->prepare("
            SELECT id FROM licenses WHERE license_key = ?
        ");
        $stmt->execute([$license_key]);
        $license_id = $stmt->fetchColumn();
        
        if ($license_id) {
            $stmt = $pdo->prepare("
                INSERT INTO license_logs (license_id, action, hardware_id, ip_address, created_at) 
                VALUES (?, 'deactivate', ?, ?, NOW())
            ");
            $stmt->execute([$license_id, $hardware_id, $_SERVER['REMOTE_ADDR']]);
        }
        
        echo json_encode(['success' => true]);
    } else {
        echo json_encode(['success' => false, 'error' => 'License not found']);
    }
}

// Pomocná funkcia pre generovanie licenčného kľúča
function generateLicenseKey() {
    $chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    $key = '';
    for ($i = 0; $i < 4; $i++) {
        if ($i > 0) $key .= '-';
        for ($j = 0; $j < 4; $j++) {
            $key .= $chars[random_int(0, strlen($chars) - 1)];
        }
    }
    return $key;
}

// Pomocná funkcia pre validáciu hardware ID
function isValidHardwareId($hardware_id) {
    return preg_match('/^[a-f0-9]{32}$/', $hardware_id);
}
?>
