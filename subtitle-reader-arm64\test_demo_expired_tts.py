#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script pre demo expired TTS funkcionalitu
Testuje prečítanie správy po vypršaní demo času
"""

import sys
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_demo_expired_tts():
    """Test demo expired TTS správy."""
    print("🧪 TESTOVANIE DEMO EXPIRED TTS")
    print("=" * 50)
    
    try:
        # Import demo timer
        from demo_timer import DemoWidget
        
        # Vytvor demo widget
        print("📱 Vytváram demo widget...")
        demo_widget = DemoWidget()
        
        # Test TTS správy
        print("🔊 Testujem TTS správu...")
        demo_widget.speak_demo_expired_message()
        
        print("✅ Test dokončený!")
        print("\nAk ste počuli správu, TTS funguje správne.")
        print("Ak nie, skontrolujte:")
        print("- Či máte zapnuté reproduktory/slúchadlá")
        print("- Či je TTS systém správne nakonfigurovaný")
        print("- Logy vyššie pre chybové hlášky")
        
    except ImportError as e:
        print(f"❌ Chyba importu: {e}")
        print("Uistite sa, že ste v správnom adresári s aplikáciou")
    except Exception as e:
        print(f"❌ Chyba pri teste: {e}")
        import traceback
        traceback.print_exc()

def test_config_messages():
    """Test rôznych jazykových verzií správy."""
    print("\n🌍 TESTOVANIE JAZYKOVÝCH VERZIÍ")
    print("=" * 50)
    
    try:
        import common_config as config
        
        messages = {
            'Čeština': getattr(config, 'DEMO_EXPIRED_MESSAGE_CS', 'N/A'),
            'Slovenčina': getattr(config, 'DEMO_EXPIRED_MESSAGE_SK', 'N/A'),
            'English': getattr(config, 'DEMO_EXPIRED_MESSAGE_EN', 'N/A'),
            'Aktuálna': getattr(config, 'DEMO_EXPIRED_MESSAGE', 'N/A')
        }
        
        for lang, message in messages.items():
            print(f"📝 {lang}: {message}")
        
        print(f"\n⚙️ TTS povolené: {getattr(config, 'DEMO_EXPIRED_TTS_ENABLED', 'N/A')}")
        print(f"🎤 Preferovaný hlas: {getattr(config, 'DEMO_EXPIRED_TTS_VOICE_PREFERENCE', 'N/A')}")
        
    except ImportError as e:
        print(f"❌ Chyba importu konfigurácie: {e}")

def test_tts_providers():
    """Test dostupných TTS providerov."""
    print("\n🎤 TESTOVANIE TTS PROVIDEROV")
    print("=" * 50)
    
    # Test TTS manager
    try:
        from tts_manager import speak_text_with_current_settings
        print("✅ TTS Manager dostupný")
        
        # Test krátkeho textu
        test_text = "Test"
        speak_text_with_current_settings(test_text)
        print("🔊 TTS Manager test dokončený")
        
    except ImportError:
        print("⚠️ TTS Manager nie je dostupný")
    except Exception as e:
        print(f"❌ TTS Manager chyba: {e}")
    
    # Test cross-platform TTS
    try:
        from platform_utils import get_tts_provider
        provider = get_tts_provider()
        print(f"✅ Cross-platform TTS dostupný: {type(provider).__name__}")
        
        voices = provider.get_available_voices()
        print(f"🎵 Dostupných hlasov: {len(voices)}")
        
        if voices:
            print("📋 Prvých 3 hlasy:")
            for i, voice in enumerate(voices[:3]):
                name = voice.get('name', 'Unknown')
                lang = voice.get('language', 'Unknown')
                quality = voice.get('quality', 'Unknown')
                print(f"   {i+1}. {name} ({lang}) - {quality}")
        
    except ImportError:
        print("⚠️ Cross-platform TTS nie je dostupný")
    except Exception as e:
        print(f"❌ Cross-platform TTS chyba: {e}")

def test_manual_tts():
    """Manuálny test TTS s rôznymi správami."""
    print("\n🎯 MANUÁLNY TTS TEST")
    print("=" * 50)
    
    messages = [
        "Vypršel limit demo verze pro dnešek. Buď si zakupte plnou verzi, nebo počkejte do zítřka.",
        "Vypršal limit demo verzie pre dnes. Buď si zakúpte plnú verziu, alebo počkajte do zajtra.",
        "Demo time limit expired for today. Either purchase the full version or wait until tomorrow."
    ]
    
    for i, message in enumerate(messages, 1):
        print(f"\n🔊 Test {i}: {message[:50]}...")
        
        try:
            # Pokús sa použiť cross-platform TTS
            from platform_utils import get_tts_provider
            provider = get_tts_provider()
            voices = provider.get_available_voices()
            
            if voices:
                voice = voices[0]  # Použij prvý dostupný hlas
                success = provider.speak(message, voice.get('id'), 200)
                if success:
                    print(f"✅ Test {i} úspešný")
                else:
                    print(f"❌ Test {i} zlyhal")
            else:
                print(f"⚠️ Test {i} - žiadne hlasy dostupné")
                
        except Exception as e:
            print(f"❌ Test {i} chyba: {e}")
        
        # Krátka pauza medzi testami
        import time
        time.sleep(1)

def main():
    """Hlavná test funkcia."""
    print("🚀 SUBTITLE READER - DEMO EXPIRED TTS TEST")
    print("Testovanie prečítania správy po vypršaní demo času")
    print()
    
    # Test konfigurácie
    test_config_messages()
    
    # Test TTS providerov
    test_tts_providers()
    
    # Test demo expired TTS
    test_demo_expired_tts()
    
    # Manuálny test
    response = input("\n❓ Chcete spustiť manuálny TTS test? (y/n): ")
    if response.lower() in ['y', 'yes', 'ano', 'áno']:
        test_manual_tts()
    
    print("\n🎉 Všetky testy dokončené!")
    print("Skontrolujte logy vyššie pre detaily o fungovaní TTS.")

if __name__ == "__main__":
    main()
