# Linux-specific requirements for Subtitle Reader
# Cross-platform dependencies
Levenshtein==0.27.1
mss==10.0.0
packaging==25.0
pillow==11.3.0
pynput==1.7.6
pytesseract==0.3.13
RapidFuzz==3.13.0
six==1.17.0

# GUI Framework
PyQt6==6.7.0

# TTS for Linux
pyttsx3==2.98

# HTTP requests
requests==2.32.3
python-dotenv==1.0.1

# Data processing
numpy==1.26.4
tqdm==4.66.4

# Web interface (if needed)
pywebview

# Linux-specific system tools (optional)
# psutil==5.9.8  # For system information

# Note: pyobjc and all pyobjc-framework-* packages are NOT included
# as they are macOS-specific

# Installation instructions for Linux:
# 1. Install system dependencies:
#    sudo apt update
#    sudo apt install python3 python3-pip python3-venv
#    sudo apt install tesseract-ocr tesseract-ocr-ces tesseract-ocr-eng
#    sudo apt install espeak espeak-data  # For TTS
#    sudo apt install xdotool wmctrl      # For window management (optional)
#
# 2. Create virtual environment: python3 -m venv .venv
# 3. Activate virtual environment: source .venv/bin/activate
# 4. Install dependencies: pip install -r requirements_linux.txt

# Optional dependencies for enhanced functionality:
# - For CUDA support (if you have NVIDIA GPU):
#   torch==2.2.2+cu118 torchvision==0.17.2+cu118 torchaudio==2.2.2+cu118 --index-url https://download.pytorch.org/whl/cu118
# - For CPU-only PyTorch:
#   torch==2.2.2 torchvision==0.17.2 torchaudio==2.2.2 --index-url https://download.pytorch.org/whl/cpu
