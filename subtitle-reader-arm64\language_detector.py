"""
Language Detector - Automatická detekcia dostupných jazykov v systéme
Detekuje dostupné jazyky pre OCR (Tesseract), TTS (Apple) a aplikáciu
"""

import subprocess
import os
import logging
import re
from typing import Dict, List, Set, Optional

class LanguageDetector:
    """Detekuje dostupné jazyky v systéme."""
    
    def __init__(self):
        self.available_app_languages = set()
        self.available_tesseract_languages = set()
        self.available_apple_tts_languages = set()
        self.available_reading_languages = set()  # Priesečník OCR + TTS
        
        # Mapovanie ISO kódov na Tesseract kódy
        self.iso_to_tesseract = {
            "cs": "ces", "sk": "slk", "en": "eng", "de": "deu", "fr": "fra",
            "es": "spa", "it": "ita", "ru": "rus", "pl": "pol", "hu": "hun",
            "ro": "ron", "bg": "bul", "hr": "hrv", "sl": "slv", "et": "est",
            "lv": "lav", "lt": "lit", "fi": "fin", "da": "dan", "sv": "swe",
            "no": "nor", "nl": "nld", "pt": "por", "uk": "ukr", "ar": "ara",
            "zh": "chi_sim", "ja": "jpn", "ko": "kor", "th": "tha", "vi": "vie"
        }
        
        # Mapovanie ISO kódov na Apple TTS kódy
        self.iso_to_apple_tts = {
            "cs": "cs-CZ", "sk": "sk-SK", "en": "en-US", "de": "de-DE", "fr": "fr-FR",
            "es": "es-ES", "it": "it-IT", "ru": "ru-RU", "pl": "pl-PL", "hu": "hu-HU",
            "ro": "ro-RO", "bg": "bg-BG", "hr": "hr-HR", "sl": "sl-SI", "et": "et-EE",
            "lv": "lv-LV", "lt": "lt-LT", "fi": "fi-FI", "da": "da-DK", "sv": "sv-SE",
            "no": "no-NO", "nl": "nl-NL", "pt": "pt-PT", "uk": "uk-UA", "ar": "ar-SA",
            "zh": "zh-CN", "ja": "ja-JP", "ko": "ko-KR", "th": "th-TH", "vi": "vi-VN"
        }

        # Opačné mapovanie: locale kódy na ISO kódy (pre cross-platform TTS)
        self.locale_to_iso = {}
        for iso, locale in self.iso_to_apple_tts.items():
            self.locale_to_iso[locale] = iso

        # Pridaj aj ďalšie bežné locale varianty
        self.locale_to_iso.update({
            "en-GB": "en", "en-AU": "en", "en-CA": "en", "en-IN": "en",
            "de-AT": "de", "de-CH": "de",
            "fr-CA": "fr", "fr-CH": "fr", "fr-BE": "fr",
            "es-MX": "es", "es-AR": "es", "es-CO": "es",
            "pt-BR": "pt", "pt-PT": "pt",
            "zh-TW": "zh", "zh-HK": "zh", "zh-SG": "zh"
        })
        
        # Mapovanie jazykových názvov
        self.language_names = {
            "cs": {"name": "Čeština", "flag": "🇨🇿"},
            "sk": {"name": "Slovenčina", "flag": "🇸🇰"},
            "en": {"name": "English", "flag": "🇺🇸"},
            "de": {"name": "Deutsch", "flag": "🇩🇪"},
            "fr": {"name": "Français", "flag": "🇫🇷"},
            "es": {"name": "Español", "flag": "🇪🇸"},
            "it": {"name": "Italiano", "flag": "🇮🇹"},
            "ru": {"name": "Русский", "flag": "🇷🇺"},
            "pl": {"name": "Polski", "flag": "🇵🇱"},
            "hu": {"name": "Magyar", "flag": "🇭🇺"},
            "ro": {"name": "Română", "flag": "🇷🇴"},
            "bg": {"name": "Български", "flag": "🇧🇬"},
            "hr": {"name": "Hrvatski", "flag": "🇭🇷"},
            "sl": {"name": "Slovenščina", "flag": "🇸🇮"},
            "et": {"name": "Eesti", "flag": "🇪🇪"},
            "lv": {"name": "Latviešu", "flag": "🇱🇻"},
            "lt": {"name": "Lietuvių", "flag": "🇱🇹"},
            "fi": {"name": "Suomi", "flag": "🇫🇮"},
            "da": {"name": "Dansk", "flag": "🇩🇰"},
            "sv": {"name": "Svenska", "flag": "🇸🇪"},
            "no": {"name": "Norsk", "flag": "🇳🇴"},
            "nl": {"name": "Nederlands", "flag": "🇳🇱"},
            "pt": {"name": "Português", "flag": "🇵🇹"},
            "uk": {"name": "Українська", "flag": "🇺🇦"},
            "ar": {"name": "العربية", "flag": "🇸🇦"},
            "zh": {"name": "中文", "flag": "🇨🇳"},
            "ja": {"name": "日本語", "flag": "🇯🇵"},
            "ko": {"name": "한국어", "flag": "🇰🇷"},
            "th": {"name": "ไทย", "flag": "🇹🇭"},
            "vi": {"name": "Tiếng Việt", "flag": "🇻🇳"}
        }
    
    def detect_app_languages(self) -> Set[str]:
        """Detekuje dostupné jazyky aplikácie (z locales/ priečinka)."""
        try:
            locales_dir = "locales"
            languages = set()
            
            if os.path.exists(locales_dir):
                for file in os.listdir(locales_dir):
                    if file.endswith(".json"):
                        lang_code = file[:-5]  # odstráni .json
                        languages.add(lang_code)
                        
            self.available_app_languages = languages
            logging.info(f"🌐 Detekované jazyky aplikácie: {sorted(languages)}")
            return languages
            
        except Exception as e:
            logging.error(f"❌ Chyba pri detekcii jazykov aplikácie: {e}")
            return {"cs", "en"}  # Fallback
    
    def detect_tesseract_languages(self) -> Set[str]:
        """Detekuje dostupné jazyky pre Tesseract OCR."""
        try:
            logging.info("🔍 Detekujem Tesseract jazyky...")

            # Spustí tesseract --list-langs
            result = subprocess.run(
                ["tesseract", "--list-langs"],
                capture_output=True,
                text=True,
                timeout=10
            )

            logging.info(f"🔍 Tesseract return code: {result.returncode}")
            logging.debug(f"🔍 Tesseract stdout: {result.stdout}")
            logging.debug(f"🔍 Tesseract stderr: {result.stderr}")

            if result.returncode == 0:
                # Parsuje výstup (Tesseract píše do stderr)
                output = result.stderr.strip() if result.stderr else result.stdout.strip()
                lines = output.split('\n')
                tesseract_langs = set()

                logging.info(f"🔍 Tesseract výstup má {len(lines)} riadkov")

                for line in lines:
                    line = line.strip()
                    if line and not line.startswith('List of available languages'):
                        tesseract_langs.add(line)
                        logging.debug(f"🔍 Tesseract jazyk: {line}")

                logging.info(f"🔍 Tesseract raw jazyky: {sorted(tesseract_langs)}")

                # Mapuje Tesseract kódy na ISO kódy
                iso_langs = set()
                for iso_code, tess_code in self.iso_to_tesseract.items():
                    if tess_code in tesseract_langs:
                        iso_langs.add(iso_code)
                        logging.debug(f"🔍 Mapovanie: {tess_code} → {iso_code}")

                self.available_tesseract_languages = iso_langs
                logging.info(f"🔍 Detekované OCR jazyky: {sorted(iso_langs)}")
                return iso_langs
            else:
                logging.error(f"❌ Tesseract zlyhalo s kódom {result.returncode}")
                logging.error(f"❌ Tesseract chyba: {result.stderr}")

        except FileNotFoundError:
            logging.error("❌ Tesseract nie je nainštalovaný alebo nie je v PATH")
        except Exception as e:
            logging.error(f"❌ Chyba pri detekcii Tesseract jazykov: {e}")
            import traceback
            logging.debug(traceback.format_exc())

        # Fallback na základné jazyky
        logging.warning("🔧 Používam fallback OCR jazyky")
        fallback = {"cs", "sk", "en", "de", "fr"}
        self.available_tesseract_languages = fallback
        return fallback
    
    def detect_apple_tts_languages(self) -> Set[str]:
        """Detekuje dostupné jazyky pre TTS cross-platform."""
        detected_locales = set()
        self.available_voices = {}  # Uložíme si hlasy pre každý jazyk

        try:
            # Try cross-platform TTS detection first
            try:
                from platform_utils import get_tts_provider
                tts_provider = get_tts_provider()
                voices = tts_provider.get_available_voices()

                logging.info(f"🎤 Detekujem {len(voices)} TTS hlasov pomocou cross-platform providera...")

                for voice_info in voices:
                    try:
                        voice_name = voice_info.get('name', 'Unknown')
                        language = voice_info.get('language', 'en-US')
                        quality = voice_info.get('quality', 'standard')

                        # Normalize language code
                        if '-' in language:
                            lang_code = language.split('-')[0].lower()
                        else:
                            lang_code = language.lower()

                        # Map to ISO codes
                        iso_code = self.locale_to_iso.get(language, lang_code)
                        if iso_code:
                            detected_locales.add(language)

                            if iso_code not in self.available_voices:
                                self.available_voices[iso_code] = []

                            self.available_voices[iso_code].append({
                                'id': voice_info.get('id', voice_name),
                                'name': voice_name,
                                'locale': language,
                                'quality': quality
                            })

                            logging.debug(f"🎤 Hlas: {voice_name} ({language})")

                    except Exception as e:
                        logging.debug(f"⚠️ Chyba pri spracovaní hlasu: {e}")
                        continue

                logging.info(f"🔊 Cross-platform TTS detekované locales: {sorted(detected_locales)}")

            except ImportError:
                logging.warning("⚠️ Cross-platform TTS provider not available, trying legacy methods")
                # Fall back to legacy Apple TTS detection
                return self._detect_legacy_apple_tts()

        except Exception as e:
            logging.error(f"❌ Chyba pri detekcii TTS hlasov: {e}")
            return self._detect_legacy_apple_tts()

        return self._convert_locales_to_iso_codes(detected_locales)

    def _detect_legacy_apple_tts(self) -> Set[str]:
        """Legacy Apple TTS detection for macOS."""
        detected_locales = set()

        try:
            # Pokus o použitie NSSpeechSynthesizer (macOS)
            try:
                from AppKit import NSSpeechSynthesizer

                # Získaj všetky dostupné hlasy
                voices = NSSpeechSynthesizer.availableVoices()

                logging.info(f"🎤 Detekujem {len(voices)} Apple TTS hlasov pomocou NSSpeechSynthesizer...")

                for voice_id in voices:
                    try:
                        attrs = NSSpeechSynthesizer.attributesForVoice_(voice_id)
                        voice_name_raw = attrs.get('VoiceName', 'Unknown')
                        locale_id = attrs.get('VoiceLocaleIdentifier', '')

                        if locale_id:
                            # Konvertuje sk_SK na sk-SK
                            locale_normalized = locale_id.replace('_', '-')
                            detected_locales.add(locale_normalized)

                            # Extrahuje ISO kód (sk z sk-SK)
                            iso_code = locale_normalized.split('-')[0]

                            # Uloží hlas pre jazyk
                            if iso_code not in self.available_voices:
                                self.available_voices[iso_code] = []

                            # Normalizácia názvu hlasu - extrahuje len základný názov
                            # Pre hlasy ako "Eddy (German (Germany))" vezme len "Eddy"
                            voice_name = voice_name_raw.split(' (')[0] if ' (' in voice_name_raw else voice_name_raw

                            # Detekcia kvality hlasu: Premium > Enhanced > Standard
                            voice_quality = 'standard'  # default
                            if ('Premium' in voice_name_raw or 'prémiový' in voice_name_raw or 'premium' in voice_name_raw.lower()):
                                voice_quality = 'premium'
                            elif ('Enhanced' in voice_name_raw or 'vylepšený' in voice_name_raw or 'enhanced' in voice_name_raw.lower()):
                                voice_quality = 'enhanced'

                            # Zachovaj is_enhanced pre spätnosť
                            is_enhanced = voice_quality in ['enhanced', 'premium']

                            self.available_voices[iso_code].append({
                                'id': str(voice_id),
                                'name': voice_name,  # Používa normalizovaný názov
                                'locale': locale_normalized,
                                'is_enhanced': is_enhanced,
                                'quality': voice_quality
                            })

                            logging.debug(f"🎤 Hlas: {voice_name_raw} -> {voice_name} ({locale_normalized})")

                    except Exception as e:
                        logging.debug(f"⚠️ Chyba pri spracovaní hlasu {voice_id}: {e}")
                        continue

                logging.info(f"🔊 NSSpeechSynthesizer detekované locales: {sorted(detected_locales)}")

            except ImportError:
                logging.warning("⚠️ pyobjc nie je dostupný, používam fallback detekciu cez 'say -v ?'")

                # Fallback na say -v ?
                try:
                    result = subprocess.run(
                        ["say", "-v", "?"],
                        capture_output=True,
                        text=True,
                        timeout=10
                    )

                    if result.returncode == 0:
                        lines = result.stdout.strip().split('\n')
                        logging.info(f"🎤 Detekujem hlasy cez 'say -v ?' - nájdených {len(lines)} riadkov")

                        for line in lines:
                            # Hľadá locale kódy v formáte en_US, cs_CZ, atď.
                            locale_match = re.search(r'\b([a-z]{2}_[A-Z]{2})\b', line)
                            if locale_match:
                                locale = locale_match.group(1)
                                locale_normalized = locale.replace('_', '-')
                                detected_locales.add(locale_normalized)

                                # Extrahuje názov hlasu (prvé slovo)
                                voice_name = line.split()[0] if line.split() else 'Unknown'
                                iso_code = locale_normalized.split('-')[0]

                                if iso_code not in self.available_voices:
                                    self.available_voices[iso_code] = []

                                # Detekcia enhanced/premium hlasov v rôznych jazykoch
                                is_enhanced = (
                                    'Enhanced' in line or 'Premium' in line or
                                    'vylepšený' in line or 'prémiový' in line or
                                    'enhanced' in line.lower() or 'premium' in line.lower()
                                )

                                self.available_voices[iso_code].append({
                                    'id': voice_name,
                                    'name': voice_name,
                                    'locale': locale_normalized,
                                    'is_enhanced': is_enhanced
                                })

                        logging.info(f"🔊 'say -v ?' detekované locales: {sorted(detected_locales)}")
                    else:
                        logging.error(f"❌ 'say -v ?' zlyhalo s kódom {result.returncode}")

                except Exception as e:
                    logging.error(f"❌ Chyba pri fallback detekcii: {e}")

            # Mapuje Apple TTS kódy na ISO kódy
            iso_langs = set()
            for iso_code, apple_code in self.iso_to_apple_tts.items():
                if apple_code in detected_locales:
                    iso_langs.add(iso_code)

            # Pridaj aj jazyky, ktoré máme priamo detekované
            for iso_code in self.available_voices.keys():
                iso_langs.add(iso_code)

            self.available_apple_tts_languages = iso_langs
            logging.info(f"🔊 Finálne detekované TTS jazyky: {sorted(iso_langs)}")

            # Zobraz najlepšie hlasy pre každý jazyk
            for lang_code in sorted(iso_langs):
                if lang_code in self.available_voices:
                    voices_list = self.available_voices[lang_code]
                    enhanced_voices = [v for v in voices_list if v['is_enhanced']]
                    if enhanced_voices:
                        best_voice = enhanced_voices[0]
                        voice_name = best_voice['name']
                        # Skontroluj, či hlas už obsahuje (Enhanced), (Standard) alebo (Premium)
                        if any(suffix in voice_name for suffix in [" (Enhanced)", " (Standard)", " (Premium)"]):
                            logging.info(f"🎯 {lang_code}: {voice_name}")
                        else:
                            logging.info(f"🎯 {lang_code}: {voice_name} (Enhanced)")
                    else:
                        best_voice = voices_list[0]
                        voice_name = best_voice['name']
                        # Skontroluj, či hlas už obsahuje (Enhanced), (Standard) alebo (Premium)
                        if any(suffix in voice_name for suffix in [" (Enhanced)", " (Standard)", " (Premium)"]):
                            logging.info(f"🎯 {lang_code}: {voice_name}")
                        else:
                            logging.info(f"🎯 {lang_code}: {voice_name} (Standard)")

            return iso_langs

        except Exception as e:
            logging.error(f"❌ Chyba pri detekcii Apple TTS jazykov: {e}")
            import traceback
            logging.error(traceback.format_exc())

        # Fallback na základné jazyky
        logging.warning("🔧 Používam fallback základné jazyky")
        fallback = {"cs", "sk", "en", "de", "fr"}
        self.available_apple_tts_languages = fallback
        return fallback

    def _convert_locales_to_iso_codes(self, detected_locales: Set[str]) -> Set[str]:
        """Convert locale codes to ISO language codes."""
        iso_langs = set()

        # Mapuje detekované locales na ISO kódy
        for locale in detected_locales:
            if '-' in locale:
                iso_code = locale.split('-')[0].lower()
                iso_langs.add(iso_code)
            else:
                iso_langs.add(locale.lower())

        # Pridaj aj jazyky, ktoré máme priamo detekované
        for iso_code in self.available_voices.keys():
            iso_langs.add(iso_code)

        self.available_apple_tts_languages = iso_langs
        logging.info(f"🔊 Finálne detekované TTS jazyky: {sorted(iso_langs)}")

        # Zobraz najlepšie hlasy pre každý jazyk
        for lang_code in sorted(iso_langs):
            if lang_code in self.available_voices:
                voices_list = self.available_voices[lang_code]
                # Sort by quality: premium > enhanced > standard
                quality_order = {'premium': 3, 'enhanced': 2, 'standard': 1}
                voices_list.sort(key=lambda v: quality_order.get(v.get('quality', 'standard'), 1), reverse=True)

                if voices_list:
                    best_voice = voices_list[0]
                    voice_name = best_voice['name']
                    quality = best_voice.get('quality', 'standard').title()
                    logging.info(f"🎯 {lang_code}: {voice_name} ({quality})")

        return iso_langs


    
    def detect_all_languages(self) -> Dict[str, Set[str]]:
        """Detekuje všetky dostupné jazyky v systéme."""
        app_langs = self.detect_app_languages()
        ocr_langs = self.detect_tesseract_languages()
        tts_langs = self.detect_apple_tts_languages()
        
        # Priesečník OCR + TTS = dostupné jazyky pre čítanie
        reading_langs = ocr_langs.intersection(tts_langs)
        self.available_reading_languages = reading_langs
        
        logging.info(f"📖 Dostupné jazyky pre čítanie (OCR+TTS): {sorted(reading_langs)}")
        
        return {
            "app": app_langs,
            "ocr": ocr_langs,
            "tts": tts_langs,
            "reading": reading_langs
        }
    
    def get_language_info(self, lang_code: str) -> Dict[str, str]:
        """Vráti informácie o jazyku."""
        info = self.language_names.get(lang_code, {
            "name": lang_code.upper(),
            "flag": "🌐"
        })
        
        return {
            "name": info["name"],
            "flag": info["flag"],
            "tesseract": self.iso_to_tesseract.get(lang_code, "eng"),
            "apple_tts": self.iso_to_apple_tts.get(lang_code, "en-US"),
            "iso": lang_code
        }
    
    def get_display_name(self, lang_code: str) -> str:
        """Vráti zobrazovací názov jazyka s vlajkou."""
        info = self.get_language_info(lang_code)
        return f"{info['flag']} {info['name']}"
    
    def is_language_available_for_reading(self, lang_code: str) -> bool:
        """Kontroluje, či je jazyk dostupný pre čítanie (OCR + TTS)."""
        return lang_code in self.available_reading_languages
    
    def get_voices_for_language(self, lang_code: str) -> List[Dict[str, str]]:
        """Vráti zoznam všetkých dostupných hlasov pre daný jazyk."""
        try:
            # Použije detekované hlasy z available_voices
            if hasattr(self, 'available_voices') and lang_code in self.available_voices:
                voices = self.available_voices[lang_code]
                if voices:
                    # Zoradí hlasy podľa priority: Premium > Enhanced > Standard, potom podľa názvu
                    def voice_priority(voice):
                        quality = voice.get('quality', 'standard')
                        if quality == 'premium':
                            return (0, voice['name'])  # Najvyššia priorita
                        elif quality == 'enhanced':
                            return (1, voice['name'])  # Stredná priorita
                        else:
                            return (2, voice['name'])  # Najnižšia priorita

                    sorted_voices = sorted(voices, key=voice_priority)
                    return sorted_voices

            # Fallback - pokus o detekciu cez say -v ? (len na macOS)
            import platform
            if platform.system() == 'Darwin':  # macOS
                apple_locale = self.iso_to_apple_tts.get(lang_code, "en-US")
                result = subprocess.run(
                    ["say", "-v", "?"],
                    capture_output=True,
                    text=True,
                    timeout=10
                )
            else:
                # Na Windows/Linux nemáme fallback, vrátime prázdny zoznam
                return []

            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                voices = []

                for line in lines:
                    if apple_locale.replace('-', '_') in line:
                        voice_name = line.split()[0]
                        # Detekcia kvality hlasu: Premium > Enhanced > Standard
                        voice_quality = 'standard'  # default
                        if ('Premium' in line or 'prémiový' in line or 'premium' in line.lower()):
                            voice_quality = 'premium'
                        elif ('Enhanced' in line or 'vylepšený' in line or 'enhanced' in line.lower()):
                            voice_quality = 'enhanced'

                        # Zachovaj is_enhanced pre spätnosť
                        is_enhanced = voice_quality in ['enhanced', 'premium']

                        voices.append({
                            'id': voice_name,
                            'name': voice_name,
                            'locale': apple_locale,
                            'is_enhanced': is_enhanced,
                            'quality': voice_quality
                        })

                # Zoradí hlasy podľa priority: Premium > Enhanced > Standard
                def voice_priority(voice):
                    quality = voice.get('quality', 'standard')
                    if quality == 'premium':
                        return (0, voice['name'])
                    elif quality == 'enhanced':
                        return (1, voice['name'])
                    else:
                        return (2, voice['name'])

                voices.sort(key=voice_priority)
                return voices

        except Exception as e:
            logging.error(f"❌ Chyba pri získavaní hlasov pre {lang_code}: {e}")

        return []

    def get_best_voice_for_language(self, lang_code: str) -> Optional[str]:
        """Nájde najlepší hlas pre daný jazyk (Premium > Enhanced > Standard)."""
        try:
            voices = self.get_voices_for_language(lang_code)
            if voices:
                # Vráti prvý hlas (už zoradené podľa priority Premium > Enhanced > Standard)
                best_voice_data = voices[0]
                best_voice = best_voice_data['name']

                # Skontroluj, či hlas už obsahuje (Enhanced), (Standard) alebo (Premium)
                if any(suffix in best_voice for suffix in [" (Enhanced)", " (Standard)", " (Premium)"]):
                    # Hlas už má suffix
                    logging.info(f"🎤 Najlepší hlas pre {lang_code}: {best_voice}")
                    return best_voice
                else:
                    # Pridaj suffix podľa kvality
                    quality = best_voice_data.get('quality', 'standard')
                    if quality == 'premium':
                        voice_with_suffix = f"{best_voice} (Premium)"
                    elif quality == 'enhanced':
                        voice_with_suffix = f"{best_voice} (Enhanced)"
                    else:
                        voice_with_suffix = f"{best_voice} (Standard)"

                    logging.info(f"🎤 Najlepší hlas pre {lang_code}: {voice_with_suffix}")
                    return voice_with_suffix

        except Exception as e:
            logging.error(f"❌ Chyba pri hľadaní hlasu pre {lang_code}: {e}")

        return None


# Globálna inštancia
language_detector = LanguageDetector()

def get_language_detector():
    """Vráti globálnu inštanciu language detectora."""
    return language_detector

def detect_system_languages():
    """Detekuje všetky dostupné jazyky v systéme."""
    return language_detector.detect_all_languages()
