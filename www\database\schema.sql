-- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> schéma pre Subtitle Reader
-- MySQL/MariaDB

-- Používame existujúcu databázu d52810_voxoloxo
USE d52810_voxoloxo;

-- Tabuľ<PERSON> užívateľov
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    email VARCHAR(255) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    status ENUM('pending', 'active', 'suspended') DEFAULT 'pending',
    email_verified_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_email (email),
    INDEX idx_status (status)
);

-- Tabuľka licencií
CREATE TABLE licenses (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    license_key VARCHAR(20) NOT NULL UNIQUE,
    purchase_id VARCHAR(100) NULL,
    stripe_payment_intent_id VARCHAR(100) NULL,
    hardware_id VARCHAR(64) NULL,
    status ENUM('active', 'expired', 'revoked') DEFAULT 'active',
    expires_at TIMESTAMP NOT NULL,
    activated_at TIMESTAMP NULL,
    deactivated_at TIMESTAMP NULL,
    last_verified TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_license_key (license_key),
    INDEX idx_hardware_id (hardware_id),
    INDEX idx_status (status),
    INDEX idx_expires_at (expires_at)
);

-- Tabuľka logov licencií
CREATE TABLE license_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    license_id INT NOT NULL,
    action ENUM('activate', 'verify', 'deactivate', 'expire') NOT NULL,
    hardware_id VARCHAR(64) NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (license_id) REFERENCES licenses(id) ON DELETE CASCADE,
    INDEX idx_license_id (license_id),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at)
);

-- Tabuľka Stripe platieb
CREATE TABLE stripe_payments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    stripe_payment_intent_id VARCHAR(100) NOT NULL UNIQUE,
    stripe_customer_id VARCHAR(100) NULL,
    amount INT NOT NULL, -- v haléřoch
    currency VARCHAR(3) NOT NULL DEFAULT 'czk',
    status ENUM('pending', 'succeeded', 'failed', 'canceled') DEFAULT 'pending',
    metadata JSON NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_stripe_payment_intent (stripe_payment_intent_id),
    INDEX idx_user_id (user_id),
    INDEX idx_status (status)
);

-- Tabuľka pre overenie emailu
CREATE TABLE email_verification_tokens (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    token VARCHAR(64) NOT NULL UNIQUE,
    expires_at TIMESTAMP NOT NULL,
    used_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_token (token),
    INDEX idx_user_id (user_id),
    INDEX idx_expires_at (expires_at)
);

-- Tabuľka pre reset hesla
CREATE TABLE password_reset_tokens (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    token VARCHAR(64) NOT NULL UNIQUE,
    expires_at TIMESTAMP NOT NULL,
    used_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_token (token),
    INDEX idx_user_id (user_id),
    INDEX idx_expires_at (expires_at)
);

-- Tabuľka pre štatistiky používania
CREATE TABLE usage_stats (
    id INT PRIMARY KEY AUTO_INCREMENT,
    license_id INT NOT NULL,
    date DATE NOT NULL,
    session_count INT DEFAULT 0,
    total_minutes_used INT DEFAULT 0,
    ocr_operations INT DEFAULT 0,
    tts_operations INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (license_id) REFERENCES licenses(id) ON DELETE CASCADE,
    UNIQUE KEY unique_license_date (license_id, date),
    INDEX idx_date (date)
);

-- Tabuľka pre konfiguráciu aplikácie
CREATE TABLE app_config (
    id INT PRIMARY KEY AUTO_INCREMENT,
    config_key VARCHAR(100) NOT NULL UNIQUE,
    config_value TEXT NOT NULL,
    description TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_config_key (config_key)
);

-- Vloženie základnej konfigurácie
INSERT INTO app_config (config_key, config_value, description) VALUES
('annual_license_price', '99000', 'Cena ročnej licencie v haléřoch'),
('demo_daily_limit', '600', 'Demo limit v sekundách (10 minút)'),
('max_activations_per_license', '1', 'Maximálny počet aktivácií na licenciu'),
('license_grace_period_days', '7', 'Grace period pre offline overenie v dňoch'),
('app_version', '2.0.0', 'Aktuálna cross-platform verzia aplikácie'),
('maintenance_mode', 'false', 'Údržbový režim'),
('stripe_webhook_enabled', 'true', 'Povolenie Stripe webhookov');

-- Vytvorenie indexov pre výkon
CREATE INDEX idx_users_created_at ON users(created_at);
CREATE INDEX idx_licenses_created_at ON licenses(created_at);
CREATE INDEX idx_stripe_payments_created_at ON stripe_payments(created_at);

-- Vytvorenie views pre reporty
CREATE VIEW active_licenses AS
SELECT 
    l.*,
    u.email,
    u.first_name,
    u.last_name,
    DATEDIFF(l.expires_at, NOW()) as days_remaining
FROM licenses l
JOIN users u ON l.user_id = u.id
WHERE l.status = 'active' AND l.expires_at > NOW();

CREATE VIEW license_usage_summary AS
SELECT 
    l.license_key,
    u.email,
    COUNT(ll.id) as total_verifications,
    MAX(ll.created_at) as last_verification,
    l.activated_at,
    l.expires_at,
    DATEDIFF(l.expires_at, NOW()) as days_remaining
FROM licenses l
JOIN users u ON l.user_id = u.id
LEFT JOIN license_logs ll ON l.id = ll.license_id AND ll.action = 'verify'
WHERE l.status = 'active'
GROUP BY l.id, u.email;

-- Trigger pre automatické vypršanie licencií
DELIMITER //
CREATE TRIGGER expire_licenses_trigger
BEFORE UPDATE ON licenses
FOR EACH ROW
BEGIN
    IF NEW.expires_at <= NOW() AND OLD.status = 'active' THEN
        SET NEW.status = 'expired';
    END IF;
END//
DELIMITER ;

-- Stored procedure pre cleanup starých tokenov
DELIMITER //
CREATE PROCEDURE CleanupExpiredTokens()
BEGIN
    DELETE FROM email_verification_tokens WHERE expires_at < NOW() AND used_at IS NULL;
    DELETE FROM password_reset_tokens WHERE expires_at < NOW() AND used_at IS NULL;
    DELETE FROM license_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 1 YEAR);
END//
DELIMITER ;

-- Event pre automatické spúšťanie cleanup (spúšťa sa každý deň o polnoci)
CREATE EVENT IF NOT EXISTS cleanup_expired_tokens
ON SCHEDULE EVERY 1 DAY
STARTS '2024-01-01 00:00:00'
DO
CALL CleanupExpiredTokens();

-- Nastavenie event schedulera (ak nie je povolený)
-- SET GLOBAL event_scheduler = ON;

-- Vytvorenie užívateľa pre aplikáciu (voliteľné)
-- CREATE USER 'subtitle_reader_app'@'localhost' IDENTIFIED BY 'strong_password_here';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON subtitle_reader.* TO 'subtitle_reader_app'@'localhost';
-- FLUSH PRIVILEGES;
