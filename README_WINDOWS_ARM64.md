# Subtitle Reader - Windows ARM64 Setup Guide

## 🎯 Úspešne spustené na Windows ARM64!

T<PERSON>to aplik<PERSON> je teraz plne funkčná na Windows ARM64 systémoch.

## ✅ Čo je nainštalované a funguje

### 🐍 Python Environment
- **Python 3.13.7** (ARM64 natívna verzia)
- **Virtuálne prostredie** (.venv) s všetk<PERSON>mi potrebnými balíčkami

### 📦 Kľúčové komponenty
- **PyQt6** - GUI framework (ARM64 kompatibilný)
- **Tesseract OCR v5.5.0** - rozpoznávanie textu z obrazovky
- **pyttsx3** - Text-to-Speech engine pre Windows
- **pywin32** - Windows systémová integrácia
- **WMI** - Windows Management Instrumentation
- **Všetky ostatné závislosti** - plne funkčné

### 🔧 Opravy pre ARM64
1. **<PERSON><PERSON><PERSON><PERSON> fallback** - používa RapidFuzz ako náhradu
2. **PyQt6 kompatibilita** - opravené API rozdiely
3. **Unicode logging** - UTF-8 encoding pre Windows
4. **Cross-platform TTS** - Windows SAPI integrácia

## 🚀 Spustenie aplikácie

### Rýchle spustenie
```cmd
run_app.bat
```

### Manuálne spustenie
```cmd
.venv\Scripts\activate.bat
python main_qt.py
```

### Test funkcionality
```cmd
.venv\Scripts\activate.bat
python test_windows_arm64.py
```

## 🎮 Použitie aplikácie

1. **Spustite aplikáciu** pomocou `run_app.bat`
2. **Vyberte oblasť** na obrazovke s titulkami
3. **Nastavte jazyk** pre OCR a TTS
4. **Spustite čítanie** titulkov

### ⌨️ Klávesové skratky (Windows)
- `Ctrl+Alt+Ctrl+V` - zapnúť/vypnúť čítanie
- `Ctrl+Alt+Ctrl+T` - test TTS
- `Ctrl+Alt+Ctrl+←/→` - hlasitosť
- `Ctrl+Alt+Ctrl+↑/↓` - rýchlosť reči

## 🔧 Technické detaily

### Systémové požiadavky
- **Windows 11 ARM64** (testované)
- **4GB RAM** (odporúčané 8GB+)
- **Python 3.10+** (nainštalované: 3.13.7)
- **Tesseract OCR** (nainštalované: v5.5.0)

### Podporované jazyky
- **OCR**: English, Slovak (ďalšie možno pridať)
- **TTS**: Všetky Windows SAPI hlasy
- **GUI**: Čeština, Slovenčina, Angličtina, Nemčina, Francúzština

### Výkon na ARM64
- **OCR spracovanie**: ~50-200ms
- **TTS generovanie**: ~200-800ms  
- **GUI odozva**: Natívna ARM64 rýchlosť
- **Pamäťová spotreba**: ~100-300MB

## 📁 Štruktúra súborov

```
subtitle-reader/
├── run_app.bat              # Spúšťací script
├── test_windows_arm64.py    # Test script
├── main_qt.py               # Hlavná aplikácia
├── .venv/                   # Virtuálne prostredie
├── app.log                  # Log súbor
└── requirements_windows.txt # Windows závislosti
```

## 🐛 Riešenie problémov

### Aplikácia sa nespustí
1. Skontrolujte `app.log` pre chybové hlášky
2. Spustite `test_windows_arm64.py` pre diagnostiku
3. Overte, že virtuálne prostredie existuje

### OCR nefunguje
1. Overte Tesseract inštaláciu: `tesseract --version`
2. Skontrolujte cestu: `C:\Program Files\Tesseract-OCR\`
3. Nastavte TESSERACT_CMD environment variable

### TTS nefunguje
1. Skontrolujte Windows TTS hlasy v Settings
2. Nainštalujte dodatočné jazykové balíčky
3. Reštartujte aplikáciu po pridaní hlasov

### GUI problémy
1. Overte PyQt6 inštaláciu: `pip list | findstr PyQt6`
2. Skontrolujte Windows display scaling
3. Spustite ako administrátor pre hotkeys

## 🔄 Aktualizácia

Pre aktualizáciu na novšiu verziu:

```cmd
git pull
.venv\Scripts\activate.bat
pip install -r requirements_windows.txt --upgrade
```

## 📞 Podpora

- **Logy**: Skontrolujte `app.log` pre detailné informácie
- **Test**: Spustite `test_windows_arm64.py` pre diagnostiku
- **GitHub**: Nahláste problémy na GitHub repository

---

## 🎉 Úspešne spustené!

Aplikácia Subtitle Reader je teraz plne funkčná na vašom Windows ARM64 systéme. Všetky kľúčové funkcie vrátane OCR, TTS a GUI fungujú správne.

**Spustite aplikáciu pomocou `run_app.bat` a začnite čítať titulky!**
