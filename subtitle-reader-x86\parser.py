import re
import csv

log_file = '/Users/<USER>/Desktop/data_analitics/projects/python/reader/2/app.log.1'

log_content = ''
with open(log_file, 'r') as f:
    log_content = f.read()

log_lines = log_content.strip().split('\n')

table_data = []
last_text = ""

for i, line in enumerate(log_lines):
    current_text = ""
    if "Prijatý text pre detekciu" in line:
        text_match = re.search(r"Prijatý text pre detekciu: '(.*?)', CycleID=(\d+)", line)
        if text_match:
            current_text = text_match.group(1).replace('\\n', ' ')
            cycle_id = int(text_match.group(2))

            similarity = None
            for j in range(i + 1, min(i + 5, len(log_lines))):
                next_line = log_lines[j]
                if "STABLE: podobnosť" in next_line:
                    similarity_match = re.search(r"podobnosť ([\d.]+)", next_line)
                    if similarity_match:
                        similarity = float(similarity_match.group(1))
                        break
                elif "REPLACE" in next_line:
                    similarity_match = re.search(r"podobnosť ([\d.]+)", next_line)
                    if similarity_match:
                        similarity = float(similarity_match.group(1))
                        break
            
            if similarity is not None:
                table_data.append([cycle_id, last_text, current_text, f"{similarity:.2f}"])
        
        last_text = current_text

with open('log_analysis.csv', 'w', newline='') as csvfile:
    writer = csv.writer(csvfile)
    writer.writerow(["Scan Number (CycleID)", "Previous Text", "Next Text", "Similarity"])
    writer.writerows(table_data)

print('Súbor log_analysis.csv bol úspešne vytvorený pomocou parser.py.')
