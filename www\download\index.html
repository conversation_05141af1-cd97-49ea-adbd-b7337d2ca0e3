<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stáhnout VOXO LOXO - Čtečka titulků</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .download-card {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }
        .download-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        .current-os {
            border: 3px solid #28a745 !important;
            box-shadow: 0 0 20px rgba(40, 167, 69, 0.6) !important;
        }
        .os-badge {
            position: absolute;
            top: -10px;
            right: -10px;
            background: #28a745;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <div class="text-center mb-5">
            <h1 class="display-4 mb-3">
                <i class="fas fa-download me-3"></i>
                Stáhnout VOXO LOXO
            </h1>
            <p class="lead">Multiplatformní čtečka titulků pro Windows, macOS a Linux</p>
        </div>

        <div class="row justify-content-center">
            <!-- Windows x86 -->
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card download-card h-100 text-white" data-os="windows-x86">
                    <div class="card-body text-center">
                        <i class="fab fa-windows display-1 text-primary mb-3"></i>
                        <h4>Windows x86 (32-bit)</h4>
                        <p class="mb-3">Automatický inštalátor pre staršie Windows systémy</p>
                        <a href="install-x86.bat" class="btn btn-primary btn-lg mb-2" download>
                            <i class="fas fa-download"></i> Stáhnout inštalátor
                        </a>
                        <p class="text-muted small">
                            Jednosúborový inštalátor<br>
                            Automaticky nainštaluje Python, Tesseract a všetky závislosti
                        </p>
                        <div class="alert alert-info mt-3">
                            <small>
                                <i class="fas fa-info-circle"></i>
                                Spustite ako správca pre automatickú inštaláciu
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Windows ARM64 -->
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card download-card h-100 text-white" data-os="windows-arm64">
                    <div class="card-body text-center">
                        <i class="fab fa-windows display-1 text-success mb-3"></i>
                        <h4>Windows ARM64</h4>
                        <p class="mb-3">Pre moderné Windows na ARM procesoroch</p>
                        <a href="install-arm64.bat" class="btn btn-success btn-lg mb-2" download>
                            <i class="fas fa-download"></i> Stáhnout inštalátor
                        </a>
                        <p class="text-muted small">
                            Optimalizované pre ARM64<br>
                            Surface Pro X, Windows 11 ARM
                        </p>
                        <div class="alert alert-success mt-3">
                            <small>
                                <i class="fas fa-microchip"></i>
                                Natívna ARM64 podpora
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- macOS -->
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card download-card h-100 text-white" data-os="macos">
                    <div class="card-body text-center">
                        <i class="fab fa-apple display-1 text-warning mb-3"></i>
                        <h4>macOS</h4>
                        <p class="mb-3">Pre Mac s Intel a Apple Silicon</p>
                        <a href="../downloads/SubtitleReader-2.0.0-macOS.dmg" class="btn btn-warning btn-lg mb-2">
                            <i class="fas fa-download"></i> Stáhnout DMG
                        </a>
                        <p class="text-muted small">
                            macOS 10.15+<br>
                            M1/M2 optimalizace
                        </p>
                        <div class="alert alert-warning mt-3">
                            <small>
                                <i class="fas fa-apple-alt"></i>
                                Plne funkčná verzia
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Inštalačné pokyny -->
        <div class="row justify-content-center mt-5">
            <div class="col-lg-10">
                <div class="card download-card">
                    <div class="card-body">
                        <h3 class="text-center mb-4">
                            <i class="fas fa-cogs"></i> Inštalačné pokyny
                        </h3>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <h5><i class="fab fa-windows text-primary"></i> Windows x86</h5>
                                <ol>
                                    <li>Stiahnite <code>install-x86.bat</code></li>
                                    <li>Kliknite pravým tlačidlom → "Spustiť ako správca"</li>
                                    <li>Inštalátor automaticky nainštaluje všetko potrebné</li>
                                    <li>Aplikácia sa spustí po dokončení</li>
                                </ol>
                            </div>
                            <div class="col-md-6 mb-3">
                                <h5><i class="fab fa-windows text-success"></i> Windows ARM64</h5>
                                <ol>
                                    <li>Stiahnite <code>install-arm64.bat</code></li>
                                    <li>Spustite ako správca</li>
                                    <li>Automatická inštalácia ARM64 verzií</li>
                                    <li>Optimalizované pre ARM procesory</li>
                                </ol>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12 text-center">
                                <h5><i class="fab fa-apple text-warning"></i> macOS</h5>
                                <p>Otvorte DMG súbor a presuňte aplikáciu do priečinka Applications</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Späť na hlavnú stránku -->
        <div class="text-center mt-5">
            <a href="../" class="btn btn-outline-light btn-lg">
                <i class="fas fa-arrow-left"></i> Späť na hlavnú stránku
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Detekcia OS a zvýraznenie
        function detectOS() {
            const userAgent = navigator.userAgent.toLowerCase();
            const platform = navigator.platform.toLowerCase();

            if (userAgent.includes('win') || platform.includes('win')) {
                // Detekcia ARM64 vs x86
                if (userAgent.includes('arm64') || userAgent.includes('aarch64')) {
                    return 'windows-arm64';
                } else {
                    return 'windows-x86';
                }
            } else if (userAgent.includes('mac') || platform.includes('mac')) {
                return 'macos';
            }
            return 'unknown';
        }

        // Zvýraznenie aktuálneho OS
        document.addEventListener('DOMContentLoaded', function() {
            const currentOS = detectOS();
            
            if (currentOS !== 'unknown') {
                const card = document.querySelector(`[data-os="${currentOS}"]`);
                if (card) {
                    card.classList.add('current-os');
                    card.style.position = 'relative';
                    
                    // Pridaj badge
                    const badge = document.createElement('div');
                    badge.className = 'os-badge';
                    badge.innerHTML = '✓';
                    badge.title = 'Váš operačný systém';
                    card.appendChild(badge);
                }
            }
        });
    </script>
</body>
</html>
