import csv
import datetime
import logging
import threading
import os
from pathlib import Path
import common_config as config

def _escape_csv_text(text):
    """Escapuje text pre CSV - nahradí nové riadky za \\n"""
    if isinstance(text, str):
        return text.replace('\n', '\\n').replace('\r', '\\r')
    return text

# Thread-safe CSV writer
csv_lock = threading.Lock()

def init_csv_logging():
    """Inicializuje CSV súbor s hlavičkami."""
    if not config.CSV_LOGGING_ENABLED:
        return
    
    csv_file = Path(config.CSV_LOG_FILE)
    
    # Ak súbor neexistuje, vyt<PERSON><PERSON><PERSON> ho s hlavičkami
    if not csv_file.exists():
        try:
            with csv_lock:
                with open(csv_file, 'w', newline='', encoding='utf-8') as f:
                    writer = csv.writer(f, quoting=csv.QUOTE_ALL)
                    writer.writerow([
                        'Timestamp',
                        'CycleID',
                        'EventType',
                        'ReadingMode',
                        'SubMode',
                        'PreviousText',
                        'CurrentText',
                        'Similarity',
                        'ChangeType',
                        'TTSText',
                        'Details'
                    ])
            logging.info(f"CSV log súbor inicializovaný: {csv_file}")
        except Exception as e:
            logging.error(f"Chyba pri inicializácii CSV súboru: {e}")

def log_text_comparison(cycle_id, previous_text, current_text, similarity, change_type, details=""):
    """
    Loguje porovnanie textov do CSV.
    
    Args:
        cycle_id: ID OCR cyklu
        previous_text: Predchádzajúci text
        current_text: Aktuálny text
        similarity: Podobnosť medzi textami (0.0-1.0)
        change_type: Typ zmeny (STABLE, INCREMENTAL, REPLACE, CLEAR)
        details: Dodatočné detaily
    """
    if not config.CSV_LOGGING_ENABLED:
        return
    
    try:
        with csv_lock:
            with open(config.CSV_LOG_FILE, 'a', newline='', encoding='utf-8') as f:
                writer = csv.writer(f, quoting=csv.QUOTE_ALL)
                writer.writerow([
                    datetime.datetime.now().isoformat(),
                    cycle_id,
                    'TEXT_COMPARISON',
                    config.reading_mode,
                    getattr(config, 'full_auto_current_sub_mode', ''),
                    _escape_csv_text(previous_text),
                    _escape_csv_text(current_text),
                    f"{similarity:.3f}",
                    change_type,
                    '',  # tts_text - prázdne pre text comparison
                    _escape_csv_text(details)
                ])
    except Exception as e:
        logging.error(f"Chyba pri zápise text comparison do CSV: {e}")

def log_mode_change(old_mode, new_mode, sub_mode="", details=""):
    """
    Loguje zmenu režimu do CSV.
    
    Args:
        old_mode: Starý režim
        new_mode: Nový režim
        sub_mode: Pod-režim (pre full_automatic)
        details: Dodatočné detaily
    """
    if not config.CSV_LOGGING_ENABLED:
        return
    
    try:
        with csv_lock:
            with open(config.CSV_LOG_FILE, 'a', newline='', encoding='utf-8') as f:
                writer = csv.writer(f, quoting=csv.QUOTE_ALL)
                # Ak máme dodatočné dáta o zmene režimu, pridáme ich do details
                mode_change_info = f"{old_mode}->{new_mode}"
                if details:
                    details = f"{mode_change_info} | {details}"
                else:
                    details = mode_change_info

                writer.writerow([
                    datetime.datetime.now().isoformat(),
                    '',  # cycle_id - prázdne pre mode change
                    'MODE_CHANGE',
                    config.reading_mode,  # aktuálny reading_mode
                    getattr(config, 'full_auto_current_sub_mode', sub_mode),  # sub_mode
                    '',  # previous_text - prázdne pre mode change
                    '',  # current_text - prázdne pre mode change
                    '',  # similarity - prázdne pre mode change
                    mode_change_info,  # change_type - informácia o zmene režimu
                    '',  # tts_text - prázdne pre mode change
                    _escape_csv_text(details)
                ])
    except Exception as e:
        logging.error(f"Chyba pri zápise mode change do CSV: {e}")

def log_tts_event(cycle_id, tts_text, event_type="TTS_SENT", details=""):
    """
    Loguje TTS udalosť do CSV.
    
    Args:
        cycle_id: ID OCR cyklu
        tts_text: Text poslaný do TTS
        event_type: Typ TTS udalosti (TTS_SENT, TTS_SKIPPED, TTS_ERROR)
        details: Dodatočné detaily
    """
    if not config.CSV_LOGGING_ENABLED:
        return
    
    try:
        with csv_lock:
            with open(config.CSV_LOG_FILE, 'a', newline='', encoding='utf-8') as f:
                writer = csv.writer(f, quoting=csv.QUOTE_ALL)
                writer.writerow([
                    datetime.datetime.now().isoformat(),
                    cycle_id,
                    event_type,
                    config.reading_mode,
                    getattr(config, 'full_auto_current_sub_mode', ''),
                    '',  # previous_text - prázdne pre TTS
                    '',  # current_text - prázdne pre TTS
                    '',  # similarity - prázdne pre TTS
                    '',  # change_type - prázdne pre TTS
                    _escape_csv_text(tts_text),
                    _escape_csv_text(details)
                ])
    except Exception as e:
        logging.error(f"Chyba pri zápise TTS event do CSV: {e}")

def log_full_auto_detection(cycle_id, detection_result, statistics, details=""):
    """
    Loguje výsledky full automatic detekcie do CSV.
    
    Args:
        cycle_id: ID OCR cyklu
        detection_result: Výsledok detekcie ('static', 'dynamic', None)
        statistics: Štatistiky detekcie (dict)
        details: Dodatočné detaily
    """
    if not config.CSV_LOGGING_ENABLED:
        return
    
    try:
        stats_str = f"STATIC:{statistics.get('static_percent', 0):.1%}, DYNAMIC:{statistics.get('dynamic_percent', 0):.1%}, SAMPLES:{statistics.get('total_samples', 0)}"
        
        with csv_lock:
            with open(config.CSV_LOG_FILE, 'a', newline='', encoding='utf-8') as f:
                writer = csv.writer(f, quoting=csv.QUOTE_ALL)
                writer.writerow([
                    datetime.datetime.now().isoformat(),
                    cycle_id,
                    'FULL_AUTO_DETECTION',
                    config.reading_mode,
                    detection_result or 'ANALYZING',
                    '',  # previous_text
                    '',  # current_text
                    '',  # similarity
                    '',  # change_type
                    '',  # tts_text
                    _escape_csv_text(f"{stats_str} | {details}")
                ])
    except Exception as e:
        logging.error(f"Chyba pri zápise full auto detection do CSV: {e}")

def log_custom_event(event_type, details="", cycle_id="", additional_data=None):
    """
    Loguje vlastnú udalosť do CSV.
    
    Args:
        event_type: Typ udalosti
        details: Detaily udalosti
        cycle_id: ID OCR cyklu (voliteľné)
        additional_data: Dodatočné dáta (dict)
    """
    if not config.CSV_LOGGING_ENABLED:
        return
    
    try:
        with csv_lock:
            with open(config.CSV_LOG_FILE, 'a', newline='', encoding='utf-8') as f:
                writer = csv.writer(f, quoting=csv.QUOTE_ALL)

                # Ak máme dodatočné dáta, pridáme ich do details
                if additional_data:
                    details_parts = [details] if details else []
                    for key, value in additional_data.items():
                        details_parts.append(f"{key}:{value}")
                    details = " | ".join(details_parts)

                writer.writerow([
                    datetime.datetime.now().isoformat(),
                    cycle_id,
                    event_type,
                    config.reading_mode,
                    getattr(config, 'full_auto_current_sub_mode', ''),
                    '',  # previous_text
                    '',  # current_text
                    '',  # similarity
                    '',  # change_type
                    '',  # tts_text
                    _escape_csv_text(details)
                ])
    except Exception as e:
        logging.error(f"Chyba pri zápise custom event do CSV: {e}")

def get_csv_stats():
    """Vráti základné štatistiky z CSV súboru."""
    if not config.CSV_LOGGING_ENABLED or not os.path.exists(config.CSV_LOG_FILE):
        return {}
    
    try:
        with open(config.CSV_LOG_FILE, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            rows = list(reader)
            
            stats = {
                'total_entries': len(rows),
                'text_comparisons': len([r for r in rows if r['event_type'] == 'TEXT_COMPARISON']),
                'mode_changes': len([r for r in rows if r['event_type'] == 'MODE_CHANGE']),
                'tts_events': len([r for r in rows if r['event_type'].startswith('TTS_')]),
                'full_auto_detections': len([r for r in rows if r['event_type'] == 'FULL_AUTO_DETECTION'])
            }
            
            return stats
    except Exception as e:
        logging.error(f"Chyba pri čítaní CSV štatistík: {e}")
        return {}

def get_recent_logs(count=10):
    """
    Získa posledné záznamy z CSV súboru pre anomaly report.

    Args:
        count: Počet posledných záznamov na vrátenie

    Returns:
        List[dict]: Zoznam slovníkov s CSV záznamami
    """
    if not config.CSV_LOGGING_ENABLED:
        return []

    csv_file = Path(config.CSV_LOG_FILE)
    if not csv_file.exists():
        return []

    try:
        with csv_lock:
            with open(csv_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                all_rows = list(reader)
                # Vrátime posledných 'count' riadkov
                recent_rows = all_rows[-count:] if len(all_rows) > count else all_rows
                return recent_rows
    except Exception as e:
        logging.error(f"Chyba pri čítaní CSV súboru pre recent logs: {e}")
        return []
