# 🎬 Screen Subtitle Reader - Cross-Platform Nástroj na čítanie titulkov z obrazovky

**GitHub Repozitár:** [https://github.com/Radisco/subtitle-reader](https://github.com/Radisco/subtitle-reader)

Pokročilý **cross-platform** nástroj pre **real-time čítanie titulkov** z obrazovky s podporou **OCR** a **TTS**. Funguje na **macOS**, **Windows** a **Linux**. Ideálny pre sledovanie videí s titulkami, online kurzy, filmy a seriály.

## 🌍 **Podporované platformy**

| Platform | Status | TTS Engine | Hotkeys | Notes |
|----------|--------|------------|---------|-------|
| **macOS** | ✅ Full | `say` command | `Cmd+Alt+Ctrl+...` | Pôvodná implementácia |
| **Windows** | ✅ Full | SAPI/pyttsx3 | `Ctrl+Alt+Ctrl+...` | Nová cross-platform verzia |
| **Linux** | ✅ Basic | espeak/pyttsx3 | `Ctrl+Alt+Ctrl+...` | Základná funkcionalita |

## 🚀 **Kľúčové vlastnosti**

### **📱 Tri režimy čítania:**
- **🔄 Dynamický režim** - kontinuálne čítanie nových titulkov
- **⏸️ Statický režim** - čítanie na požiadanie (manuálne)
- **🤖 Automatický režim** - inteligentná detekcia nových titulkov

### **🎯 Inteligentná detekcia:**
- **🔍 Automatická detekcia** - rozpoznávanie nových titulkov
- **📊 Filtrovanie duplicitov** - zabránenie opakovania
- **⚡ Optimalizované spracovanie** - rýchle a presné

### **🎯 Cross-platform funkcie:**
- **🔍 OCR** extrakcia textu z obrazovky (Tesseract)
- **🎤 TTS** prečítanie textu (macOS `say`, Windows SAPI, Linux espeak)
- **⌨️ Hotkey** ovládanie (automatické mapovanie klávesových skratiek)
- **🖥️ GUI** s Qt6 tmavým režimom (natívny vzhľad na každej platforme)
- **⚡ M1/M2 optimalizácia** (Metal GPU, MPS) - macOS
- **💾 Cache systém** pre optimalizované spracovanie
- **📝 Multiline podpora** s batch processing
- **🌐 Viacjazyčný systém (i18n/l10n)** s nezávislými nastaveniami
- **🔐 Komerčná licencia** s demo režimom (20 minút denne + hlasové upozornenie)
- **🛡️ Hardware fingerprinting** pre licenčný systém

## 📦 **Inštalácia**

### 🪟 **Windows**
Pozrite si detailný návod: **[README_WINDOWS.md](README_WINDOWS.md)**

```cmd
# Rýchla inštalácia
setup_windows.bat

# Alebo manuálne
python -m venv .venv
.venv\Scripts\activate.bat
pip install -r requirements_windows.txt
```

### 🍎 **macOS** (pôvodná verzia)

### **Požiadavky:**
- **Python 3.8+** (odporúčané 3.10+)
- **Tesseract OCR** pre rozpoznávanie textu
- **macOS 10.15+** / **Windows 10+** / **Linux Ubuntu 20.04+**

### **1. Inštalácia Tesseract OCR:**

#### **macOS:**
```bash
brew install tesseract
```

#### **Windows:**
1. Stiahnite inštalátor z [oficiálnej stránky](https://github.com/UB-Mannheim/tesseract/wiki)
2. Pridajte do systémovej cesty (PATH): `C:\Program Files\Tesseract-OCR`

#### **Linux (Debian/Ubuntu):**
```bash
sudo apt update && sudo apt install tesseract-ocr
```

### **2. Klonovanie repozitára:**
```bash
git clone https://github.com/Radisco/subtitle-reader.git
cd subtitle-reader
```

### 🐧 **Linux**
```bash
# Inštalácia systémových závislostí
sudo apt update
sudo apt install python3 python3-pip python3-venv
sudo apt install tesseract-ocr tesseract-ocr-ces tesseract-ocr-eng
sudo apt install espeak espeak-data  # Pre TTS

# Inštalácia aplikácie
python3 -m venv .venv
source .venv/bin/activate
pip install -r requirements_linux.txt
```

### **3. Virtuálne prostredie (všeobecné):**
```bash
python3 -m venv .venv
source .venv/bin/activate  # macOS/Linux
# .\.venv\Scripts\activate  # Windows
```

### **4. Inštalácia závislostí:**

#### **Cross-platform závislosti:**
```bash
# macOS (pôvodné)
pip install -r requirements.txt

# Windows
pip install -r requirements_windows.txt

# Linux
pip install -r requirements_linux.txt
```

#### **Testovanie cross-platform funkcionality:**
```bash
# Test všetkých platforiem
python test_cross_platform.py

# Overenie TTS hlasov
python -c "from platform_utils import get_tts_provider; print(len(get_tts_provider().get_available_voices()))"
```

## 🎮 **Použitie**

### **Spustenie aplikácie:**

#### **Qt GUI:**
```bash
# Štandardné Python prostredie
python3 main_qt.py

# Python 3.10 Framework (macOS)
/Library/Frameworks/Python.framework/Versions/3.10/bin/python3 main_qt.py

# Pyenv prostredie
pyenv activate reader-env
python main_qt.py

# Test prostredie (ak existuje)
./test_env/bin/python main_qt.py
```

### **Prvé nastavenie:**

1. **Výber oblasti** - označte myšou oblasť s titulkami na obrazovke
2. **Režim čítania** - vyberte dynamický/statický/automatický
3. **Jazyk TTS** - nastavte jazyk pre prečítanie
4. **Optimalizácia** - nastavte prahy detekcie a filtre

### **Ovládanie:**

#### **Hotkey (globálne klávesové skratky):**
- **`Cmd+Shift+R`** (macOS) / **`Ctrl+Shift+R`** (Windows/Linux) - zapnúť/vypnúť čítanie
- **`Cmd+Shift+T`** (macOS) / **`Ctrl+Shift+T`** (Windows/Linux) - test TTS

#### **GUI ovládanie:**
- **▶️ Štart/Stop** - spustenie/zastavenie čítania
- **⚙️ Nastavenia** - konfigurácia všetkých parametrov
- **📊 Štatistiky** - sledovanie výkonu a logov

## 🎯 **Inteligentná detekcia titulkov**

### **Pokročilé algoritmy:**

#### **🔍 Automatická detekcia nových titulkov**
- **Podobnosť:** Rozpoznávanie nových vs. starých titulkov
- **Filtrovanie:** Zabránenie opakovania rovnakého obsahu
- **Optimalizácia:** Rýchle spracovanie bez zbytočných čítaní

#### **📊 Dynamické prahy**
- **Adaptívne nastavenie:** Automatické prispôsobenie rôznym typom obsahu
- **Stabilita:** Detekcia stabilných vs. animovaných titulkov
- **Presnosť:** Minimalizácia falošných pozitívnych detekcií

#### **⚡ Optimalizované spracovanie**
- **Cache systém:** Uloženie spracovaných textov
- **Batch processing:** Efektívne spracovanie viacerých riadkov
- **Memory management:** Optimálne využitie systémových zdrojov

## 🔧 **Technológie a závislosti**

### **Hlavné komponenty:**
- **Python 3.8+** - hlavný programovací jazyk
- **Tesseract OCR** - rozpoznávanie textu z obrázkov
- **PyQt5** - moderné GUI rozhranie
- **Tkinter** - legacy GUI rozhranie (fallback)

### **OCR a spracovanie obrazu:**
- **`pytesseract`** - Python wrapper pre Tesseract
- **`Pillow (PIL)`** - spracovanie obrázkov
- **`mss`** - snímanie obrazovky (cross-platform)

### **TTS (Text-to-Speech):**
- **macOS:** `say` command (systémový)
- **Windows:** `pyttsx3` (SAPI)
- **Linux:** `espeak` alebo `festival`

### **Prekladač (voliteľné):**
- **`transformers`** - Hugging Face Transformers knižnica
- **`torch`** - PyTorch pre neural networks
- **`ctranslate2`** - optimalizovaný inference engine
- **`sentencepiece`** - tokenizácia pre niektoré modely

### **GUI a systémové:**
- **`PyQt6`** - Qt6 bindings pre Python (cross-platform)
- **`pynput`** - globálne hotkey a input handling
- **`pyobjc`** - macOS system integrácia (200+ frameworkov) - iba macOS
- **`pywin32`** - Windows system integrácia - iba Windows
- **`WMI`** - Windows Management Instrumentation - iba Windows

### **Detailné závislosti:**
- **`mss`** - rýchle snímanie obrazovky (cross-platform)
- **`Pillow (PIL)`** - spracovanie a manipulácia obrázkov
- **`pytesseract`** - Python wrapper pre Tesseract OCR
- **`pynput`** - globálne hotkey a input handling
- **`pyttsx3`** - Text-to-Speech pre Windows/Linux
- **`Levenshtein`** + **`RapidFuzz`** - porovnávanie textov a detekcia zmien
- **`pyobjc`** - macOS system integrácia (200+ Objective-C frameworkov)

## ⚡ **Výkon a optimalizácie**

### **M1/M2 Apple Silicon optimalizácie:**
- **Metal GPU** pre CTranslate2 (Speed režim)
- **MPS (Metal Performance Shaders)** pre PyTorch modely
- **int8_float16 kvantizácia** pre optimálny pomer rýchlosť/kvalita
- **Batch processing** pre multiline texty
- **4-10x rýchlejšie** preklady oproti CPU

### **Výkonnostné metriky:**

#### **OCR spracovanie:**
- **Snímanie obrazovky:** 1-5ms
- **OCR rozpoznávanie:** 50-200ms
- **Text processing:** 1-10ms

#### **Prekladač (M1/M2 GPU):**
- **Speed režim:** 5-50ms
- **Balanced režim:** 50-200ms
- **Quality režim:** 200-500ms

#### **TTS prehrávanie:**
- **macOS `say`:** 100-500ms (podľa dĺžky)
- **Windows SAPI:** 200-800ms
- **Linux espeak:** 100-300ms

### **Cache systém:**
- **Translation cache** - uloženie prekladov pre rýchlejšie opätovné použitie
- **Model persistence** - modely zostávajú v pamäti
- **OCR cache** - podobné texty sa nezopakujú

## 🛠️ **Riešenie problémov**

### **Inštalácia a závislosti:**

#### **Tesseract nie je nájdený:**
```bash
# Overenie inštalácie
tesseract --version

# macOS
brew install tesseract

# Windows - pridajte do PATH:
C:\Program Files\Tesseract-OCR
```

#### **Python dependencies chýbajú:**
```bash
# Aktualizácia pip
pip install --upgrade pip

# Reinstall dependencies
pip install -r requirements.txt --force-reinstall
```

#### **Prekladač nefunguje - "No module named 'transformers'":**
```bash
# Skontrolujte, aké Python prostredie používate
which python3

# Pre Python 3.10 Framework (macOS)
/Library/Frameworks/Python.framework/Versions/3.10/bin/pip3 install torch transformers

# Pre pyenv prostredie
pyenv activate reader-env
pip install torch transformers

# Pre test_env prostredie
./test_env/bin/pip install torch transformers
```

#### **PyQt5 problémy:**
```bash
# macOS
pip install PyQt5

# Linux
sudo apt install python3-pyqt5

# Windows
pip install PyQt5
```

### **OCR problémy:**

#### **Nerozpoznáva text správne:**
- Zvýšte **OCR_CONFIDENCE_THRESHOLD** (0-100)
- Zväčšite oblasť snímkovania
- Skontrolujte kontrast a veľkosť textu
- Zmeňte jazyk OCR v nastaveniach

#### **Pomalé OCR:**
- Zmenšite oblasť snímkovania
- Znížte frekvenciu snímkovania
- Skontrolujte CPU zaťaženie

### **TTS problémy:**

#### **TTS nefunguje:**
```bash
# macOS - test
say "Hello world"

# Windows - test v PowerShell
Add-Type -AssemblyName System.Speech
$speak = New-Object System.Speech.Synthesis.SpeechSynthesizer
$speak.Speak("Hello world")

# Linux
sudo apt install espeak
espeak "Hello world"
```

#### **Pomalé TTS:**
- Zvýšte **TTS_RATE** (slová za minútu)
- Skráťte texty pre TTS
- Skontrolujte systémové TTS nastavenia

### **Prekladač problémy:**

#### **Model sa nestiahol:**
```bash
# Manuálne stiahnutie
python3 -c "from transformers import MarianMTModel; MarianMTModel.from_pretrained('Helsinki-NLP/opus-mt-en-cs')"
```

#### **Nedostatok pamäte:**
- Zatvorte iné aplikácie
- Použite Speed režim namiesto Quality
- Reštartujte aplikáciu

#### **Pomalý preklad:**
- Prepnite na Speed režim
- Skontrolujte či sa používa GPU (M1/M2)
- Vyčistite translation cache

### **GUI problémy:**

#### **Qt GUI sa nespustí:**
```bash
# Nainštalujte Qt5
pip install PyQt5
```

#### **Hotkey nefungujú:**
- Skontrolujte povolenia pre Accessibility (macOS)
- Reštartujte aplikáciu s admin právami (Windows)
- Skontrolujte konflikt s inými aplikáciami

## 📊 **Konfigurácia**

### **Hlavné nastavenia v `common_config.py`:**

```python
# OCR nastavenia
OCR_CONFIDENCE_THRESHOLD = 60  # 0-100
OCR_LANGUAGE = 'eng'           # tesseract jazyk

# TTS nastavenia
TTS_RATE = 200                 # slová za minútu
TTS_VOICE = 'default'          # hlas

# Režimy čítania
READING_MODE = 'dynamic'       # 'static', 'dynamic', 'automatic'

# Prekladač
TRANSLATION_ENABLED = False    # zapnúť/vypnúť
TRANSLATION_SOURCE_LANGUAGE = 'en'
TRANSLATION_TARGET_LANGUAGE = 'cs'

# Hotkey
HOTKEY_TOGGLE_READING = 'cmd+shift+r'  # macOS
# HOTKEY_TOGGLE_READING = 'ctrl+shift+r'  # Windows/Linux
```

### **Pokročilé nastavenia:**

```python
# Výkon
ENABLE_FAST_MODE = True        # rýchly režim
MAX_LENGTH = 256               # max dĺžka prekladu
BEAM_SIZE = 4                  # kvalita vs rýchlosť

# Cache
TRANSLATION_CACHE_SIZE = 1000  # počet uložených prekladov
MODEL_PERSISTENCE = True       # modely v pamäti

# Logging
LOG_LEVEL = 'INFO'             # 'DEBUG', 'INFO', 'WARNING', 'ERROR'
LOG_TO_FILE = True             # logovanie do súboru
```

## 🧪 **Testovanie a vývoj**

### **Test súbory:**
```bash
# Test základnej funkcionality
python3 test_translation.py

# Test M1/M2 optimalizácií
python3 test_m1_m2_optimizations.py

# Test multiline prekladov
python3 test_multiline_translation.py

# Test hotkey systému
python3 test_hotkeys.py

# Test M2M100 prekladača
python3 test_qt_m2m100.py
```

### **Vývojové poznámky:**
- **Logy:** `app.log` obsahuje detailné informácie o behu aplikácie
- **CSV logy:** `log_analysis.csv` pre analýzu výkonu
- **OCR anomálie:** `OCR_Anomaly_Reports/` pre debugging OCR problémov
- **Cache:** `~/.cache/huggingface/` pre stiahnuté modely

### **Štruktúra projektu:**
```
subtitle-reader/
├── main_qt.py              # Qt GUI spúšťač
├── qt_gui.py               # Qt GUI implementácia
├── advanced_params_dialog.py # Qt dialóg pre nastavenia
├── translation_manager.py  # Prekladač engine
├── multi_translator.py     # Multi-režim prekladač
├── ocr_core.py             # OCR spracovanie
├── tts_manager.py          # TTS engine
├── hotkey_manager.py       # Globálne hotkey
├── common_config.py        # Konfigurácia
├── requirements.txt        # Základné závislosti
├── requirements_translation.txt  # Prekladač závislosti
├── static_mode/            # Statický režim
├── dynamic_mode/           # Dynamický režim
├── automatic_mode/         # Automatický režim
└── full_automatic_mode/    # Plne automatický režim
```

## 🚀 **Budúce vylepšenia**

### **Plánované funkcie:**
- [ ] **Cloud TTS** - Google/Azure/AWS TTS integrácia
- [ ] **Vlastné modely** - trénovanie špecializovaných prekladačov
- [ ] **Batch OCR** - spracovanie viacerých oblastí naraz
- [ ] **Auto-detekcia jazyka** - automatické rozpoznanie jazyka titulkov
- [ ] **Subtitle export** - export titulkov do SRT/VTT formátov
- [ ] **Web interface** - ovládanie cez webový prehliadač
- [ ] **Mobile app** - iOS/Android verzie

### **Optimalizácie:**
- [ ] **GPU batch processing** - ešte rýchlejšie preklady
- [ ] **Model quantization** - menšie modely, rovnaká kvalita
- [ ] **Streaming TTS** - real-time audio generovanie
- [ ] **OCR preprocessing** - lepšie rozpoznávanie textu

## 🔐 **Demo režim a licencovanie**

Aplikácia obsahuje pokročilý demo systém s nasledujúcimi funkciami:

### **Demo režim (10 minút denne):**
- ⏰ **Denný limit**: 20 minút používania zadarmo
- 🔊 **Hlasové upozornenie**: Po vypršaní času sa automaticky prečíta správa
- 📱 **Vizuálny indikátor**: Odpočítavanie zostávajúceho času v GUI
- 🔄 **Denné obnovenie**: Čas sa automaticky obnoví každý deň o polnoci

### **Hlasové upozornenie:**
```
"Vypršel limit demo verze pro dnešek.
Buď si zakupte plnou verzi, nebo počkejte do zítřka."
```

### **Konfigurácia TTS správy:**
```python
# common_config.py
DEMO_EXPIRED_TTS_ENABLED = True  # Zapnúť/vypnúť hlasové upozornenie
DEMO_EXPIRED_TTS_VOICE_PREFERENCE = "czech"  # Preferovaný jazyk hlasu
DEMO_EXPIRED_MESSAGE = "Vlastná správa..."  # Vlastný text správy
```

### **Testovanie:**
```bash
# Test demo expired TTS
python test_demo_expired_tts.py

# Test v GUI (tlačidlo "🔊 Test TTS" v demo widgete)
```

## 📄 **Licencia**

Tento projekt je licencovaný pod **MIT licenciou**. Pozrite si súbor `LICENSE` pre viac informácií.

## 🤝 **Prispievanie**

Príspevky sú vítané! Prosím:

1. **Fork** repozitár
2. Vytvorte **feature branch** (`git checkout -b feature/AmazingFeature`)
3. **Commit** zmeny (`git commit -m 'Add some AmazingFeature'`)
4. **Push** do branch (`git push origin feature/AmazingFeature`)
5. Otvorte **Pull Request**

### **Reporting Issues:**
- Použite **GitHub Issues** pre bug reporty
- Priložte **logy** (`app.log`) a **system info**
- Opíšte **kroky na reprodukciu** problému

## 📞 **Kontakt a podpora**

- **GitHub Issues:** [https://github.com/Radisco/subtitle-reader/issues](https://github.com/Radisco/subtitle-reader/issues)
- **Dokumentácia:** Všetky `.md` súbory v repozitári
- **Wiki:** GitHub Wiki pre pokročilé témy

## 🎯 **Rýchly štart**

```bash
# 1. Klonovanie
git clone https://github.com/Radisco/subtitle-reader.git
cd subtitle-reader

# 2. Virtuálne prostredie
python3 -m venv .venv
source .venv/bin/activate  # macOS/Linux

# 3. Závislosti
pip install -r requirements.txt
pip install -r requirements_translation.txt  # voliteľné

# 4. Spustenie
python3 main_qt.py  # Qt GUI
```

**Hotovo!** 🎉 Aplikácia je pripravená na použitie.

---

## 🌐 **Viacjazyčný systém (i18n/l10n)**

Aplikácia obsahuje pokročilý systém pre internationalization (i18n) a localization (l10n) s nezávislými nastaveniami pre GUI a čítanie titulkov.

### **🏗️ Architektúra systému:**

#### **1. Jazykové súbory (JSON formát):**
```
locales/
├── cs.json    # Čeština (výchozí jazyk)
├── sk.json    # Slovenčina
├── en.json    # English (fallback)
└── ...        # Ľahko rozšíriteľné
```

#### **2. Dva nezávislé jazykové systémy:**

**A) Jazyk aplikácie (GUI):**
- 🎨 Mení texty v GUI (tlačidlá, labely, tooltips)
- 🌍 Dropdown s vlajkami: 🇨🇿🇸🇰🇺🇸🇩🇪🇫🇷
- 🔄 Automatické obnovenie GUI textov
- 💾 Uloženie do `config.APP_LANGUAGE`

**B) Jazyk čítania titulkov (OCR + TTS):**
- 🔍 Mení OCR jazyk (Tesseract: ces, slk, eng...)
- 🔊 Mení TTS jazyk (Apple: cs-CZ, sk-SK, en-US...)
- 🌐 Dropdown s 8 jazykmi: 🇨🇿🇸🇰🇺🇸🇩🇪🇫🇷🇪🇸🇮🇹🇷🇺
- ⚙️ Automatické nastavenie OCR/TTS
- 💾 Uloženie do `config.READING_LANGUAGE`

### **🔧 Implementácia (`i18n_manager.py`):**

#### **Translator trieda:**
```python
from i18n_manager import get_translator

translator = get_translator()
text = translator.t("start_reading")  # → "Spustit čtení"
```

#### **LanguageManager trieda:**
```python
from i18n_manager import get_language_manager

lang_mgr = get_language_manager()
lang_mgr.set_reading_language("sk")  # Nastaví OCR + TTS na slovenčinu
```

#### **Mapovanie jazykov:**
```python
LANGUAGE_MAPPINGS = {
    "cs": {
        "name": "Čeština",
        "flag": "🇨🇿",
        "tesseract": "ces",     # OCR jazyk
        "apple_tts": "cs-CZ",   # TTS jazyk
        "iso": "cs-CZ"
    },
    "sk": {
        "name": "Slovenčina",
        "flag": "🇸🇰",
        "tesseract": "slk",
        "apple_tts": "sk-SK",
        "iso": "sk-SK"
    }
    # ... ďalšie jazyky
}
```

### **🚀 Výhody systému:**
- ✅ **Ľahko rozšíriteľné** - Stačí pridať nový JSON súbor
- ✅ **Nezávislé systémy** - GUI jazyk ≠ čítací jazyk
- ✅ **Automatická detekcia** - Pripravené na budúce rozšírenia
- ✅ **Profesionálny štandard** - Používa i18n best practices
- ✅ **Fallback systém** - Nikdy sa nezrúti kvôli chýbajúcemu prekladu
- ✅ **Konzistentné hlášky** - TTS hlášky v jazyku čítania

### **📝 Pridanie nového jazyka:**

1. **Vytvorte jazykový súbor:**
```bash
cp locales/cs.json locales/de.json
# Upravte preklady v de.json
```

2. **Pridajte mapovanie do `i18n_manager.py`:**
```python
"de": {
    "name": "Deutsch",
    "flag": "🇩🇪",
    "tesseract": "deu",
    "apple_tts": "de-DE",
    "iso": "de-DE"
}
```

3. **Reštartujte aplikáciu** - Nový jazyk sa automaticky zobrazí v dropdown menu.

### **🔄 Funkčné prepínanie jazykov:**
- 🎨 **`set_app_language()`** - Zmení GUI jazyk + obnoví texty
- 📖 **`set_reading_language()`** - Zmení OCR + TTS jazyk
- 🔄 **Callback metódy** - Automatické aktualizácie
- 🎯 **Refresh GUI** - Okamžité zobrazenie zmien

**Príklad použitia:**
```python
# Zmení jazyk aplikácie na slovenčinu
set_app_language("sk")

# Zmení jazyk čítania na nemčinu (OCR + TTS)
set_reading_language("de")
```

Systém je pripravený na ľahké pridávanie nových jazykov a automatickú detekciu jazyka v budúcnosti.

## 🌍 **Cross-Platform Rozdiely**

### **Funkčné rozdiely medzi platformami:**

| Funkcia | macOS | Windows | Linux | Poznámky |
|---------|-------|---------|-------|----------|
| **TTS Engine** | `say` command | SAPI/pyttsx3 | espeak/pyttsx3 | Kvalita hlasov sa líši |
| **Voice Quality** | Enhanced/Premium | Standard | Basic | macOS má najlepšie hlasy |
| **Hotkeys** | `Cmd+Alt+Ctrl+...` | `Ctrl+Alt+Ctrl+...` | `Ctrl+Alt+Ctrl+...` | Automatické mapovanie |
| **Active App Detection** | AppKit | win32gui | xdotool/wmctrl | Rôzne API |
| **Browser URL Detection** | AppleScript | Obmedzené | Obmedzené | Najlepšie na macOS |
| **Hardware Fingerprinting** | IOKit | WMI | /sys filesystem | Rôzne zdroje údajov |
| **System Language** | defaults | Registry | locale | Rôzne metódy detekcie |

### **Výkonnostné rozdiely:**

- **macOS**: Najlepšia integrácia, najkvalitnejšie TTS hlasy
- **Windows**: Dobrá kompatibilita, štandardné SAPI hlasy
- **Linux**: Základná funkcionalita, espeak TTS

### **Inštalačné súbory:**

- **macOS**: `requirements.txt` (pôvodný)
- **Windows**: `requirements_windows.txt` + `setup_windows.bat`
- **Linux**: `requirements_linux.txt`

### **Testovanie:**

```bash
# Test cross-platform funkcionality
python test_cross_platform.py

# Overenie TTS hlasov
python -c "from platform_utils import get_tts_provider; provider = get_tts_provider(); print(f'TTS voices: {len(provider.get_available_voices())}')"

# Test hotkeys
python -c "from platform_utils import normalize_hotkey_combination; print(normalize_hotkey_combination('<cmd>+<alt>+<ctrl>+v'))"
```

---

## 📞 **Podpora a kontakt**

- **GitHub Issues**: [Nahlásiť problém](https://github.com/Radisco/subtitle-reader/issues)
- **Windows Setup**: Pozrite si [README_WINDOWS.md](README_WINDOWS.md)
- **Cross-platform Testing**: Spustite `python test_cross_platform.py`

**Poznámka**: Aplikácia je teraz plne cross-platform s automatickým mapovaním funkcií pre každú platformu.

---

## 🌐 **Web stránka**

Kompletná komerčná web stránka s:
- **Responzívny dizajn** - Bootstrap 5
- **Vlastné pozadia** - hero.png (rolovací) a faq.png (statický)
- **Stripe platby** - bezpečné online platby
- **Registračný systém** - automatická aktivácia licencií
- **FAQ sekcia** - vycentrovaná s priehľadným pozadím
- **Cross-platform informácie** - detaily pre každý OS

### 🖼️ **Pozadia stránky**

**Hero sekcia:**
- `hero.png` - rolovací pozadie s gradientom
- Rozmer: minimálne 1200x800px
- Správanie: roluje sa so stránkou

**FAQ sekcia:**
- `faq.png` - statické pozadie (fixed)
- Rozmer: minimálne 1200x800px
- Správanie: zostáva statické pri rolovaní
- Obsah má priehľadné pozadie

### 🛠️ **Vytvorenie obrázkov**

1. Otvorte `www/create-placeholder-images.html`
2. Stiahnite vygenerované `hero.png` a `faq.png`
3. Uložte ich do `www/` priečinka
4. Otvorte `www/test-backgrounds.html` pre test
5. Otvorte `www/index.php` pre finálny výsledok

---

**Aplikácia je pripravená na použitie!** 🎉
