"""
Full Automatic Mode - Detekcia typu titulkov pomocou dynamického režimu.

Táto implementácia používa existujúcu logiku dynamického režimu pre detekciu typu titulkov.
Sleduje premenné z dynamic_mode a rozhoduje na základe vzorcov správania.

LOGIKA DETEKCIE:
1. Spustí dočasný dynamický režim
2. Sleduje current_first_line a current_second_line
3. Detekuje vzorce:
   - DYNAMICKÉ: prvý riadok stabilný + druhý riadok rastie
   - STATICKÉ: text stabilný alebo úplná výmena bez rastu
"""

import logging
import time
import threading
import queue

import common_config as config
import common_utils
from tts_manager import speak_text
import csv_logger


def reset_detection_state():
    """Resetuje stav detekcie na začiatok."""
    config.full_auto_detection_first_line = ""
    config.full_auto_detection_second_line = ""
    config.full_auto_first_line_stable_count = 0
    config.full_auto_second_line_growth_count = 0
    config.full_auto_detection_cycles = 0
    config.full_auto_last_detection_time = time.time()
    
    logging.info("[FULL_AUTO_DETECT] Stav detekcie resetovaný")


def analyze_dynamic_mode_state():
    """
    Analyzuje aktuálny stav dynamického režimu a rozhoduje o type titulkov.
    
    Vráti: 'dynamic', 'static' alebo None (pokračovať v detekcii)
    """
    current_first = getattr(config, 'current_first_line', '')
    current_second = getattr(config, 'current_second_line', '')
    
    config.full_auto_detection_cycles += 1
    
    logging.debug(f"[FULL_AUTO_DETECT] Cyklus {config.full_auto_detection_cycles}: "
                 f"1.'{current_first}' | 2.'{current_second}'")
    
    # === DETEKCIA DYNAMICKÝCH TITULKOV ===
    if current_first and current_second:
        # Máme dvojriadkový text
        
        # Kontrola stability prvého riadku
        if current_first == config.full_auto_detection_first_line:
            config.full_auto_first_line_stable_count += 1
        else:
            # Prvý riadok sa zmenil - reset detekcie
            logging.debug(f"[FULL_AUTO_DETECT] Prvý riadok sa zmenil: '{config.full_auto_detection_first_line}' → '{current_first}' - RESET")
            config.full_auto_detection_first_line = current_first
            config.full_auto_detection_second_line = current_second
            config.full_auto_first_line_stable_count = 1
            config.full_auto_second_line_growth_count = 0
            return None
        
        # Kontrola rastu druhého riadku
        if (config.full_auto_detection_second_line and 
            current_second.startswith(config.full_auto_detection_second_line) and 
            len(current_second) > len(config.full_auto_detection_second_line)):
            
            config.full_auto_second_line_growth_count += 1
            logging.debug(f"[FULL_AUTO_DETECT] Druhý riadok rastie: '{config.full_auto_detection_second_line}' → '{current_second}'")
        
        # Aktualizácia sledovaných riadkov
        config.full_auto_detection_first_line = current_first
        config.full_auto_detection_second_line = current_second
        
        # ROZHODNUTIE: DYNAMICKÉ TITULKY
        if (config.full_auto_first_line_stable_count >= config.FULL_AUTO_MIN_STABLE_CYCLES and
            config.full_auto_second_line_growth_count >= config.FULL_AUTO_MIN_GROWTH_CYCLES):
            
            logging.info(f"[FULL_AUTO_DETECT] ✅ DYNAMICKÉ TITULKY detekované! "
                        f"Stabilita 1. riadku: {config.full_auto_first_line_stable_count}, "
                        f"Rast 2. riadku: {config.full_auto_second_line_growth_count}")
            return 'dynamic'
    
    # === DETEKCIA STATICKÝCH TITULKOV ===
    elif current_first and not current_second:
        # Jednoriadkový text - môže byť statický
        if current_first == config.full_auto_detection_first_line:
            config.full_auto_first_line_stable_count += 1
        else:
            # Text sa zmenil
            config.full_auto_detection_first_line = current_first
            config.full_auto_first_line_stable_count = 1
        
        # ROZHODNUTIE: STATICKÉ TITULKY
        if config.full_auto_first_line_stable_count >= config.FULL_AUTO_STATIC_STABILITY_CYCLES:
            logging.info(f"[FULL_AUTO_DETECT] ✅ STATICKÉ TITULKY detekované! "
                        f"Stabilita: {config.full_auto_first_line_stable_count} cyklov")
            return 'static'
    
    # === TIMEOUT DETEKCIE ===
    if config.full_auto_detection_cycles >= config.FULL_AUTO_MAX_DETECTION_CYCLES:
        # Príliš dlhá detekcia - rozhodneme na základe toho, čo sme videli
        if config.full_auto_second_line_growth_count > 0:
            logging.info(f"[FULL_AUTO_DETECT] ⏰ TIMEOUT - rozhodnutie na základe rastu: DYNAMICKÉ")
            return 'dynamic'
        else:
            logging.info(f"[FULL_AUTO_DETECT] ⏰ TIMEOUT - rozhodnutie na základe stability: STATICKÉ")
            return 'static'
    
    # Pokračovať v detekcii
    return None


def start_detection_via_dynamic_mode():
    """
    Spustí detekciu typu titulkov pomocou dynamického režimu.
    """
    logging.info("[FULL_AUTO_DETECT] Spúšťam detekciu pomocou dynamického režimu")
    
    # Reset stavu detekcie
    reset_detection_state()
    
    # Spustenie dočasného dynamického režimu pre detekciu
    import dynamic_mode.dynamic_logic
    dynamic_mode.dynamic_logic.start_dynamic_mode_processing()
    
    logging.info("[FULL_AUTO_DETECT] Dočasný dynamický režim spustený pre detekciu")


def stop_detection_via_dynamic_mode():
    """
    Zastaví detekciu a vyčistí dočasný dynamický režim.
    """
    logging.info("[FULL_AUTO_DETECT] Zastavujem detekciu pomocou dynamického režimu")
    
    # Zastavenie dočasného dynamického režimu
    import dynamic_mode.dynamic_logic
    dynamic_mode.dynamic_logic.stop_dynamic_mode_processing()
    
    # Reset stavu detekcie
    reset_detection_state()
    
    logging.info("[FULL_AUTO_DETECT] Detekcia zastavená")


def detection_worker():
    """
    Worker thread pre kontinuálnu detekciu typu titulkov.
    Sleduje stav dynamického režimu a rozhoduje o type titulkov.
    """
    logging.info("[FULL_AUTO_DETECT] Detection worker spustený")
    
    while config.is_reading and config.reading_mode == 'full_automatic':
        try:
            # Analyzujeme aktuálny stav dynamického režimu
            detected_type = analyze_dynamic_mode_state()
            
            if detected_type:
                # Typ titulkov bol detekovaný!
                logging.info(f"[FULL_AUTO_DETECT] 🎯 Detekovaný typ: {detected_type.upper()}")
                
                # Zastavíme detekčný dynamický režim
                import dynamic_mode.dynamic_logic
                dynamic_mode.dynamic_logic.stop_dynamic_mode_processing()
                
                # Prepneme na detekovaný režim
                config.reading_mode = detected_type
                
                if detected_type == 'dynamic':
                    from i18n_manager import get_tts_message
                    tts_msg = get_tts_message("tts_detected_dynamic")
                    speak_text(tts_msg)
                    # Spustíme skutočný dynamický režim
                    dynamic_mode.dynamic_logic.start_dynamic_mode_processing()

                elif detected_type == 'static':
                    from i18n_manager import get_tts_message
                    tts_msg = get_tts_message("tts_detected_static")
                    speak_text(tts_msg)
                    # Spustíme statický režim
                    import static_mode.static_logic
                    static_mode.static_logic.start_static_mode_processing()
                
                # Ukončíme detekciu
                break
            
            # Krátka pauza medzi analýzami
            time.sleep(0.2)
            
        except Exception as e:
            logging.error(f"[FULL_AUTO_DETECT] Chyba v detection worker: {e}")
            time.sleep(0.5)
    
    logging.info("[FULL_AUTO_DETECT] Detection worker ukončený")
