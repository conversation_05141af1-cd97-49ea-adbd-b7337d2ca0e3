#!/bin/bash

# 🎬 Subtitle Reader - <PERSON><PERSON><PERSON><PERSON><PERSON> spú<PERSON> pre macOS
# Vytvorené: 2025-09-25
# Dvojklikom spustíš aplikáciu bez terminálu

# Získaj adresár aplikácie
APP_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
cd "$APP_DIR"

# Nájdi Python (testovaná cesta)
PYTHON_CMD="/Library/Frameworks/Python.framework/Versions/3.10/bin/python3"

# Ak sa nenašiel, skús alternatívy
if [ ! -f "$PYTHON_CMD" ]; then
    if command -v python3 &> /dev/null; then
        PYTHON_CMD="python3"
    elif command -v python &> /dev/null; then
        PYTHON_CMD="python"
    else
        osascript -e 'display dialog "❌ Python nie je nainštalovaný!\n\nNainštaluj Python z https://python.org" buttons {"OK"} default button "OK"'
        exit 1
    fi
# Nájdi správny Python (preferujeme python3)
if command -v python3 &>/dev/null; then
    PYTHON_CMD="python3"
elif command -v python &>/dev/null; then
    PYTHON_CMD="python"
else
    osascript -e 'display notification "❌ Python nie je nainštalovaný!" with title "Subtitle Reader Chyba"'
    exit 1
fi

# Kontrola hlavného súboru
if [ ! -f "main_qt.py" ]; then
    osascript -e 'display dialog "❌ Súbor main_qt.py sa nenašiel!\n\nSkontroluj, či si v správnom adresári." buttons {"OK"} default button "OK"'
    osascript -e 'display notification "❌ Súbor main_qt.py sa nenašiel!" with title "Subtitle Reader Chyba"'
    exit 1
fi

# Kontrola závislostí
if ! $PYTHON_CMD -c "import PyQt6" 2>/dev/null; then
    osascript -e 'display dialog "⚠️ Chýbajú závislosti!\n\nSpusti najprv:\npip3 install -r requirements.txt" buttons {"OK"} default button "OK"'
if ! "$PYTHON_CMD" -c "import PyQt6, pytesseract, PIL, pynput" &>/dev/null; then
    osascript -e 'display notification "⚠️ Chýbajú závislosti. Spusti SpustiApp.command" with title "Subtitle Reader"'
    exit 1
fi

# Spusti aplikáciu na pozadí (bez terminálu)
nohup $PYTHON_CMD main_qt.py > /dev/null 2>&1 &

# Zobraz notifikáciu o spustení
osascript -e 'display notification "🎬 Subtitle Reader sa spúšťa..." with title "Subtitle Reader"'
osascript -e 'display notification "Aplikácia sa spúšťa na pozadí..." with title "🎬 Subtitle Reader"'

# Zatvor terminál
osascript -e 'tell application "Terminal" to close first window' > /dev/null 2>&1 &
osascript -e 'tell application "Terminal" to close (first window whose frontmost is true)' >/dev/null 2>&1

exit 0
