# -*- coding: utf-8 -*-
"""
Licenčný systém pre Subtitle Reader aplikáciu
Spravuje demo režim, licencie a komunikáciu so serverom
"""

import json
import logging
import requests
import hashlib
import time
from datetime import datetime, timed<PERSON>ta
from pathlib import Path
from typing import Optional, Dict, Any
import threading

import common_config as config
from hardware_fingerprint import get_hardware_fingerprint


class LicenseManager:
    """Správca licencií a demo režimu."""
    
    def __init__(self):
        self.license_file = Path("license.json")
        self.server_url = "https://voxoloxo.com/api"  # API endpoint
        self.license_data = {}
        self.demo_time_used_today = 0  # sekundy
        self.demo_limit_daily = 20 * 60  # 20 minút denne
        self.is_demo_active = False
        self.demo_start_time = None
        self.license_check_interval = 3600  # 1 hodina
        self.last_license_check = 0
        
        # Načítanie licenčných údajov
        self.load_license_data()
        
        # Spustenie background thread pre pravidelné overovanie
        self.start_license_monitor()
    
    def load_license_data(self):
        """Načíta licenčné údaje z lokálneho súboru."""
        try:
            if self.license_file.exists():
                with open(self.license_file, 'r', encoding='utf-8') as f:
                    self.license_data = json.load(f)
                logging.info("📄 Licenčné údaje načítané")
            else:
                # Prvé spustenie - vytvor demo licenciu
                self.create_demo_license()
        except Exception as e:
            logging.error(f"❌ Chyba pri načítavaní licencie: {e}")
            self.create_demo_license()
    
    def save_license_data(self):
        """Uloží licenčné údaje do lokálneho súboru."""
        try:
            with open(self.license_file, 'w', encoding='utf-8') as f:
                json.dump(self.license_data, f, indent=2, ensure_ascii=False)
            logging.info("💾 Licenčné údaje uložené")
        except Exception as e:
            logging.error(f"❌ Chyba pri ukladaní licencie: {e}")
    
    def create_demo_license(self):
        """Vytvorí demo licenciu pre nového užívateľa."""
        hardware_id = get_hardware_fingerprint()
        today = datetime.now().strftime("%Y-%m-%d")
        
        self.license_data = {
            "license_type": "demo",
            "hardware_id": hardware_id,
            "created_date": today,
            "last_used_date": today,
            "demo_time_used_today": 0,
            "total_demo_time_used": 0,
            "license_key": None,
            "expires_at": None,
            "user_email": None,
            "purchase_id": None
        }
        self.save_license_data()
        logging.info("🆕 Demo licencia vytvorená")
    
    def is_licensed(self) -> bool:
        """Kontroluje, či má užívateľ platnú licenciu."""
        # 🔧 OPRAVA: Kontrola testovacieho režimu
        if hasattr(self, '_test_mode') and self._test_mode == 'full':
            logging.info("🧪 Testovací režim: simulujem plnú licenciu")
            return True

        if self.license_data.get("license_type") == "full":
            expires_at = self.license_data.get("expires_at")
            if expires_at:
                expiry_date = datetime.fromisoformat(expires_at)
                if datetime.now() < expiry_date:
                    return True
                else:
                    logging.warning("⚠️ Licencia expirovala")
                    self.downgrade_to_demo()
                    return False
        return False
    
    def get_license_status(self) -> Dict[str, Any]:
        """Vráti aktuálny stav licencie."""
        if self.is_licensed():
            # 🔧 OPRAVA: Testovací režim má fake dátumy
            if hasattr(self, '_test_mode') and self._test_mode == 'full':
                return {
                    "type": "full",
                    "days_remaining": 365,
                    "expires_at": "15.09.2025"
                }

            expires_at = datetime.fromisoformat(self.license_data["expires_at"])
            days_remaining = (expires_at - datetime.now()).days
            return {
                "type": "full",
                "days_remaining": days_remaining,
                "expires_at": expires_at.strftime("%d.%m.%Y")
            }
        else:
            today = datetime.now().strftime("%Y-%m-%d")
            if self.license_data.get("last_used_date") != today:
                # Nový deň - reset demo času
                self.license_data["last_used_date"] = today
                self.license_data["demo_time_used_today"] = 0
                self.demo_time_used_today = 0
                self.save_license_data()
            else:
                self.demo_time_used_today = self.license_data.get("demo_time_used_today", 0)
            
            remaining_seconds = max(0, self.demo_limit_daily - self.demo_time_used_today)
            remaining_minutes = remaining_seconds // 60
            
            return {
                "type": "demo",
                "remaining_minutes": remaining_minutes,
                "remaining_seconds": remaining_seconds,
                "daily_limit_minutes": self.demo_limit_daily // 60
            }
    
    def can_start_demo(self) -> bool:
        """Kontroluje, či môže užívateľ spustiť demo."""
        # 🔧 OPRAVA: Testovací režim má vždy povolené čítanie
        if hasattr(self, '_test_mode'):
            if self._test_mode == 'full':
                logging.info("🧪 Testovací režim: povolené čítanie (plná licencia)")
                return True
            elif self._test_mode == 'demo':
                logging.info("🧪 Testovací režim: povolené čítanie (demo)")
                return True

        if self.is_licensed():
            return True

        status = self.get_license_status()
        return status["remaining_seconds"] > 0
    
    def start_demo_session(self) -> bool:
        """Spustí demo session."""
        if not self.can_start_demo():
            return False

        # 🔧 OPRAVA: Testovací režim nepočíta čas
        if hasattr(self, '_test_mode'):
            if self._test_mode == 'full':
                logging.info("🧪 Testovací režim: session spustená (plná licencia)")
                return True
            elif self._test_mode == 'demo':
                logging.info("🧪 Testovací režim: session spustená (demo bez počítania času)")
                return True

        if self.is_licensed():
            return True

        self.is_demo_active = True
        self.demo_start_time = time.time()
        logging.info("⏱️ Demo session spustená")
        return True
    
    def stop_demo_session(self):
        """Ukončí demo session a uloží použitý čas."""
        if not self.is_demo_active or self.demo_start_time is None:
            return
        
        if self.is_licensed():
            return
        
        session_time = int(time.time() - self.demo_start_time)
        self.demo_time_used_today += session_time
        self.license_data["demo_time_used_today"] = self.demo_time_used_today
        self.license_data["total_demo_time_used"] = self.license_data.get("total_demo_time_used", 0) + session_time
        
        self.is_demo_active = False
        self.demo_start_time = None
        self.save_license_data()
        
        logging.info(f"⏹️ Demo session ukončená. Použitý čas: {session_time}s")
    
    def get_current_demo_time(self) -> int:
        """Vráti aktuálny demo čas v sekundách."""
        if not self.is_demo_active or self.demo_start_time is None:
            return 0
        return int(time.time() - self.demo_start_time)
    
    def activate_license(self, license_key: str, user_email: str) -> bool:
        """Aktivuje platenú licenciu."""
        try:
            hardware_id = get_hardware_fingerprint()
            
            # Overenie licencie na serveri
            response = requests.post(f"{self.server_url}/license.php", json={
                "action": "activate",
                "license_key": license_key,
                "hardware_id": hardware_id,
                "user_email": user_email
            }, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    # Aktivácia úspešná
                    self.license_data.update({
                        "license_type": "full",
                        "license_key": license_key,
                        "user_email": user_email,
                        "expires_at": result["expires_at"],
                        "purchase_id": result.get("purchase_id")
                    })
                    self.save_license_data()
                    logging.info("✅ Licencia úspešne aktivovaná")
                    return True
                else:
                    logging.error(f"❌ Aktivácia zlyhala: {result.get('error', 'Neznáma chyba')}")
                    return False
            else:
                logging.error(f"❌ Server error: {response.status_code}")
                return False
                
        except Exception as e:
            logging.error(f"❌ Chyba pri aktivácii licencie: {e}")
            return False
    
    def verify_license_online(self) -> bool:
        """Overí licenciu online na serveri."""
        if not self.is_licensed():
            return False
        
        try:
            response = requests.post(f"{self.server_url}/license.php", json={
                "action": "verify",
                "license_key": self.license_data["license_key"],
                "hardware_id": get_hardware_fingerprint()
            }, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                return result.get("success", False)
            else:
                logging.warning(f"⚠️ Licencia sa nedá overiť online: {response.status_code}")
                return True  # Offline grace period
                
        except Exception as e:
            logging.warning(f"⚠️ Chyba pri online overení: {e}")
            return True  # Offline grace period
    
    def downgrade_to_demo(self):
        """Degraduje licenciu na demo."""
        self.license_data["license_type"] = "demo"
        self.license_data["license_key"] = None
        self.license_data["expires_at"] = None
        self.save_license_data()
        logging.info("⬇️ Licencia degradovaná na demo")
    
    def start_license_monitor(self):
        """Spustí background monitoring licencie."""
        def monitor():
            while True:
                try:
                    current_time = time.time()
                    if current_time - self.last_license_check > self.license_check_interval:
                        if self.is_licensed():
                            self.verify_license_online()
                        self.last_license_check = current_time
                    time.sleep(300)  # Check každých 5 minút
                except Exception as e:
                    logging.error(f"❌ Chyba v license monitor: {e}")
                    time.sleep(60)
        
        monitor_thread = threading.Thread(target=monitor, daemon=True)
        monitor_thread.start()
        logging.info("🔍 License monitor spustený")
    
    def get_purchase_url(self) -> str:
        """Vráti URL pre nákup licencie."""
        hardware_id = get_hardware_fingerprint()
        return f"https://voxoloxo.com/purchase/register.php?hw={hardware_id}"


# Globálna inštancia
_license_manager: Optional[LicenseManager] = None

def get_license_manager() -> LicenseManager:
    """Vráti globálnu inštanciu license managera."""
    global _license_manager
    if _license_manager is None:
        _license_manager = LicenseManager()
    return _license_manager

def is_licensed() -> bool:
    """Skratka pre kontrolu licencie."""
    return get_license_manager().is_licensed()

def can_start_reading() -> bool:
    """Kontroluje, či môže užívateľ spustiť čítanie."""
    return get_license_manager().can_start_demo()

def start_reading_session() -> bool:
    """Spustí reading session (demo alebo full)."""
    return get_license_manager().start_demo_session()

def stop_reading_session():
    """Ukončí reading session."""
    get_license_manager().stop_demo_session()
