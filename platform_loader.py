#!/usr/bin/env python3
"""
Platform-specific module loader for Subtitle Reader application.
Automatically loads the correct platform-specific modules based on the current OS.

This module provides a centralized way to import platform-specific implementations
without having to check platform conditions in every file that needs them.
"""

import sys
import platform
import logging
import importlib
from typing import Any, Optional

# Platform detection
IS_MACOS = platform.system() == "Darwin"
IS_WINDOWS = platform.system() == "Windows"
IS_LINUX = platform.system() == "Linux"

PLATFORM_NAME = platform.system()

logging.info(f"🔍 Platform loader initialized for: {PLATFORM_NAME}")

class PlatformModuleLoader:
    """Centralized loader for platform-specific modules."""
    
    def __init__(self):
        self.platform = PLATFORM_NAME
        self.loaded_modules = {}
        
    def get_hotkey_manager(self):
        """Get the appropriate hotkey manager for current platform."""
        module_name = "hotkey_manager"
        
        if module_name in self.loaded_modules:
            return self.loaded_modules[module_name]
            
        try:
            if IS_MACOS:
                module = importlib.import_module("mac_hotkey_manager")
                logging.info("🔧 Loaded macOS hotkey manager")
            elif IS_WINDOWS:
                module = importlib.import_module("win_hotkey_manager")
                logging.info("🔧 Loaded Windows hotkey manager")
            elif IS_LINUX:
                # Linux uses Windows-style hotkeys
                module = importlib.import_module("win_hotkey_manager")
                logging.info("🔧 Loaded Windows hotkey manager for Linux")
            else:
                # Fallback to Windows version
                module = importlib.import_module("win_hotkey_manager")
                logging.warning(f"⚠️ Unknown platform {self.platform}, using Windows hotkey manager as fallback")
                
            self.loaded_modules[module_name] = module
            return module
            
        except ImportError as e:
            logging.error(f"❌ Failed to load hotkey manager for {self.platform}: {e}")
            # Try fallback to generic hotkey_manager if it exists
            try:
                module = importlib.import_module("hotkey_manager")
                logging.info("🔧 Loaded fallback hotkey manager")
                self.loaded_modules[module_name] = module
                return module
            except ImportError:
                logging.error("❌ No hotkey manager available!")
                return None
    
    def get_tts_manager(self):
        """Get the appropriate TTS manager for current platform."""
        module_name = "tts_manager"
        
        if module_name in self.loaded_modules:
            return self.loaded_modules[module_name]
            
        try:
            if IS_MACOS:
                module = importlib.import_module("mac_tts_manager")
                logging.info("🎤 Loaded macOS TTS manager")
            elif IS_WINDOWS:
                module = importlib.import_module("win_tts_manager")
                logging.info("🎤 Loaded Windows TTS manager")
            elif IS_LINUX:
                # Linux uses Windows-style TTS
                module = importlib.import_module("win_tts_manager")
                logging.info("🎤 Loaded Windows TTS manager for Linux")
            else:
                # Fallback to Windows version
                module = importlib.import_module("win_tts_manager")
                logging.warning(f"⚠️ Unknown platform {self.platform}, using Windows TTS manager as fallback")
                
            self.loaded_modules[module_name] = module
            return module
            
        except ImportError as e:
            logging.error(f"❌ Failed to load TTS manager for {self.platform}: {e}")
            # Try fallback to generic tts_manager if it exists
            try:
                module = importlib.import_module("tts_manager")
                logging.info("🎤 Loaded fallback TTS manager")
                self.loaded_modules[module_name] = module
                return module
            except ImportError:
                logging.error("❌ No TTS manager available!")
                return None

# Global instance
_platform_loader = PlatformModuleLoader()

# Convenience functions for easy importing
def get_hotkey_manager():
    """Get the platform-appropriate hotkey manager module."""
    return _platform_loader.get_hotkey_manager()

def get_tts_manager():
    """Get the platform-appropriate TTS manager module."""
    return _platform_loader.get_tts_manager()

def get_tts_function(func_name):
    """Get TTS function from platform-specific module."""
    tts_manager = get_tts_manager()
    if tts_manager and hasattr(tts_manager, func_name):
        return getattr(tts_manager, func_name)
    else:
        logging.error(f"❌ TTS function '{func_name}' not available")
        return lambda *args, **kwargs: None

def get_hotkey_function(func_name):
    """Get hotkey function from platform-specific module."""
    hotkey_manager = get_hotkey_manager()
    if hotkey_manager and hasattr(hotkey_manager, func_name):
        return getattr(hotkey_manager, func_name)
    else:
        logging.error(f"❌ Hotkey function '{func_name}' not available")
        return lambda *args, **kwargs: None

# Platform info functions
def get_platform_info():
    """Get current platform information."""
    return {
        "system": PLATFORM_NAME,
        "is_macos": IS_MACOS,
        "is_windows": IS_WINDOWS,
        "is_linux": IS_LINUX,
        "python_version": platform.python_version(),
        "machine": platform.machine()
    }

def is_macos():
    """Check if running on macOS."""
    return IS_MACOS

def is_windows():
    """Check if running on Windows."""
    return IS_WINDOWS

def is_linux():
    """Check if running on Linux."""
    return IS_LINUX

# Example usage:
if __name__ == "__main__":
    print("Platform Loader Test")
    print("=" * 50)
    
    info = get_platform_info()
    for key, value in info.items():
        print(f"{key}: {value}")
    
    print("\nTesting module loading:")
    
    hotkey_mgr = get_hotkey_manager()
    if hotkey_mgr:
        print(f"✅ Hotkey manager loaded: {hotkey_mgr.__name__}")
    else:
        print("❌ Failed to load hotkey manager")
    
    tts_mgr = get_tts_manager()
    if tts_mgr:
        print(f"✅ TTS manager loaded: {tts_mgr.__name__}")
    else:
        print("❌ Failed to load TTS manager")
