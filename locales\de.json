{"app_title": "Untertitel-Leser", "main_tab": "Hauptsteuerung", "debug_tab": "Debug-Parameter", "detection_tab": "Untertitel-Erkennung", "start_reading": "<PERSON><PERSON> starten", "stop_reading": "<PERSON><PERSON> stoppen", "hotkey_hint": "oder drücken Sie Cmd+Alt+Ctrl+V", "mode_group": "Les<PERSON><PERSON>", "mode_full_auto": "Vollautomatisch", "mode_static": "Statisch", "mode_dynamic": "Dynamisch", "ocr_group": "OCR", "ocr_threshold": "OCR-Schwellenwert:", "language_group": "<PERSON><PERSON><PERSON>", "app_language": "Anwendungssprache:", "reading_language": "Untertitel-Lesesprache:", "performance_mode": "Leistungsmodus:", "custom_settings": "Einstellungen", "clear_cache": "<PERSON><PERSON> le<PERSON>n", "optimize_memory": "Speicher optimieren", "speed_test": "⚡ Geschwindigkeitstest", "tts_group": "<PERSON><PERSON><PERSON> (TTS)", "speech_rate": "Sprechgeschwindigkeit:", "speech_volume": "Sprachlautstärke:", "allow_uppercase": "<PERSON><PERSON> von Großbuchstaben erlauben", "perf_speed": "Geschwindigkeit (beam=1)", "perf_balanced": "Ausgewogen (beam=4)", "perf_quality": "Qualität (beam=5)", "perf_custom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tooltip_custom_params": "Benutzerdefinierte Übersetzungsparameter konfigurieren", "tooltip_clear_cache": "Übersetzungs-<PERSON><PERSON> leeren um Speicher zu sparen", "tooltip_optimize_memory": "Speicheroptimierung erzwingen", "tooltip_speed_test": "Übersetzungsgeschwindigkeitstest ausführen", "tts_detecting_subtitles": "<PERSON><PERSON><PERSON>-Ty<PERSON>.", "tts_starting_continuous": "Starte kontinuierliche Untertitel-Erkennung.", "tts_starting_reading": "Be<PERSON>ne Ihnen Untertitel vorzulesen.", "tts_ending_reading": "<PERSON><PERSON> das Lesen.", "tts_detected_dynamic": "Dynamische Untertitel erkannt.", "tts_detected_static": "Statische Untertitel erkannt.", "tts_switched_dynamic": "Auf dynamische Verarbeitung umgeschaltet.", "tts_switched_static": "Auf statische Verarbeitung umgeschaltet.", "tts_switched_to_static": "Auf statische Untertitel umgeschaltet.", "demo_expired_message": "20 Minuten Demo-Zeitlimit für heute abgelaufen. Entweder kaufen Sie die Vollversion oder warten Si<PERSON> bis morgen.", "demo_time_remaining": "🔒 Demo-Version, Sie haben heute noch {time} übrig", "purchase_button": "Für 1 Jahr kaufen", "full_version_status": "🔓 Vollversion, gültig bis {expires_at} ({days_remaining} Tage)", "demo_expired_title": "Demo-Zeit abgelaufen", "demo_expired_dialog": "Ihre tägliche Demo-Zeit ist abgelaufen.\n\nUm die Anwendung weiter zu nutzen, kaufen Sie bitte die Vollversion für ein Jahr.\n\nDie Demo-Zeit wird morgen erneuert.", "demo_test_button": "Demo-Version", "full_test_button": "Vollversion", "tts_test_button": "🔊 Test TTS", "toolbar_actions": "Aktionen", "reset_completed_title": "Reset abgeschlossen", "reset_completed_message": "Alle Einstellungen wurden auf die Standardwerte entsprechend der Systemsprache zurückgesetzt.", "start_reading_section": "LESEN STARTEN", "translator_stats": "Statistiken", "translator_stats_legacy": "Statistiken: Legacy-System", "translator_stats_unavailable": "Statistiken: Multi-Übersetzer nicht verfügbar", "translator_stats_error": "Statistiken: Initialisierungsfehler", "reading_allowed": "ERLAUBT", "reading_forbidden": "VERBOTEN", "uppercase_reading_status": "<PERSON><PERSON> von Großbuchstaben ist", "keyboard_image_not_found": "Tastaturbild nicht gefunden (keyboard_MAC_200.jpg)", "keyboard_image_error": "Fe<PERSON> beim Laden des Bildes:", "performance_speed": "🚀 Geschwindigkeit (beam=1)", "performance_balanced": "⚖️ Ausgewogen (beam=4)", "performance_quality": "🎯 Qualität (beam=5)", "performance_custom": "⚙️ <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "quality_fast": "⚡ <PERSON><PERSON><PERSON>", "quality_balanced": "⚖️ Ausgewogen", "quality_quality": "🏆 Qualität", "quality_custom": "⚙️ <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "no_voices_available": "<PERSON><PERSON>", "statistics_not_available": "Statistiken: <PERSON>cht verfügbar", "statistics_legacy_system": "Statistiken: Legacy-System", "statistics_initialization_error": "Statistiken: Initialisierungsfehler", "connection_status_enter_credentials": "Status: ❌ API-Schlüssel und Region eingeben", "connection_status_testing": "Status: 🔄 Teste Verbindung...", "connection_status_invalid_credentials": "Status: ❌ Ungültige Anmeldedaten", "connection_status_success": "Status: ✅ Verbindung erfolgreich", "connection_status_failed": "Status: ❌ Verbindungstest fehlgeschlagen", "connection_status_error": "Status: ❌ <PERSON><PERSON>: ", "connection_status_configured": "Status: ✅ Anmeldedaten gesetzt", "connection_status_not_configured": "Status: <PERSON><PERSON> konfigu<PERSON>t", "filtering_enabled": "AKTIVIERT", "filtering_disabled": "DEAKTIVIERT", "filtering_active": "Aktiv (X={x}px)", "filtering_detecting": "Erkennung ({samples}/5 Proben)", "filtering_waiting": "<PERSON><PERSON> auf Erkennung", "filtering_all_text": "Nimmt allen Text ohne Filterung", "device_cpu": "CPU", "device_mps": "MPS (Apple Silicon)", "device_cuda": "CUDA (NVIDIA)", "model_status_not_loaded": "<PERSON>l nicht geladen", "model_status_loaded": "<PERSON>l geladen", "model_status_loading": "Lade Modell...", "memory_usage": "Speicher: {memory} MB", "voice_for_reading": "🎤 Stimme zum Lesen:", "voice_for_reading_tooltip": "<PERSON><PERSON><PERSON>en Si<PERSON> eine Stimme zum Lesen von Untertiteln und Systemmeldungen", "available_languages_info": "Verfügbar: {count} <PERSON><PERSON><PERSON> (OCR: {ocr}, TTS: {tts})", "available_source_info": "Verfügbar: {count} <PERSON><PERSON><PERSON> (OCR ∩ Übersetzer)", "available_target_info": "Verfügbar: {count} <PERSON><PERSON><PERSON> (TTS ∩ Übersetzer)", "speed_test_tooltip": "Übersetzungsgeschwindigkeitstest ausführen", "custom_settings_button": "⚙️ Einstellungen", "custom_settings_tooltip": "Benutzerdefinierte Übersetzungsparameter konfigurieren", "similarity_thresholds": "⚙️ Grundlegende Ähnlichkeitsschwellen", "tts_duplicate_threshold": "TTS-Duplikatsschwelle", "stability_similarity": "Zeilenstabilität (Ähnlichkeit)", "two_frame_stability": "Zwei-Frame-Stabilität", "static_similarity": "Statische Ähnlichkeit", "static_stability_threshold": "Statische Stabilitätsschwelle", "static_replacement_threshold": "Statische Ersetzungsschwelle", "cycling_stability": "🔄 Zyklische Stabilität", "min_cycles_first_line": "Min Z<PERSON>len 1. Zeile", "min_cycles_second_line": "<PERSON> 2. <PERSON><PERSON><PERSON>", "min_cycles_second_growth": "<PERSON> 2. <PERSON><PERSON><PERSON> Wachstum", "single_text_cycles": "Einzeltext-Stabilitätszyklen", "different_texts_count": "<PERSON><PERSON>hl verschiedener Texte zum Wechseln", "dynamic_growth": "📈 Dynamisches Wachstum", "dynamic_growth_similarity": "Dynamische Wachstumsähnlichkeit", "dynamic_min_word_length": "Min Länge hinzugefügter Wörter", "dynamic_max_words": "<PERSON> Wörter auf einmal hinzugefügt", "full_auto_thresholds": "🤖 Vollautomatische Schwellen", "fa_initial_static": "FA: anfängliche statische Schwelle", "fa_initial_dynamic": "FA: anfängliche dynamische Schwelle", "fa_stable_static": "FA: stabile statische Schwelle", "fa_stable_dynamic": "FA: stabile dynamis<PERSON>hwelle", "fa_min_samples": "FA: min stabile Proben", "fa_replace_threshold": "FA: Ersetzungsschwelle", "ocr_analysis": "🔍 OCR und Analyse", "ocr_threshold_param": "OCR-Schwelle", "dynamic_ocr_threshold": "Dynamische OCR-Schwelle", "min_text_length": "Min Textlänge für Analyse", "tests_tab": "Tests", "tolerance_group": "Untertitel-Positionstoleranz", "ocr_interval_group": "OCR-Intervall", "filtering_group": "Filterung nach linker Seite", "actions_group": "Aktionen", "y_threshold_label": "Y-Untertitel-Schwelle (%):", "reference_logging": "Referenz-Protokollierung für Erkennungsanalyse", "current_logging_to": "Aktuelle Protokollierung nach:", "standard_logging": "app.log (Standard-Protokollierung)", "static_logging": "static.log (für statische Untertitel)", "dynamic_logging": "dynamic.log (für dynamische Untertitel)", "clear_test_logs": "Alle Test-Logs löschen", "open_logs_folder": "Log-Ordner öffnen", "source_language_label": "Quellsprache:", "target_language_label": "Zielsprache:", "mode_off": "Aus", "mode_manual": "<PERSON><PERSON>", "mode_auto": "Automatisch", "mode_smart": "Intelligent", "quality_profile_label": "Qualitätsprofil:", "profile_fast": "⚡ <PERSON><PERSON><PERSON>", "profile_balanced": "⚖️ Ausgewogen", "profile_quality": "🏆 Qualität", "profile_custom": "⚙️ <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "active_status": " (aktiv)", "not_exists": "existiert nicht", "error_loading_stats": "<PERSON><PERSON> beim Laden der Statistiken: {error}", "reading_uppercase_enabled": "<PERSON><PERSON> von Großbuchstaben ist AKTIVIERT", "reading_uppercase_disabled": "<PERSON><PERSON> von Großbuchstaben ist DEAKTIVIERT", "speech_rate_set": "Sprechgeschwindigkeit auf {rate} gesetzt", "speech_volume_set": "Sprachlautstärke auf {volume} gesetzt", "ocr_threshold_set": "OCR-Schwellenwert auf {threshold} gesetzt", "thread_safe_status_update": "Thread‑sichere Statusaktualisierung.", "start_stop_action": "Start/Stopp", "cancel_button": "Abbrechen", "test_text_placeholder": "Text für Test eingeben:", "error_no_input": "FEHLER: Text zum Übersetzen e<PERSON>ben!", "swap_languages_button": "🔄 Sprachen tauschen", "confirmation_dialog": "Bestätigung", "success_dialog": "Erfolg", "error_dialog": "<PERSON><PERSON>", "warning_dialog": "<PERSON><PERSON><PERSON>", "critical_error_dialog": "Kritische<PERSON> Fehler", "status_initialized": "✅ Initialisiert", "status_not_initialized": "❌ Nicht initialisiert", "device_mps_gpu": "🚀 MPS GPU (Apple Silicon)", "device_cuda_gpu": "🚀 CUDA GPU", "device_cpu_only": "💻 CPU", "memory_usage_model": "~2-3 GB (600M Modell)", "memory_usage_none": "0 GB", "test_connection_button": "🧪 Verbindung testen", "test_connection_tooltip": "Testet API-Verbindung mit aktuellen Anmeldedaten", "filtering_status_all_text": "Nimmt allen Text ohne Filterung", "filtering_status_waiting": "Wartet auf Erkennung", "filtering_status_active": "Aktiv (X={x}px)", "filtering_status_detecting": "Erkennt ({samples}/5 Proben)", "reset_left_detection": "Reset der linken Seitenerkennung", "report_anomaly": "<PERSON><PERSON><PERSON> melden", "dialog_ok": "✅ OK", "dialog_cancel": "❌ Abbrechen", "dialog_reset": "🔄 Auf Standard zurücksetzen", "generation_settings": "🔧 Generierung", "penalty_settings": "⚖️ Strafen", "optimization_settings": "🚀 Optimierungen", "info_section": "ℹ️ Informationen", "max_length_param": "<PERSON>:", "min_length_param": "<PERSON>:", "num_beams_param": "<PERSON><PERSON><PERSON>:", "use_cache_param": "<PERSON><PERSON> verwenden", "enable_fast_mode": "Schnellmodus aktivieren:", "memory_optimization": "Speicheroptimierung:", "beam_search_settings": "🔹 Beam Search", "penalties_settings": "🔹 Strafen und Regulierungen", "length_limits_settings": "🔹 Längenbegrenzungen", "apple_optimizations": "🔹 Apple Silicon M1 Optimierungen", "cache_settings": "🔹 Cache-Einstellungen", "api_settings": "🔹 API-Einstellungen", "enable_cache": "<PERSON><PERSON> aktiv<PERSON>", "max_cache_size": "<PERSON>r<PERSON>:", "timeout_param": "Timeout:", "rate_limit_param": "Rate Limit:", "batch_size_param": "Batch-Größe:", "seconds_suffix": " Sekunden", "tokens_suffix": " Token", "status_label": "Status:", "active_log_suffix": " (aktiv)", "file_not_exists": "existiert nicht", "stats_error_prefix": "Fehler beim Laden der Statistiken: ", "log_files_cleared": "Test-Log-<PERSON><PERSON>: ", "no_log_files_to_clear": "<PERSON>ine Test-Log-<PERSON><PERSON> zum Löschen", "logs_folder_opened": "Logs-Ordner geöffnet: ", "anomaly_report_started": "Anomalie-Bericht über GUI-Schaltfläche gestartet (Qt)", "detection_reset": "Erkennung der linken Seite dynamischer Untertitel wurde zurückgesetzt", "filtering_status_on": "AKTIVIERT", "filtering_status_off": "DEAKTIVIERT", "all_modes_enabled": "alle Modi aktiviert, Einstellungen wiederhergestellt", "switched_to_static": "auf statischen Modus umgeschaltet", "connection_status_missing_credentials": "Status: ❌ API-Schlüssel und Region eingeben", "max_retries_param": "<PERSON>:", "beam_search_group": "🔹 Beam Search", "penalties_group": "🔹 Strafen und Regulierungen", "no_repeat_ngram_label": "No repeat ngram size:", "no_repeat_ngram_tooltip": "<PERSON><PERSON><PERSON> Wiederholung von N-Grammen (z.B. 'the the the')\n0 = de<PERSON><PERSON><PERSON><PERSON>, 2-4 = typische Werte", "repetition_penalty_label": "Wiederholungsstrafe:", "repetition_penalty_tooltip": "Bestraft wiederholte Wörter\n>1.0 = weniger Wiederholungen, 1.1-1.3 = empfohlen", "length_penalty_label": "Längenstrafe:", "length_penalty_tooltip": "Beeinflusst Präferenz für längere/kürzere Sätze\n>1.0 = längere Übersetzungen, <1.0 = kürzere Übersetzungen", "length_limits_group": "🔹 Längenbegrenzungen", "max_length_label": "<PERSON>:", "max_length_not_supported_label": "<PERSON> (nicht unterstützt):", "optimizations_group": "🔹 Optimierungen für Apple Silicon M1", "fast_mode_label": "Schnellmodus aktivieren:", "fast_mode_tooltip": "Aktiviert schnelle Optimierungen", "memory_opt_label": "Speicheroptimierung:", "memory_opt_tooltip": "Aktiviert Speicheroptimierungen", "info_group": "ℹ️ Informationen", "reset_to_defaults": "🔄 Auf Standard zurücksetzen", "ocr_interval_label": "OCR-Intervall (s):"}