# Cross-platform dependencies
Levenshtein==0.27.1
mss==10.0.0
packaging==25.0
pillow==11.3.0
pynput==1.7.6
pytesseract==0.3.13
pyttsx3==2.98
RapidFuzz==3.13.0
six==1.17.0
pywebview
PyQt6

# macOS-specific dependencies (install only on macOS)
pyobjc==11.1; sys_platform == "darwin"
pyobjc-core==11.1; sys_platform == "darwin"
pyobjc-framework-Accessibility==11.1; sys_platform == "darwin"
pyobjc-framework-Accounts==11.1; sys_platform == "darwin"
pyobjc-framework-AddressBook==11.1; sys_platform == "darwin"
pyobjc-framework-AdServices==11.1; sys_platform == "darwin"
pyobjc-framework-AdSupport==11.1; sys_platform == "darwin"
pyobjc-framework-AppleScriptKit==11.1; sys_platform == "darwin"
pyobjc-framework-AppleScriptObjC==11.1; sys_platform == "darwin"
pyobjc-framework-ApplicationServices==11.1; sys_platform == "darwin"
pyobjc-framework-AppTrackingTransparency==11.1; sys_platform == "darwin"
pyobjc-framework-AudioVideoBridging==11.1; sys_platform == "darwin"
pyobjc-framework-AuthenticationServices==11.1; sys_platform == "darwin"
pyobjc-framework-AutomaticAssessmentConfiguration==11.1; sys_platform == "darwin"
pyobjc-framework-Automator==11.1; sys_platform == "darwin"
pyobjc-framework-AVFoundation==11.1; sys_platform == "darwin"
pyobjc-framework-AVKit==11.1; sys_platform == "darwin"
pyobjc-framework-AVRouting==11.1; sys_platform == "darwin"
pyobjc-framework-BackgroundAssets==11.1; sys_platform == "darwin"
pyobjc-framework-BrowserEngineKit==11.1; sys_platform == "darwin"
pyobjc-framework-BusinessChat==11.1; sys_platform == "darwin"
pyobjc-framework-CalendarStore==11.1; sys_platform == "darwin"
pyobjc-framework-CallKit==11.1; sys_platform == "darwin"
pyobjc-framework-Carbon==11.1; sys_platform == "darwin"
pyobjc-framework-CFNetwork==11.1; sys_platform == "darwin"
pyobjc-framework-Cinematic==11.1; sys_platform == "darwin"
pyobjc-framework-ClassKit==11.1; sys_platform == "darwin"
pyobjc-framework-CloudKit==11.1; sys_platform == "darwin"
pyobjc-framework-Cocoa==11.1; sys_platform == "darwin"
pyobjc-framework-Collaboration==11.1; sys_platform == "darwin"
pyobjc-framework-ColorSync==11.1; sys_platform == "darwin"
pyobjc-framework-Contacts==11.1; sys_platform == "darwin"
pyobjc-framework-ContactsUI==11.1; sys_platform == "darwin"
pyobjc-framework-CoreAudio==11.1; sys_platform == "darwin"
pyobjc-framework-CoreAudioKit==11.1; sys_platform == "darwin"
pyobjc-framework-CoreBluetooth==11.1; sys_platform == "darwin"
pyobjc-framework-CoreData==11.1; sys_platform == "darwin"
pyobjc-framework-CoreHaptics==11.1; sys_platform == "darwin"
pyobjc-framework-CoreLocation==11.1; sys_platform == "darwin"
pyobjc-framework-CoreMedia==11.1; sys_platform == "darwin"
pyobjc-framework-CoreMediaIO==11.1; sys_platform == "darwin"
pyobjc-framework-CoreMIDI==11.1; sys_platform == "darwin"
pyobjc-framework-CoreML==11.1; sys_platform == "darwin"
pyobjc-framework-CoreMotion==11.1; sys_platform == "darwin"
pyobjc-framework-CoreServices==11.1; sys_platform == "darwin"
pyobjc-framework-CoreSpotlight==11.1; sys_platform == "darwin"
pyobjc-framework-CoreText==11.1; sys_platform == "darwin"
pyobjc-framework-CoreWLAN==11.1; sys_platform == "darwin"
pyobjc-framework-CryptoTokenKit==11.1; sys_platform == "darwin"
pyobjc-framework-DataDetection==11.1; sys_platform == "darwin"
pyobjc-framework-DeviceCheck==11.1; sys_platform == "darwin"
pyobjc-framework-DeviceDiscoveryExtension==11.1; sys_platform == "darwin"
pyobjc-framework-DictionaryServices==11.1; sys_platform == "darwin"
pyobjc-framework-DiscRecording==11.1; sys_platform == "darwin"
pyobjc-framework-DiscRecordingUI==11.1; sys_platform == "darwin"
pyobjc-framework-DiskArbitration==11.1; sys_platform == "darwin"
pyobjc-framework-DVDPlayback==11.1; sys_platform == "darwin"
pyobjc-framework-EventKit==11.1; sys_platform == "darwin"
pyobjc-framework-ExceptionHandling==11.1; sys_platform == "darwin"
pyobjc-framework-ExecutionPolicy==11.1; sys_platform == "darwin"
pyobjc-framework-ExtensionKit==11.1; sys_platform == "darwin"
pyobjc-framework-ExternalAccessory==11.1; sys_platform == "darwin"
pyobjc-framework-FileProvider==11.1; sys_platform == "darwin"
pyobjc-framework-FileProviderUI==11.1; sys_platform == "darwin"
pyobjc-framework-FinderSync==11.1; sys_platform == "darwin"
pyobjc-framework-FSEvents==11.1; sys_platform == "darwin"
pyobjc-framework-FSKit==11.1; sys_platform == "darwin"
pyobjc-framework-GameCenter==11.1; sys_platform == "darwin"
pyobjc-framework-GameController==11.1; sys_platform == "darwin"
pyobjc-framework-GameKit==11.1; sys_platform == "darwin"
pyobjc-framework-GameplayKit==11.1; sys_platform == "darwin"
pyobjc-framework-HealthKit==11.1; sys_platform == "darwin"
pyobjc-framework-ImageCaptureCore==11.1; sys_platform == "darwin"
pyobjc-framework-InputMethodKit==11.1; sys_platform == "darwin"
pyobjc-framework-InstallerPlugins==11.1; sys_platform == "darwin"
pyobjc-framework-InstantMessage==11.1; sys_platform == "darwin"
pyobjc-framework-Intents==11.1; sys_platform == "darwin"
pyobjc-framework-IntentsUI==11.1; sys_platform == "darwin"
pyobjc-framework-IOBluetooth==11.1; sys_platform == "darwin"
pyobjc-framework-IOBluetoothUI==11.1; sys_platform == "darwin"
pyobjc-framework-IOSurface==11.1; sys_platform == "darwin"
pyobjc-framework-iTunesLibrary==11.1; sys_platform == "darwin"
pyobjc-framework-KernelManagement==11.1; sys_platform == "darwin"
pyobjc-framework-LatentSemanticMapping==11.1; sys_platform == "darwin"
pyobjc-framework-LaunchServices==11.1; sys_platform == "darwin"
pyobjc-framework-libdispatch==11.1; sys_platform == "darwin"
pyobjc-framework-libxpc==11.1; sys_platform == "darwin"
pyobjc-framework-LinkPresentation==11.1; sys_platform == "darwin"
pyobjc-framework-LocalAuthentication==11.1; sys_platform == "darwin"
pyobjc-framework-LocalAuthenticationEmbeddedUI==11.1; sys_platform == "darwin"
pyobjc-framework-MailKit==11.1; sys_platform == "darwin"
pyobjc-framework-MapKit==11.1; sys_platform == "darwin"
pyobjc-framework-MediaAccessibility==11.1; sys_platform == "darwin"
pyobjc-framework-MediaExtension==11.1; sys_platform == "darwin"
pyobjc-framework-MediaLibrary==11.1; sys_platform == "darwin"
pyobjc-framework-MediaPlayer==11.1; sys_platform == "darwin"
pyobjc-framework-MediaToolbox==11.1; sys_platform == "darwin"
pyobjc-framework-Metal==11.1; sys_platform == "darwin"
pyobjc-framework-MetalFX==11.1; sys_platform == "darwin"
pyobjc-framework-MetalKit==11.1; sys_platform == "darwin"
pyobjc-framework-MetalPerformanceShaders==11.1; sys_platform == "darwin"
pyobjc-framework-MetalPerformanceShadersGraph==11.1; sys_platform == "darwin"
pyobjc-framework-MetricKit==11.1; sys_platform == "darwin"
pyobjc-framework-MLCompute==11.1; sys_platform == "darwin"
pyobjc-framework-ModelIO==11.1; sys_platform == "darwin"
pyobjc-framework-MultipeerConnectivity==11.1; sys_platform == "darwin"
pyobjc-framework-NaturalLanguage==11.1; sys_platform == "darwin"
pyobjc-framework-NetFS==11.1; sys_platform == "darwin"
pyobjc-framework-Network==11.1; sys_platform == "darwin"
pyobjc-framework-NetworkExtension==11.1; sys_platform == "darwin"
pyobjc-framework-NotificationCenter==11.1; sys_platform == "darwin"
pyobjc-framework-OpenDirectory==11.1; sys_platform == "darwin"
pyobjc-framework-OSAKit==11.1; sys_platform == "darwin"
pyobjc-framework-OSLog==11.1; sys_platform == "darwin"
pyobjc-framework-PassKit==11.1; sys_platform == "darwin"
pyobjc-framework-PencilKit==11.1; sys_platform == "darwin"
pyobjc-framework-PHASE==11.1; sys_platform == "darwin"
pyobjc-framework-Photos==11.1; sys_platform == "darwin"
pyobjc-framework-PhotosUI==11.1; sys_platform == "darwin"
pyobjc-framework-PreferencePanes==11.1; sys_platform == "darwin"
pyobjc-framework-PushKit==11.1; sys_platform == "darwin"
pyobjc-framework-Quartz==11.1; sys_platform == "darwin"
pyobjc-framework-QuickLookThumbnailing==11.1; sys_platform == "darwin"
pyobjc-framework-ReplayKit==11.1; sys_platform == "darwin"
pyobjc-framework-SafariServices==11.1; sys_platform == "darwin"
pyobjc-framework-SafetyKit==11.1; sys_platform == "darwin"
pyobjc-framework-SceneKit==11.1; sys_platform == "darwin"
pyobjc-framework-ScreenCaptureKit==11.1; sys_platform == "darwin"
pyobjc-framework-ScreenSaver==11.1; sys_platform == "darwin"
pyobjc-framework-ScreenTime==11.1; sys_platform == "darwin"
pyobjc-framework-ScriptingBridge==11.1; sys_platform == "darwin"
pyobjc-framework-SearchKit==11.1; sys_platform == "darwin"
pyobjc-framework-Security==11.1; sys_platform == "darwin"
pyobjc-framework-SecurityFoundation==11.1; sys_platform == "darwin"
pyobjc-framework-SecurityInterface==11.1; sys_platform == "darwin"
pyobjc-framework-SecurityUI==11.1; sys_platform == "darwin"
pyobjc-framework-SensitiveContentAnalysis==11.1; sys_platform == "darwin"
pyobjc-framework-ServiceManagement==11.1; sys_platform == "darwin"
pyobjc-framework-SharedWithYou==11.1; sys_platform == "darwin"
pyobjc-framework-SharedWithYouCore==11.1; sys_platform == "darwin"
pyobjc-framework-ShazamKit==11.1; sys_platform == "darwin"
pyobjc-framework-Social==11.1; sys_platform == "darwin"
pyobjc-framework-SoundAnalysis==11.1; sys_platform == "darwin"
pyobjc-framework-Speech==11.1; sys_platform == "darwin"
pyobjc-framework-SpriteKit==11.1; sys_platform == "darwin"
pyobjc-framework-StoreKit==11.1; sys_platform == "darwin"
pyobjc-framework-Symbols==11.1; sys_platform == "darwin"
pyobjc-framework-SyncServices==11.1; sys_platform == "darwin"
pyobjc-framework-SystemConfiguration==11.1; sys_platform == "darwin"
pyobjc-framework-SystemExtensions==11.1; sys_platform == "darwin"
pyobjc-framework-ThreadNetwork==11.1; sys_platform == "darwin"
pyobjc-framework-UniformTypeIdentifiers==11.1; sys_platform == "darwin"
pyobjc-framework-UserNotifications==11.1; sys_platform == "darwin"
pyobjc-framework-UserNotificationsUI==11.1; sys_platform == "darwin"
pyobjc-framework-VideoSubscriberAccount==11.1; sys_platform == "darwin"
pyobjc-framework-VideoToolbox==11.1; sys_platform == "darwin"
pyobjc-framework-Virtualization==11.1; sys_platform == "darwin"
pyobjc-framework-Vision==11.1; sys_platform == "darwin"
pyobjc-framework-WebKit==11.1; sys_platform == "darwin"

# Windows-specific dependencies (install only on Windows)
# Add Windows-specific packages here if needed

# Linux-specific dependencies (install only on Linux)
# Add Linux-specific packages here if needed
