#!/usr/bin/env python3
"""
Test Apple TTS detekcie pomocou NSSpeechSynthesizer
"""

import sys
import logging

logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

def test_apple_tts_detection():
    """Testuje detekciu Apple TTS hlasov."""
    try:
        print("🔍 Test 1: Import AppKit...")
        from AppKit import NSSpeechSynthesizer
        print("✅ AppKit importovaný úspešne")
        
        print("\n🔍 Test 2: <PERSON>ís<PERSON>ie dostupných hlasov...")
        voices = NSSpeechSynthesizer.availableVoices()
        print(f"✅ Nájdených {len(voices)} hlasov")
        
        print("\n🔍 Test 3: Analýza hlasov...")
        detected_locales = set()
        voice_info = {}
        
        for i, voice_id in enumerate(voices):
            if i >= 10:  # Obmedzíme na prvých 10 pre test
                print(f"... a ďalších {len(voices) - 10} hlasov")
                break
                
            attrs = NSSpeechSynthesizer.attributesForVoice_(voice_id)
            voice_name = attrs.get('VoiceName', 'Unknown')
            locale_id = attrs.get('VoiceLocaleIdentifier', '')
            
            if locale_id:
                # Konvertuje sk_SK na sk-SK
                locale_normalized = locale_id.replace('_', '-')
                detected_locales.add(locale_normalized)
                
                # Extrahuje ISO kód (sk z sk-SK)
                iso_code = locale_normalized.split('-')[0]
                
                if iso_code not in voice_info:
                    voice_info[iso_code] = []
                
                voice_info[iso_code].append({
                    'id': str(voice_id),
                    'name': voice_name,
                    'locale': locale_normalized,
                    'is_enhanced': 'Enhanced' in voice_name or 'Premium' in voice_name
                })
                
                print(f"🎤 {voice_name} ({locale_normalized}) - {'Enhanced' if 'Enhanced' in voice_name else 'Standard'}")
        
        print(f"\n📊 Súhrn:")
        print(f"🌐 Detekované locales: {sorted(detected_locales)}")
        print(f"🗣️ Jazyky s hlasmi: {sorted(voice_info.keys())}")
        
        print(f"\n🔍 Test 4: Najlepšie hlasy pre každý jazyk...")
        for lang_code, voices_list in voice_info.items():
            # Preferuje Enhanced hlasy
            enhanced_voices = [v for v in voices_list if v['is_enhanced']]
            if enhanced_voices:
                best_voice = enhanced_voices[0]
                print(f"🎯 {lang_code}: {best_voice['name']} (Enhanced)")
            else:
                best_voice = voices_list[0]
                print(f"🎯 {lang_code}: {best_voice['name']} (Standard)")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Nainštalujte pyobjc: pip install pyobjc-framework-AppKit")
        return False
    except Exception as e:
        print(f"❌ Chyba: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🍎 Test Apple TTS detekcie")
    print("=" * 50)
    
    success = test_apple_tts_detection()
    
    if success:
        print("\n🎉 Test úspešný!")
    else:
        print("\n💥 Test neúspešný!")
        sys.exit(1)
