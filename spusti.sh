#!/bin/bash

# 🚀 Jednoduchý spúš<PERSON> pre Subtitle Reader
# Spustí aplikáciu s pyenv a zatvorí terminál

# Získaj adresár kde sa nachádza tento script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# Nastav pyenv ak existuje
export PATH="$HOME/.pyenv/bin:$PATH"
if command -v pyenv 1>/dev/null 2>&1; then
    eval "$(pyenv init -)"
    eval "$(pyenv virtualenv-init -)"
    
    # Aktivuj reader-env ak existuje
    if pyenv versions | grep -q "reader-env"; then
        pyenv shell reader-env
    fi
fi

# Spusti aplikáciu na pozadí
nohup python main_qt.py > /dev/null 2>&1 &

echo "✅ Aplikácia spustená na pozadí!"
