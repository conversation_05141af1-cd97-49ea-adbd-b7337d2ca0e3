# 📚 Dokumentácia pre vývoj<PERSON>rov - Medziplatformový systém

## 🎯 Prehľad projektu

Tento projekt predstavuje zlúčenie dvoch platformovo-špecifických aplikácií (macOS a Windows) do jedného univerzálneho systému s pokročilým **Platform Loader** mechanizmom. Aplikácia poskytuje OCR (optické rozpoznávanie textu) a TTS (text-to-speech) funkcionalitu s podporou viacerých jazykov.

## 🏗️ Architektúra systému

### Štruktúra adresárov
```
WM/
├── platform_loader.py          # ⭐ Centrálny platform loader
├── mac_hotkey_manager.py        # 🍎 macOS hotkey funkcionalita
├── win_hotkey_manager.py        # 🪟 Windows hotkey funkcionalita  
├── mac_tts_manager.py           # 🍎 macOS TTS funkcionalita
├── win_tts_manager.py           # 🪟 Windows TTS funkcionalita
├── app_logic.py                 # 🧠 Hlavná logika s wrapper funkciami
├── main_qt.py                   # 🚀 Hlavný vstupný bod
├── qt_gui.py                    # 🖥️ PyQt6 GUI rozhranie
├── requirements.txt             # 📦 Univerzálne závislosti
├── run_app.sh                   # 🍎 macOS/Linux spúšťač
├── run_app.bat                  # 🪟 Windows spúšťač
├── setup.sh                     # ⚙️ Inštalačný skript
└── app_settings.json            # ⚙️ Konfiguračný súbor
```

## 🔧 Platform Loader System

### Princíp fungovania

Platform Loader je centrálny systém, ktorý:
1. **Detekuje aktuálnu platformu** pomocou `platform.system()`
2. **Dynamicky načítava** správne moduly podľa platformy
3. **Poskytuje jednotné API** pre všetky platformy
4. **Rieši cirkulárne importy** pomocou wrapper funkcií

### 🏗️ Podporované platformy

#### macOS (Darwin)
- **Detekcia**: `platform.system() == 'Darwin'`
- **Moduly**: `mac_hotkey_manager.py`, `mac_tts_manager.py`
- **Závislosti**: pyobjc frameworks, pynput
- **TTS**: macOS `say` command

#### Windows x86/x64
- **Detekcia**: `platform.system() == 'Windows'` + `platform.machine() in ['AMD64', 'x86_64']`
- **Moduly**: `win_hotkey_manager.py`, `win_tts_manager.py`
- **Závislosti**: pywin32, pyttsx3, keyboard, comtypes
- **TTS**: Windows SAPI + OneCore TTS
- **Špecifické**: WinRT API pre OneCore TTS

#### Windows ARM64
- **Detekcia**: `platform.system() == 'Windows'` + `platform.machine() == 'ARM64'`
- **Moduly**: Rovnaké ako x86 (`win_hotkey_manager.py`, `win_tts_manager.py`)
- **Závislosti**: Rovnaké ako x86, ale bez `comtypes` (kompatibilita)
- **TTS**: Windows SAPI + OneCore TTS (ARM64 natívne)
- **Špecifické**: ARM64-optimalizované WinRT balíčky

### Kľúčové komponenty

#### 1. `platform_loader.py`
```python
class PlatformModuleLoader:
    def get_current_platform(self):
        system = platform.system().lower()
        if system == 'darwin': return 'macos'
        elif system == 'windows': return 'windows'
        else: return 'linux'
    
    def get_hotkey_manager(self):
        # Dynamicky načíta správny hotkey manager
    
    def get_tts_manager(self):
        # Dynamicky načíta správny TTS manager
```

#### 2. Wrapper funkcie
```python
def get_tts_function(func_name):
    """Získa TTS funkciu z platformovo-špecifického modulu."""
    tts_manager = get_tts_manager()
    return getattr(tts_manager, func_name)

def get_hotkey_function(func_name):
    """Získa hotkey funkciu z platformovo-špecifického modulu."""
    hotkey_manager = get_hotkey_manager()
    return getattr(hotkey_manager, func_name)
```

## 🔄 Migrácia z pôvodného systému

### Pred migráciou
```python
# Priamy import
import tts_manager
import hotkey_manager

# Priame volanie
tts_manager.speak_text("Hello")
hotkey_manager.register_hotkey()
```

### Po migrácii
```python
# Platform loader import
from platform_loader import get_tts_function, get_hotkey_function

# Wrapper funkcie
def speak_text(*args, **kwargs):
    return get_tts_function('speak_text')(*args, **kwargs)

def register_hotkey(*args, **kwargs):
    return get_hotkey_function('register_hotkey')(*args, **kwargs)
```

## 📦 Správa závislostí

### Univerzálne `requirements.txt`
```txt
# Základné závislosti
PyQt6>=6.4.0
Pillow>=9.0.0
pytesseract>=0.3.10

# Platform-specific závislosti
pynput>=1.7.6; sys_platform != "win32"
keyboard>=0.13.5; sys_platform == "win32"
pywin32>=305; sys_platform == "win32"
```

### ⚠️ **DÔLEŽITÉ: Windows ARM64 vs x86 rozdiely**

#### Windows x86/x64 (`requirements_x86.txt`)
```txt
# Windows Runtime (OneCore TTS) - x86 kompatibilné
winrt-Windows.Foundation>=2.0.0
winrt-Windows.Media.SpeechSynthesis>=2.0.0
winrt-Windows.Storage.Streams>=2.0.0
comtypes>=1.1.14  # x86 kompatibilné
pyperclip>=1.8.0  # Clipboard operácie
```

#### Windows ARM64 (používa základné `requirements.txt`)
```txt
# ARM64 NEPOUŽÍVA:
# - comtypes (kompatibilita problémy)
# - winrt-Windows.* (ARM64 má natívne WinRT)
# - pyperclip (nahradené ctypes riešením)

# ARM64 POUŽÍVA:
# - Natívne Windows WinRT API
# - ARM64-optimalizované PyQt6
# - Fallback riešenia pre kompatibilitu
```

### Inštalácia
```bash
# macOS/Linux
./setup.sh

# Windows x86/x64
pip install -r requirements_x86.txt

# Windows ARM64
pip install -r requirements.txt
```

## 🎮 Platformovo-špecifické moduly

### macOS moduly (`mac_*.py`)

#### `mac_hotkey_manager.py`
- Používa **pynput** pre globálne hotkeys
- Podporuje macOS špecifické klávesy (cmd, option)
- Implementuje thread-safe spracovanie

#### `mac_tts_manager.py`
- Používa **macOS `say` command**
- Podporuje Enhanced hlasy
- Cross-platform TTS provider

### Windows moduly (`win_*.py`)

#### `win_hotkey_manager.py` - **Univerzálne pre x86 aj ARM64**
- Používa **pynput** library (cross-platform)
- Fallback na **keyboard** library ak pynput zlyhá
- Win32 API integrácia cez ctypes (univerzálne)
- Podporuje Windows špecifické klávesy
- **ARM64 kompatibilita**: Plne funkčné

#### `win_tts_manager.py` - **Univerzálne pre x86 aj ARM64**
- **Primárne**: Windows OneCore TTS (WinRT API)
- **Fallback**: Windows SAPI cez pyttsx3
- **ARM64 výhody**: Natívne WinRT API, lepší výkon
- **x86 výhody**: Širšia kompatibilita s legacy SAPI hlasmi

### 🔍 **Kľúčové zistenie: Nie je potrebné rozdeľovať!**

**Windows moduly sú už navrhnuté ako univerzálne:**
- `win_hotkey_manager.py` funguje na x86 aj ARM64
- `win_tts_manager.py` funguje na x86 aj ARM64
- Platform loader automaticky detekuje Windows bez ohľadu na architektúru
- Rozdiely sú len v **závislostí**, nie v **kóde**

## 🧪 Testovací plán pre Windows vývojárov

### 1. Základné testy spustenia

#### Test 1: Inštalácia závislostí
```bash
cd WM
pip install -r requirements.txt
```
**Očakávaný výsledok:** Všetky závislosti sa nainštalujú bez chýb

#### Test 2: Spustenie aplikácie
```bash
python main_qt.py
```
**Očakávaný výsledok:** 
- GUI sa spustí bez chýb
- Zobrazí sa hlavné okno aplikácie
- V logoch sa zobrazí "🔧 Loaded Windows hotkey manager"

### 2. Platform Loader testy

#### Test 3: Detekcia platformy
```python
from platform_loader import get_current_platform
print(get_current_platform())  # Očakáva: "windows"
```

#### Test 4: Načítanie Windows modulov
```python
from platform_loader import get_hotkey_manager, get_tts_manager

hotkey_mgr = get_hotkey_manager()
tts_mgr = get_tts_manager()

print(type(hotkey_mgr).__name__)  # Očakáva: obsahuje "win"
print(type(tts_mgr).__name__)     # Očakáva: obsahuje "win"
```

### 3. Hotkey testy

#### Test 5: Registrácia hotkeys
```python
from platform_loader import get_hotkey_function

register_hotkeys = get_hotkey_function('register_global_hotkeys')
register_hotkeys()
```
**Očakávaný výsledok:** Hotkeys sa zaregistrujú bez chýb

#### Test 6: Funkčnosť hotkeys
- Stlačte `Alt+X` - má spustiť/zastaviť čítanie
- Stlačte `Alt+C` - má kopírovať text
- Stlačte `Ctrl+Alt+I` - má otvoriť nastavenia

### 4. TTS testy

#### Test 7: Základný TTS test
```python
from platform_loader import get_tts_function

speak_text = get_tts_function('speak_text')
speak_text("Hello Windows TTS system")
```
**Očakávaný výsledok:** Text sa prečíta Windows TTS

#### Test 8: Dostupné hlasy
```python
from platform_loader import get_tts_function

get_voices = get_tts_function('get_available_voices')
voices = get_voices()
print(f"Dostupných hlasov: {len(voices)}")
```

### 5. GUI testy

#### Test 9: Všetky záložky
- Otvorte každú záložku v GUI
- Skontrolujte, či sa načítavajú bez chýb
- Overte funkčnosť všetkých tlačidiel

#### Test 10: Nastavenia
- Zmeňte jazyk aplikácie
- Zmeňte TTS hlas
- Uložte nastavenia a reštartujte aplikáciu

### 6. OCR testy

#### Test 11: OCR funkcionalita
- Spustite čítanie (`Alt+X`)
- Umiestnite text na obrazovku
- Overte, či sa text rozpozná a prečíta

### 7. Integračné testy

#### Test 12: Kompletný workflow
1. Spustite aplikáciu
2. Nastavte slovenský jazyk
3. Spustite automatické čítanie
4. Otvorte dokument s textom
5. Overte správne rozpoznanie a čítanie

## 🐛 Riešenie problémov

### Časté problémy a riešenia

#### 1. Import chyby
```
ImportError: cannot import name 'get_tts_function'
```
**Riešenie:** Skontrolujte, či je `platform_loader.py` správne implementovaný

#### 2. Hotkey nefungujú
**Riešenie:** 
- Spustite ako administrátor
- Skontrolujte Windows security nastavenia
- Overte, či nie sú hotkeys blokované iným softvérom

#### 3. TTS nefunguje
**Riešenie:**
- Skontrolujte Windows TTS nastavenia
- Overte, či sú nainštalované TTS hlasy
- Reštartujte Windows Audio službu

### Debug režim
```bash
# Spustenie s debug logmi
python main_qt.py --debug
```

## 📝 Checklist pre Windows vývojárov

### Pred začatím vývoja
- [ ] Python 3.8+ nainštalovaný
- [ ] Git repository naklonovaný
- [ ] Virtual environment vytvorený
- [ ] Závislosti nainštalované

### Počas vývoja
- [ ] Platform loader funguje správne
- [ ] Windows moduly sa načítavajú
- [ ] Hotkeys fungujú
- [ ] TTS funguje
- [ ] GUI sa spúšťa bez chýb
- [ ] OCR rozpoznáva text

### Pred odovzdaním
- [ ] Všetky testy prešli
- [ ] Kód je zdokumentovaný
- [ ] Žiadne hardcoded cesty
- [ ] Error handling implementovaný
- [ ] Loggovanie funguje správne

## 🔗 Kontakt a podpora

Pre otázky a podporu kontaktujte hlavného vývojára alebo vytvorte issue v repository.

---
*Dokumentácia vytvorená: 2025-09-24*
*Verzia: 1.0*
