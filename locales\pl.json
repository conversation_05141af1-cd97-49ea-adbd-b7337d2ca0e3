{"app_title": "Czytnik napisów", "main_tab": "Główne sterowanie", "debug_tab": "Parametry debugowania", "detection_tab": "Wykrywanie napisów", "start_reading": "Rozpocznij czytanie", "stop_reading": "Zatrzymaj czytanie", "hotkey_hint": "lub naciśnij Cmd+Alt+Ctrl+V", "mode_group": "Tryb czytania", "mode_full_auto": "Całkowicie automatyczny", "mode_static": "Statyczny", "mode_dynamic": "Dynamiczny", "ocr_group": "OCR", "ocr_threshold": "Próg OCR:", "language_group": "Język", "app_language": "Język aplikacji:", "reading_language": "Język czytania napisów:", "performance_mode": "<PERSON><PERSON> wydajności:", "custom_settings": "Ustawienia", "clear_cache": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pamięć podręczną", "optimize_memory": "<PERSON>ty<PERSON><PERSON><PERSON><PERSON>", "speed_test": "⚡ Test prędkości", "tts_group": "Mowa (TTS)", "speech_rate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mowy:", "speech_volume": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mowy:", "allow_uppercase": "Zezwól na czytanie wielkich liter", "perf_speed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (beam=1)", "perf_balanced": "Zrównoważony (beam=4)", "perf_quality": "<PERSON><PERSON><PERSON><PERSON><PERSON> (beam=5)", "perf_custom": "Niestandardowy", "tooltip_custom_params": "Skonfiguruj niestandardowe parametry tłumaczenia", "tooltip_clear_cache": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pamięć podręczną tłumaczeń aby zao<PERSON><PERSON>ęd<PERSON>ć pamięć", "tooltip_optimize_memory": "<PERSON><PERSON><PERSON><PERSON> optymalizację p<PERSON>", "tooltip_speed_test": "Uruchom test prędkości tłumaczenia", "tts_detecting_subtitles": "Wykrywam typ napisów.", "tts_starting_continuous": "Rozpoczynam ciągłe wykrywanie napisów.", "tts_starting_reading": "Zaczynam czytać Ci napisy.", "tts_ending_reading": "Kończę czytanie.", "tts_detected_dynamic": "Wykryto napisy <PERSON>zne.", "tts_detected_static": "Wykryto napisy statyczne.", "tts_switched_dynamic": "Przełączono na przetwarzanie dynamiczne.", "tts_switched_static": "Przełączono na przetwarzanie statyczne.", "tts_switched_to_static": "Przełączono na napisy statyczne.", "demo_expired_message": "Limit czasu demo 20 minut wygasł na dziś. Kup pełną wersję lub poczekaj do jutra.", "demo_time_remaining": "🔒 Wersja demo, zostało Ci na dziś {time}", "purchase_button": "<PERSON><PERSON> na rok", "full_version_status": "🔓 <PERSON><PERSON>łna wersja, wa<PERSON>na do {expires_at} ({days_remaining} dni)", "demo_expired_title": "<PERSON>zas <PERSON> wygasł", "demo_expired_dialog": "Twój dzienny czas demo wygasł.\n\nAby kontynuować korzystanie z aplikacji, kup pełną wersję na rok.\n\nCzas demo zostanie odnowiony jutro.", "demo_test_button": "<PERSON><PERSON><PERSON> demo", "full_test_button": "Pełna wersja", "tts_test_button": "🔊 Test TTS", "toolbar_actions": "<PERSON><PERSON><PERSON><PERSON>", "reset_completed_title": "<PERSON>set z<PERSON>", "reset_completed_message": "Wszystkie ustawienia zostały przywrócone do wartości domyślnych zgodnie z językiem systemu.", "start_reading_section": "ROZPOCZNIJ CZYTANIE", "translator_stats": "Statystyki", "translator_stats_legacy": "Statystyki: System starszego typu", "translator_stats_unavailable": "Statystyki: Multi-tłumacz niedostępny", "translator_stats_error": "Statystyki: Błąd inicjalizacji", "reading_allowed": "DOZWOLONE", "reading_forbidden": "ZABRONIONE", "uppercase_reading_status": "Czytanie wielkich liter jest", "keyboard_image_not_found": "<PERSON><PERSON><PERSON> klawiatury nie został znaleziony (keyboard_MAC_200.jpg)", "keyboard_image_error": "Błąd podczas ładowania obrazu:", "performance_speed": "🚀 <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (beam=1)", "performance_balanced": "⚖️ Zrównoważony (beam=4)", "performance_quality": "🎯 <PERSON><PERSON><PERSON><PERSON><PERSON> (beam=5)", "performance_custom": "⚙️ Niestandardowy", "quality_fast": "⚡ <PERSON><PERSON><PERSON><PERSON>", "quality_balanced": "⚖️ Zrównoważony", "quality_quality": "🏆 <PERSON><PERSON><PERSON><PERSON><PERSON>", "quality_custom": "⚙️ Niestandardowy", "no_voices_available": "Brak dostępnych głosów", "statistics_not_available": "Statystyki: Niedostępne", "statistics_legacy_system": "Statystyki: System starszego typu", "statistics_initialization_error": "Statystyki: Błąd inicjalizacji", "connection_status_enter_credentials": "Status: ❌ Wprowadź klucz API i region", "connection_status_testing": "Status: 🔄 Testowanie połączenia...", "connection_status_invalid_credentials": "Status: ❌ Nieprawidłowe dane uwierzytelniające", "connection_status_success": "Status: ✅ Połączenie udane", "connection_status_failed": "Status: ❌ Test połączenia nie powiódł się", "connection_status_error": "Status: ❌ Błąd: ", "connection_status_configured": "Status: ✅ <PERSON> ustawi<PERSON>", "connection_status_not_configured": "Status: <PERSON><PERSON>", "filtering_enabled": "WŁĄCZONE", "filtering_disabled": "WYŁĄCZONE", "filtering_active": "Aktywne (X={x}px)", "filtering_detecting": "Wykrywanie ({samples}/5 próbek)", "filtering_waiting": "Oczekiwanie na wykrycie", "filtering_all_text": "Pobiera cały tekst bez filtrowania", "device_cpu": "CPU", "device_mps": "MPS (Apple Silicon)", "device_cuda": "CUDA (NVIDIA)", "model_status_not_loaded": "Model nie jest za<PERSON><PERSON><PERSON>y", "model_status_loaded": "<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "model_status_loading": "Ładowanie modelu...", "memory_usage": "<PERSON><PERSON><PERSON><PERSON>: {memory} MB", "voice_for_reading": "🎤 Głos do czytania:", "voice_for_reading_tooltip": "Wybierz głos do czytania napisów i komunikatów systemowych", "available_languages_info": "Dostępne: {count} języków (OCR: {ocr}, TTS: {tts})", "available_source_info": "Dostępne: {count} j<PERSON><PERSON><PERSON><PERSON> (OCR ∩ tłumacz)", "available_target_info": "Dostępne: {count} j<PERSON><PERSON><PERSON><PERSON> (TTS ∩ tłumacz)", "speed_test_tooltip": "Uruchom test prędkości tłumaczenia", "custom_settings_button": "⚙️ Us<PERSON>wienia", "custom_settings_tooltip": "Skonfiguruj niestandardowe parametry tłumaczenia", "similarity_thresholds": "⚙️ Podstawowe progi podobieństwa", "tts_duplicate_threshold": "Próg duplikatów TTS", "stability_similarity": "Stabiln<PERSON><PERSON><PERSON> linii (podobieństwo)", "two_frame_stability": "Stabiln<PERSON>ść dw<PERSON>ch klatek", "static_similarity": "Podobieństwo statyczne", "static_stability_threshold": "Próg stabilności statycznej", "static_replacement_threshold": "Próg zastąpienia statycznego", "cycling_stability": "🔄 <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> c<PERSON>zna", "min_cycles_first_line": "<PERSON> 1. linia", "min_cycles_second_line": "<PERSON> 2. linia", "min_cycles_second_growth": "<PERSON> cykli wzrost 2. linii", "single_text_cycles": "Cykle stabilności pojedynczego tekstu", "different_texts_count": "Liczba różnych tekstów do przełączenia", "dynamic_growth": "📈 Dynamiczny wzrost", "dynamic_growth_similarity": "Podobieństwo dynamicznego wzrostu", "dynamic_min_word_length": "<PERSON> dł<PERSON><PERSON><PERSON><PERSON> do<PERSON> słów", "dynamic_max_words": "Max słów dodanych na raz", "full_auto_thresholds": "🤖 Progi całkowicie automatyczne", "fa_initial_static": "FA: początkowy próg statyczny", "fa_initial_dynamic": "FA: początkowy próg dynamiczny", "fa_stable_static": "FA: stabilny próg statyczny", "fa_stable_dynamic": "FA: stabilny próg dynamiczny", "fa_min_samples": "FA: min stabilnych pró<PERSON>", "fa_replace_threshold": "FA: próg zastąpienia", "ocr_analysis": "🔍 OCR i analiza", "ocr_threshold_param": "Próg OCR", "dynamic_ocr_threshold": "Dynamiczny próg OCR", "min_text_length": "Min dług<PERSON>ć tekstu do analizy", "tests_tab": "Testy", "tolerance_group": "Tolerancje pozycji napisów", "ocr_interval_group": "Interwał OCR", "filtering_group": "Filtrowanie wedł<PERSON> lewej strony", "actions_group": "<PERSON><PERSON><PERSON><PERSON>", "y_threshold_label": "Próg Y napisów (%):", "reference_logging": "Referencyjne logowanie do analizy detekcji", "current_logging_to": "Aktualne logowanie do:", "standard_logging": "app.log (standardowe logowanie)", "static_logging": "static.log (dla napisów statycznych)", "dynamic_logging": "dynamic.log (dla napisów dynamicznych)", "clear_test_logs": "<PERSON><PERSON><PERSON><PERSON><PERSON>ć wszystkie logi testowe", "open_logs_folder": "Otwórz folder z logami", "source_language_label": "Język źródłowy:", "target_language_label": "Język docelowy:", "mode_off": "Wyłączony", "mode_manual": "Ręczny", "mode_auto": "Automatyczny", "mode_smart": "Inteligentny", "quality_profile_label": "Prof<PERSON>:", "profile_fast": "⚡ <PERSON><PERSON><PERSON><PERSON>", "profile_balanced": "⚖️ Zrównoważony", "profile_quality": "🏆 <PERSON><PERSON><PERSON><PERSON><PERSON>", "profile_custom": "⚙️ Niestandardowy", "active_status": " (aktywny)", "not_exists": "nie istnieje", "error_loading_stats": "Błąd ładowania statystyk: {error}", "reading_uppercase_enabled": "Czytanie wielkich liter jest WŁĄCZONE", "reading_uppercase_disabled": "Czytanie wielkich liter jest WYŁĄCZONE", "speech_rate_set": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mowy ustawiona na {rate}", "speech_volume_set": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ć mowy ustawiona na {volume}", "ocr_threshold_set": "Próg OCR ustawiony na {threshold}", "thread_safe_status_update": "Thread‑bezpieczna aktualizacja statusu.", "start_stop_action": "Start/Stop", "cancel_button": "<PERSON><PERSON><PERSON>", "test_text_placeholder": "Wprowadź tekst do testu:", "error_no_input": "BŁĄD: Wprowadź tekst do tłumaczenia!", "swap_languages_button": "🔄 Zamień języki", "confirmation_dialog": "Potwierdzenie", "success_dialog": "Sukces", "error_dialog": "Błąd", "warning_dialog": "Ostrzeżenie", "critical_error_dialog": "Błąd krytyczny", "status_initialized": "✅ <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "status_not_initialized": "❌ <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "device_mps_gpu": "🚀 GPU MPS (Apple Silicon)", "device_cuda_gpu": "🚀 GPU CUDA", "device_cpu_only": "💻 CPU", "memory_usage_model": "~2-3 GB (model 600M)", "memory_usage_none": "0 GB", "test_connection_button": "🧪 Test połączenia", "test_connection_tooltip": "Testuje połączenie API z bieżącymi danymi uwierzytelniającymi", "filtering_status_all_text": "Bierze cały tekst bez filtrowania", "filtering_status_waiting": "Czeka na wykrycie", "filtering_status_active": "Aktywny (X={x}px)", "filtering_status_detecting": "Wykrywa ({samples}/5 próbek)", "reset_left_detection": "Reset wykrywania lewej strony", "report_anomaly": "Zgłoś anomalię", "dialog_ok": "✅ OK", "dialog_cancel": "❌ <PERSON><PERSON><PERSON>", "dialog_reset": "🔄 Reset do domyślnych", "generation_settings": "🔧 <PERSON>row<PERSON>e", "penalty_settings": "⚖️ Kary", "optimization_settings": "🚀 Optymalizacje", "info_section": "ℹ️ Informacje", "max_length_param": "<PERSON><PERSON> dł<PERSON>:", "min_length_param": "<PERSON>:", "num_beams_param": "Liczba beams:", "use_cache_param": "Użyj cache", "enable_fast_mode": "Włącz tryb szybki:", "memory_optimization": "Optymalizacja pamięci:", "beam_search_settings": "🔹 Beam Search", "penalties_settings": "🔹 Kary i regulacje", "length_limits_settings": "🔹 <PERSON><PERSON> długo<PERSON>ci", "apple_optimizations": "🔹 Optymalizacje Apple Silicon M1", "cache_settings": "🔹 Ustawienia cache", "api_settings": "🔹 Ustawienia API", "enable_cache": "Włącz cache", "max_cache_size": "Maks. rozmiar cache:", "timeout_param": "Timeout:", "rate_limit_param": "<PERSON>it <PERSON>:", "batch_size_param": "Rozmiar partii:", "seconds_suffix": " sekund", "tokens_suffix": " tokenów", "status_label": "Status:", "active_log_suffix": " (aktywny)", "file_not_exists": "nie istnieje", "stats_error_prefix": "Błąd podczas ładowania statystyk: ", "log_files_cleared": "Wyczyszczone pliki logów testowych: ", "no_log_files_to_clear": "Brak plików logów testowych do wyczyszczenia", "logs_folder_opened": "Otwarty folder logów: ", "anomaly_report_started": "Raport anomalii uruchomiony przez przycisk GUI (Qt)", "detection_reset": "Wykrywanie lewej strony dynamicznych napisów zostało zresetowane", "filtering_status_on": "WŁĄCZONE", "filtering_status_off": "WYŁĄCZONE", "all_modes_enabled": "wszystkie tryby włączone, ustawienia przywrócone", "switched_to_static": "przełączono na tryb statyczny", "connection_status_missing_credentials": "Status: ❌ Wprowadź klucz API i region", "max_retries_param": "Maks. ponownych prób:", "beam_search_group": "🔹 Beam Search", "penalties_group": "🔹 Kary i regulacje", "no_repeat_ngram_label": "Rozmiar no repeat ngram:", "no_repeat_ngram_tooltip": "Blokuje powtarzanie n-gramów (np. 'the the the')\n0 = w<PERSON><PERSON><PERSON><PERSON><PERSON>, 2-4 = typo<PERSON> warto<PERSON>ci", "repetition_penalty_label": "Kara za powtórzenia:", "repetition_penalty_tooltip": "<PERSON><PERSON><PERSON> powtarzające się słowa\n>1.0 = mniej powtórzeń, 1.1-1.3 = zale<PERSON>", "length_penalty_label": "Kara za długość:", "length_penalty_tooltip": "Wpływa na preferencję dłuższych/kr<PERSON><PERSON><PERSON>ch zdań\n>1.0 = dłuższe tłumaczenia, <1.0 = kr<PERSON><PERSON><PERSON> tłumaczenia", "length_limits_group": "🔹 <PERSON><PERSON> długo<PERSON>ci", "max_length_label": "<PERSON><PERSON><PERSON>:", "max_length_not_supported_label": "<PERSON><PERSON><PERSON> (nieobsługiwane):", "optimizations_group": "🔹 Optymalizacje dla Apple Silicon M1", "fast_mode_label": "Włącz tryb szybki:", "fast_mode_tooltip": "Włącza szybkie optymalizacje", "memory_opt_label": "Optymalizacja pamięci:", "memory_opt_tooltip": "Włącza optymalizacje pamięci", "info_group": "ℹ️ Informacje", "reset_to_defaults": "🔄 Resetuj do domyślnych", "ocr_interval_label": "Interwał OCR (s):"}