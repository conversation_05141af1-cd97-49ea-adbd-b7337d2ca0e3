#!/usr/bin/env python3
"""
Test script pre nové hotkey funkcie
"""

import sys
import logging
import time

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_selected_text_functions():
    """Test funkcií pre získanie označeného textu"""
    print("🧪 TESTOVANIE FUNKCIÍ PRE OZNAČENÝ TEXT")
    print("=" * 50)
    
    try:
        from hotkey_manager import get_selected_text, handle_read_selected_text
        
        print("✅ Import funkcií úspešný")
        
        # Test získania označeného textu
        print("\n📋 Test získania označeného textu:")
        print("   Označte nejaký text v inom okne a stlačte Enter...")
        input("   Stlačte Enter po označení textu...")
        
        selected_text = get_selected_text()
        if selected_text:
            print(f"✅ Označený text získaný: '{selected_text[:100]}...'")
        else:
            print("⚠️ Žia<PERSON>y označený text nenájdený")
        
        return True
        
    except ImportError as e:
        print(f"❌ Chyba importu: {e}")
        return False
    except Exception as e:
        print(f"❌ Chyba: {e}")
        return False

def test_pyperclip():
    """Test pyperclip knižnice"""
    print("\n📦 TESTOVANIE PYPERCLIP")
    print("=" * 30)
    
    try:
        import pyperclip
        
        # Test základnej funkcionality
        test_text = "Test text pre pyperclip"
        pyperclip.copy(test_text)
        
        retrieved_text = pyperclip.paste()
        
        if retrieved_text == test_text:
            print("✅ pyperclip funguje správne")
            return True
        else:
            print(f"❌ pyperclip test zlyhal: '{retrieved_text}' != '{test_text}'")
            return False
            
    except ImportError:
        print("❌ pyperclip nie je nainštalovaný")
        print("   Nainštalujte: pip install pyperclip")
        return False
    except Exception as e:
        print(f"❌ pyperclip chyba: {e}")
        return False

def test_tts_functions():
    """Test TTS funkcií"""
    print("\n🔊 TESTOVANIE TTS FUNKCIÍ")
    print("=" * 30)
    
    try:
        from platform_loader import get_tts_function
        is_speaking = get_tts_function('is_speaking')
        stop_speaking = get_tts_function('stop_speaking')
        speak_text = get_tts_function('speak_text')
        
        print("✅ Import TTS funkcií úspešný")
        
        # Test is_speaking
        speaking_status = is_speaking()
        print(f"📊 Aktuálny stav čítania: {speaking_status}")
        
        # Test speak_text
        print("🔊 Test čítania textu...")
        speak_text("Toto je test nových hotkey funkcií")
        
        time.sleep(1)
        
        # Test stop_speaking
        print("🛑 Test zastavenia čítania...")
        stop_speaking()
        
        return True
        
    except ImportError as e:
        print(f"❌ Chyba importu TTS: {e}")
        return False
    except Exception as e:
        print(f"❌ TTS chyba: {e}")
        return False

def test_hotkey_combinations():
    """Test hotkey kombinácií"""
    print("\n⌨️ TESTOVANIE HOTKEY KOMBINÁCIÍ")
    print("=" * 35)
    
    try:
        from hotkey_manager import setup_pynput_hotkeys
        
        print("✅ Import hotkey managera úspešný")
        print("📋 Nové hotkey kombinácie:")
        print("   Alt+X: Toggle čítania titulkov")
        print("   Alt+C: Čítanie označeného textu")
        print("")
        print("🔧 Logika Alt+C:")
        print("   - Ak je text označený → číta označený text")
        print("   - Ak nie je text označený a niečo sa číta → zastaví čítanie")
        print("   - Ak nie je text označený a nič sa nečíta → nič sa nestane")
        
        return True
        
    except ImportError as e:
        print(f"❌ Chyba importu hotkey managera: {e}")
        return False
    except Exception as e:
        print(f"❌ Hotkey manager chyba: {e}")
        return False

def interactive_test():
    """Interaktívny test nových funkcií"""
    print("\n🎮 INTERAKTÍVNY TEST")
    print("=" * 25)
    
    try:
        from hotkey_manager import handle_read_selected_text
        
        while True:
            print("\n📋 Možnosti:")
            print("1. Test čítania označeného textu")
            print("2. Test získania označeného textu")
            print("3. Ukončiť")
            
            choice = input("\nVyberte možnosť (1-3): ").strip()
            
            if choice == '1':
                print("\n🔊 Test čítania označeného textu:")
                print("   1. Označte text v inom okne")
                print("   2. Stlačte Enter")
                input("   Pripravený? Stlačte Enter...")
                
                handle_read_selected_text()
                
            elif choice == '2':
                print("\n📋 Test získania označeného textu:")
                print("   1. Označte text v inom okne")
                print("   2. Stlačte Enter")
                input("   Pripravený? Stlačte Enter...")
                
                from hotkey_manager import get_selected_text
                text = get_selected_text()
                
                if text:
                    print(f"✅ Získaný text: '{text[:200]}...'")
                else:
                    print("⚠️ Žiadny text nenájdený")
                    
            elif choice == '3':
                print("👋 Ukončujem test...")
                break
            else:
                print("❌ Neplatná voľba")
        
        return True
        
    except Exception as e:
        print(f"❌ Chyba v interaktívnom teste: {e}")
        return False

def main():
    """Hlavná funkcia"""
    print("🚀 TEST NOVÝCH HOTKEY FUNKCIÍ")
    print("=" * 40)
    print("📋 Testované funkcie:")
    print("   • Alt+X: Toggle čítania")
    print("   • Alt+C: Čítanie označeného textu")
    print("   • Získavanie označeného textu")
    print("   • TTS integrácia")
    print("=" * 40)
    
    # Spusti všetky testy
    tests = [
        ("pyperclip knižnica", test_pyperclip),
        ("TTS funkcie", test_tts_functions),
        ("Hotkey kombinácie", test_hotkey_combinations),
        ("Funkcie pre označený text", test_selected_text_functions),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 Spúšťam test: {test_name}")
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test '{test_name}' zlyhal: {e}")
            results.append((test_name, False))
    
    # Súhrn výsledkov
    print("\n" + "=" * 40)
    print("📊 SÚHRN TESTOV:")
    print("=" * 40)
    
    success_count = 0
    for test_name, result in results:
        status = "✅ ÚSPECH" if result else "❌ CHYBA"
        print(f"   {status}: {test_name}")
        if result:
            success_count += 1
    
    print(f"\n🎯 Úspešnosť: {success_count}/{len(results)} ({success_count/len(results)*100:.1f}%)")
    
    if success_count == len(results):
        print("\n🎉 VŠETKY TESTY PREŠLI!")
        print("💡 Môžete spustiť interaktívny test")
        
        if input("\nSpustiť interaktívny test? (y/n): ").lower() == 'y':
            interactive_test()
    else:
        print("\n⚠️ NIEKTORÉ TESTY ZLYHALI")
        print("🔧 Prosím opravte chyby uvedené vyššie")
    
    print("\n👋 Test dokončený")

if __name__ == "__main__":
    main()
