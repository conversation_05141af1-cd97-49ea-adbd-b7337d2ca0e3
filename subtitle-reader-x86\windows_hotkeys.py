#!/usr/bin/env python3
"""
Windows-specific global hotkey implementation using Win32 API
"""

import logging
import threading
import time
import ctypes
from ctypes import wintypes
import queue

# Windows API constants
MOD_ALT = 0x0001
MOD_CONTROL = 0x0002
MOD_SHIFT = 0x0004
MOD_WIN = 0x0008

VK_X = 0x58  # X key
VK_C = 0x43  # C key
VK_LEFT = 0x25
VK_RIGHT = 0x27
VK_UP = 0x26
VK_DOWN = 0x28
VK_I = 0x49  # I key

# Windows API functions
user32 = ctypes.windll.user32
kernel32 = ctypes.windll.kernel32

class WindowsHotkeyManager:
    """Windows-specific global hotkey manager using RegisterHotKey API"""
    
    def __init__(self):
        self.hotkeys = {}
        self.hotkey_id = 1
        self.running = False
        self.thread = None
        self.command_queue = queue.Queue()
        
    def register_hotkey(self, modifiers, vk_code, command):
        """Register a global hotkey"""
        try:
            # Register hotkey with Windows
            result = user32.RegisterHotKeyW(
                None,  # Window handle (None for global)
                self.hotkey_id,  # Hotkey ID
                modifiers,  # Modifier keys
                vk_code  # Virtual key code
            )
            
            if result:
                self.hotkeys[self.hotkey_id] = {
                    'modifiers': modifiers,
                    'vk_code': vk_code,
                    'command': command
                }
                logging.info(f"✅ Windows hotkey registered: ID={self.hotkey_id}, modifiers={modifiers}, vk={vk_code}, command={command}")
                self.hotkey_id += 1
                return True
            else:
                error_code = kernel32.GetLastError()
                logging.error(f"❌ Failed to register Windows hotkey: error={error_code}")
                return False
                
        except Exception as e:
            logging.error(f"❌ Exception registering Windows hotkey: {e}")
            return False
    
    def start_listening(self):
        """Start listening for hotkey messages"""
        if self.running:
            return
            
        self.running = True
        self.thread = threading.Thread(target=self._message_loop, daemon=True)
        self.thread.start()
        logging.info("🎧 Windows hotkey listener started")
    
    def stop_listening(self):
        """Stop listening for hotkey messages"""
        self.running = False
        
        # Unregister all hotkeys
        for hotkey_id in self.hotkeys.keys():
            try:
                user32.UnregisterHotKey(None, hotkey_id)
                logging.debug(f"🗑️ Unregistered Windows hotkey ID={hotkey_id}")
            except Exception as e:
                logging.error(f"❌ Error unregistering hotkey {hotkey_id}: {e}")
        
        self.hotkeys.clear()
        
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=2.0)
            
        logging.info("🛑 Windows hotkey listener stopped")
    
    def _message_loop(self):
        """Windows message loop for hotkey events"""
        try:
            # Message structure
            class MSG(ctypes.Structure):
                _fields_ = [
                    ("hwnd", wintypes.HWND),
                    ("message", wintypes.UINT),
                    ("wParam", wintypes.WPARAM),
                    ("lParam", wintypes.LPARAM),
                    ("time", wintypes.DWORD),
                    ("pt", wintypes.POINT)
                ]
            
            msg = MSG()
            
            logging.info("🔄 Windows hotkey message loop started")
            
            while self.running:
                # Get message with timeout
                result = user32.PeekMessageW(
                    ctypes.byref(msg),
                    None,  # Any window
                    0x0312,  # WM_HOTKEY
                    0x0312,  # WM_HOTKEY
                    0x0001   # PM_REMOVE
                )
                
                if result:
                    # Process hotkey message
                    if msg.message == 0x0312:  # WM_HOTKEY
                        hotkey_id = msg.wParam
                        if hotkey_id in self.hotkeys:
                            command = self.hotkeys[hotkey_id]['command']
                            logging.info(f"🎯 Windows hotkey triggered: ID={hotkey_id}, command={command}")
                            self.command_queue.put(command)
                        else:
                            logging.warning(f"⚠️ Unknown hotkey ID: {hotkey_id}")
                else:
                    # No message, sleep briefly
                    time.sleep(0.01)
                    
        except Exception as e:
            logging.error(f"❌ Error in Windows hotkey message loop: {e}")
            import traceback
            logging.debug(traceback.format_exc())
        finally:
            logging.info("🔄 Windows hotkey message loop ended")
    
    def get_command(self, timeout=None):
        """Get next command from queue"""
        try:
            if timeout is None:
                return self.command_queue.get_nowait()
            else:
                return self.command_queue.get(timeout=timeout)
        except queue.Empty:
            return None

def setup_windows_hotkeys(command_handler):
    """Setup Windows-specific global hotkeys"""
    try:
        hotkey_manager = WindowsHotkeyManager()
        
        # Register hotkeys
        hotkeys_to_register = [
            # Alt+X: Toggle reading
            (MOD_ALT, VK_X, 'toggle_reading'),
            # Alt+C: Read selected text
            (MOD_ALT, VK_C, 'read_selected_text'),
            # Ctrl+Alt+Arrow keys: Volume/Rate control
            (MOD_CONTROL | MOD_ALT, VK_LEFT, 'volume_down'),
            (MOD_CONTROL | MOD_ALT, VK_RIGHT, 'volume_up'),
            (MOD_CONTROL | MOD_ALT, VK_DOWN, 'rate_down'),
            (MOD_CONTROL | MOD_ALT, VK_UP, 'rate_up'),
            # Ctrl+Alt+I: Report anomaly
            (MOD_CONTROL | MOD_ALT, VK_I, 'report_anomaly'),
        ]
        
        success_count = 0
        for modifiers, vk_code, command in hotkeys_to_register:
            if hotkey_manager.register_hotkey(modifiers, vk_code, command):
                success_count += 1
        
        if success_count > 0:
            # Start listening
            hotkey_manager.start_listening()
            
            # Start command processor
            def command_processor():
                logging.info("🔧 Windows hotkey command processor started")
                while hotkey_manager.running:
                    try:
                        command = hotkey_manager.get_command(timeout=1.0)
                        if command:
                            logging.info(f"🎯 Processing Windows hotkey command: {command}")
                            command_handler(command)
                    except Exception as e:
                        logging.error(f"❌ Error processing Windows hotkey command: {e}")
                logging.info("🔧 Windows hotkey command processor ended")
            
            processor_thread = threading.Thread(target=command_processor, daemon=True)
            processor_thread.start()
            
            logging.info(f"✅ Windows hotkeys setup complete: {success_count}/{len(hotkeys_to_register)} registered")
            return hotkey_manager
        else:
            logging.error("❌ No Windows hotkeys registered successfully")
            return None
            
    except Exception as e:
        logging.error(f"❌ Failed to setup Windows hotkeys: {e}")
        import traceback
        logging.debug(traceback.format_exc())
        return None

def get_hotkey_description():
    """Get description of available hotkeys"""
    return {
        'Alt+X': 'Zapnúť/vypnúť čítanie titulkov',
        'Alt+C': 'Čítanie označeného textu (inteligentné)',
        'Ctrl+Alt+←/→': 'Hlasitosť TTS',
        'Ctrl+Alt+↑/↓': 'Rýchlosť TTS',
        'Ctrl+Alt+I': 'Nahlásiť anomáliu'
    }

if __name__ == "__main__":
    # Test Windows hotkeys
    logging.basicConfig(level=logging.INFO)
    
    def test_command_handler(command):
        print(f"🎯 Test command received: {command}")
    
    print("🧪 Testing Windows hotkeys...")
    print("Available hotkeys:")
    for hotkey, description in get_hotkey_description().items():
        print(f"   {hotkey}: {description}")
    
    manager = setup_windows_hotkeys(test_command_handler)
    
    if manager:
        print("✅ Windows hotkeys active. Press hotkeys to test...")
        print("Press Ctrl+C to exit")
        
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n🛑 Stopping Windows hotkeys...")
            manager.stop_listening()
            print("👋 Test completed")
    else:
        print("❌ Failed to setup Windows hotkeys")
