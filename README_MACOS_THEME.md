# 🎨 macOS Dark Theme pre Subtitle Reader

## 📖 Prehľad

Subtitle Reader na Windows ARM64 teraz používa krásnu tmavú tému inšpirovanú macOS dizajnom. Táto téma poskytuje konzistentný a moderný vzhľad, k<PERSON><PERSON> je príjemný pre oči a profesionálne vyzerá.

## 🎯 Funkcie témy

### 🌙 **Tmavá farebná paleta**
- **Pozadie okna**: `#1c1c1e` - Hlavné tmavé pozadie
- **Obsah panely**: `#2c2c2e` - Tmavšie pozadie pre obsah
- **Karty/skupiny**: `#3a3a3c` - Stredne tmavé pozadie
- **Input polia**: `#48484a` - Svetlejšie pozadie pre vstupy

### 🔤 **Textové farby**
- **Hlavný text**: `#ffffff` - <PERSON><PERSON><PERSON> bi<PERSON>
- **Se<PERSON>nd<PERSON><PERSON>y text**: `#ebebf5` - <PERSON><PERSON><PERSON>
- **<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> text**: `#ebebf599` - <PERSON><PERSON><PERSON> s 60% priehľadnosťou
- **Zakázaný text**: `#ebebf54d` - Šedý s 30% priehľadnosťou

### 🎨 **Systémové farby**
- **Hlavná modrá**: `#007aff` - Apple modrá pre tlačidlá
- **Úspech**: `#30d158` - Zelená pre pozitívne akcie
- **Varovanie**: `#ff9f0a` - Oranžová pre upozornenia
- **Chyba**: `#ff453a` - Červená pre chyby
- **Info**: `#bf5af2` - Fialová pre informácie

## 🧩 **Komponenty**

### 📑 **Tabuľky (Tabs)**
- Zaoblené rohy s jemným prechodom
- Aktívny tab zvýraznený modrou farbou
- Hover efekty pre lepšiu interakciu

### 🔘 **Tlačidlá**
- **Primary**: Modrá farba pre hlavné akcie
- **Success**: Zelená pre pozitívne akcie (Start)
- **Danger**: Červená pre nebezpečné akcie (Stop)
- **Secondary**: Šedá pre vedľajšie akcie

### 🎚️ **Slidery**
- Moderný dizajn s jemnými tieňmi
- Modrá farba pre vyplnenú časť
- Biela rukoväť s hover efektmi

### 📝 **Textové štýly**
- **Title**: Veľký, tučný text pre nadpisy
- **Subtitle**: Stredný text pre podnadpisy
- **Body**: Štandardný text pre obsah
- **Caption**: Malý, kurzívny text pre poznámky
- **Value**: Modrý text pre hodnoty sliderov

### 📦 **Combo boxy**
- Tmavé pozadie s jemným okrajom
- Modrý okraj pri hover/focus
- Dropdown s tmavým pozadím

### 📜 **Scroll oblasti**
- Tenké, diskrétne scrollbary
- Hover efekty pre lepšiu viditeľnosť
- Zaoblené rohy

## 🔧 **Technické detaily**

### 📁 **Súbory témy**
- `macos_dark_theme.py` - Hlavný súbor s definíciami farieb a štýlov
- `qt_gui.py` - Aplikácia štýlov na GUI komponenty

### 🎨 **Použitie v kóde**
```python
from macos_dark_theme import get_main_window_style, get_button_styles, get_text_styles

# Aplikovanie hlavného štýlu
self.setStyleSheet(get_main_window_style())

# Použitie tlačidlových štýlov
button_styles = get_button_styles()
self.start_btn.setStyleSheet(button_styles['primary'])
self.stop_btn.setStyleSheet(button_styles['danger'])

# Použitie textových štýlov
text_styles = get_text_styles()
self.title_label.setStyleSheet(text_styles['title'])
self.value_label.setStyleSheet(text_styles['value'])
```

### 🎯 **Výhody**
1. **Konzistentnosť**: Jednotný dizajn naprieč celou aplikáciou
2. **Modulárnosť**: Ľahko upraviteľné farby a štýly
3. **Profesionálnosť**: Moderný vzhľad inšpirovaný macOS
4. **Čitateľnosť**: Optimalizované kontrasty pre lepšiu čitateľnosť
5. **Udržateľnosť**: Centralizované definície štýlov

## 🚀 **Budúce vylepšenia**

- [ ] Svetlá téma (Light Mode)
- [ ] Prispôsobiteľné farby
- [ ] Animácie a prechody
- [ ] Responzívny dizajn
- [ ] Témy pre rôzne platformy

## 📸 **Ukážky**

Aplikácia teraz vyzerá ako natívna macOS aplikácia s:
- Tmavým pozadím a jemnými kontrastmi
- Modernými tlačidlami s hover efektmi
- Elegantnými slidermi a combo boxmi
- Profesionálnou typografiou
- Konzistentnou farebnou paletou

---

**Subtitle Reader s macOS Dark Theme - Krásne, funkčné a profesionálne!** 🎉
