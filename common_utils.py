import re
try:
    import Levenshtein
except ImportError:
    # Fallback to RapidFuzz if Levenshtein is not available
    from rapidfuzz import fuzz
    class LevenshteinFallback:
        @staticmethod
        def distance(s1, s2):
            return int((100 - fuzz.ratio(s1, s2)) * max(len(s1), len(s2)) / 100)
    Levenshtein = LevenshteinFallback()
import common_config as config
import csv
import time
import logging

# 🔧 Import OCR text corrector pre opravu častých OCR chýb
try:
    from ocr_text_corrector import correct_ocr_text
    OCR_CORRECTOR_AVAILABLE = True
    logging.info("🔧 OCR Text Corrector načítaný úspešne")
except ImportError as e:
    OCR_CORRECTOR_AVAILABLE = False
    logging.warning(f"⚠️ OCR Text Corrector nie je dostupný: {e}")

def clean_text(text):
    if not isinstance(text, str):
        return [] # Return empty list

    # 🔧 OCR KOREKTOR: Oprava častých OCR chýb LEN PRE ANGLIČTINU
    corrected_text = text
    if OCR_CORRECTOR_AVAILABLE and hasattr(config, 'TRANSLATION_SOURCE_LANGUAGE'):
        source_lang = getattr(config, 'TRANSLATION_SOURCE_LANGUAGE', 'en')
        if source_lang and source_lang.lower() in ['en', 'eng', 'english']:
            try:
                corrected_text = correct_ocr_text(text, source_lang)
                if corrected_text != text:
                    logging.info(f"🔧 OCR OPRAVA: '{text}' -> '{corrected_text}'")
            except Exception as e:
                logging.error(f"❌ Chyba pri OCR korekcii: {e}")
                corrected_text = text

    lines = corrected_text.split('\n')
    filtered_lines = []

    for line in lines:
        # Vyčistenie každého riadku individuálne - ODSTRÁNENÉ ÚVODZOVKY
        # Ponechávame len písmená, čísla, medzery a základnú interpunkciu (bez úvodzoviek)
        temp_cleaned_line = re.sub(r'[^a-zA-Z0-9\s.,?!-áčďéěíňóřšťúůýžÁČĎÉĚÍŇÓŘŠŤŮŮÝŽ\\]]', '', line)
        stripped_line = re.sub(r'\s+', ' ', temp_cleaned_line).strip()

        # Skontrolujeme, či riadok obsahuje aspoň jeden alfanumerický znak
        # a či je jeho dĺžka po odstránení nežiaducich znakov dostatočná
        if re.search(r'[a-zA-Z0-9áčďéěíňóřšťúůýžÁČĎÉĚÍŇÓŘŠŤŮŮÝŽ]', stripped_line) and len(stripped_line) > 2:
            filtered_lines.append(stripped_line)

    return filtered_lines # Return list of lines

def clean_text_to_string(text):
    """
    Vyčistí text a vráti ho ako jeden string (nie zoznam riadkov).
    Používa sa pre porovnávanie textov v detekčných algoritmoch.
    """
    if not isinstance(text, str):
        return ""

    # Odstránenie \n znakov a nahradenie medzerami
    cleaned = text.replace('\n', ' ')

    # Odstránenie nežiaducich znakov (ponechanie len písmen, číslic, základnej interpunkcie)
    # ODSTRÁNENÉ ÚVODZOVKY - môžu spôsobovať problémy s prekladom
    cleaned = re.sub(r'[^a-zA-Z0-9\s.,?!-áčďéěíňóřšťúůýžÁČĎÉĚÍŇÓŘŠŤŮŮÝŽ\\]]', '', cleaned)

    # Normalizácia medzier (viacnásobné medzery → jedna medzera)
    cleaned = re.sub(r'\s+', ' ', cleaned).strip()

    # Kontrola, či obsahuje aspoň jeden alfanumerický znak
    if not re.search(r'[a-zA-Z0-9áčďéěíňóřšťúůýžÁČĎÉĚÍŇÓŘŠŤŮŮÝŽ]', cleaned):
        return ""

    # Kontrola minimálnej dĺžky
    if len(cleaned) <= 2:
        return ""

    return cleaned

def levenshtein_distance(s1, s2):
    return Levenshtein.distance(s1, s2)

def calculate_similarity(text1, text2):
    if not text1 and not text2: return 1.0
    if not text1 or not text2: return 0.0
    distance = levenshtein_distance(text1, text2)
    max_len = max(len(text1), len(text2))
    if max_len == 0: return 1.0
    return round(1.0 - (distance / max_len), 4)

def is_new_subtitle(text: str, threshold: float) -> bool:
    """
    Skontroluje, či je text dostatočne odlišný od textov v histórii TTS.
    """
    if not text:
        return False

    # Pridáme kontrolu proti poslednému spracovanému textu s veľmi vysokou podobnosťou
    if config.last_processed_dynamic_text:
        similarity_to_last = calculate_similarity(text, config.last_processed_dynamic_text)
        if similarity_to_last > 0.95:
            logging.debug(f"[DUPLICATE_CHECK] Text '{text}' je príliš podobný poslednému '{config.last_processed_dynamic_text}' (podobnosť: {similarity_to_last:.3f})")
            return False # Text je príliš podobný poslednému prečítanému, ignorujeme ho

    for i, old_text in enumerate(config.tts_history):
        similarity = calculate_similarity(text, old_text)
        if similarity > threshold:
            logging.debug(f"[DUPLICATE_CHECK] Text '{text}' je príliš podobný histórii[{i}] '{old_text}' (podobnosť: {similarity:.3f}, prah: {threshold})")
            return False # Našiel sa príliš podobný text v histórii

    logging.debug(f"[DUPLICATE_CHECK] Text '{text}' je nový (história: {len(config.tts_history)} textov, prah: {threshold})")
    return True # Žiadny podobný text sa nenašiel

def clear_tts_history():
    """Vyčistí históriu TTS textov. Užitočné pri štarte nového čítania."""
    config.tts_history.clear()
    config.last_processed_dynamic_text = ""
    logging.debug("[TTS_HISTORY] História TTS textov vyčistená")

def initialize_csv_log():
    """Initialize the CSV log file with a header."""
    if config.CSV_LOGGING_ENABLED:
        with open(config.CSV_LOG_FILE, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(["Timestamp", "CycleID", "ChangeType", "Similarity", "PreviousText", "NextText", "ModeChange", "TextToRead"])

def log_text_change_to_csv(cycle_id, change_type, similarity, prev_text, next_text, text_to_read=""):
    """Log the text change to a CSV file."""
    if config.CSV_LOGGING_ENABLED:
        with open(config.CSV_LOG_FILE, 'a', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow([time.strftime('%Y-%m-%d %H:%M:%S'), cycle_id, change_type, f"{similarity:.2f}", prev_text, next_text, "", text_to_read])

def log_mode_change_to_csv(cycle_id, old_mode, new_mode):
    """Log the mode change to a CSV file."""
    if config.CSV_LOGGING_ENABLED:
        with open(config.CSV_LOG_FILE, 'a', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow([time.strftime('%Y-%m-%d %H:%M:%S'), cycle_id, 'MODE_SWITCH', '1.0', old_mode, f'SWITCH_TO_{new_mode.upper()}', new_mode.upper()])
