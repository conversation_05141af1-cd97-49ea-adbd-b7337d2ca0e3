# -*- coding: utf-8 -*-
"""
Cross-platform hardware fingerprinting pre identifikáciu počítača
Vytvorí jedinečný identifikátor pre ka<PERSON> systém (macOS, Windows, Linux)
"""

import hashlib
import subprocess
import logging
import platform
from typing import Optional


def get_hardware_fingerprint() -> str:
    """
    Vytvorí jedinečný hardware fingerprint cross-platform.
    Kombinuje viacero hardware identifikátorov pre spoľahlivosť.
    """
    try:
        # Try cross-platform implementation first
        try:
            from platform_utils import get_system_info_provider
            provider = get_system_info_provider()
            fingerprint = provider.get_hardware_fingerprint()
            if fingerprint:
                return fingerprint
        except ImportError:
            logging.warning("⚠️ Cross-platform system info provider not available")
        except Exception as e:
            logging.debug(f"⚠️ Cross-platform fingerprinting failed: {e}")

        # Fallback to legacy implementation
        return _legacy_hardware_fingerprint()

    except Exception as e:
        logging.error(f"❌ Error creating hardware fingerprint: {e}")
        # Emergency fallback
        fallback = hashlib.sha256(platform.node().encode('utf-8')).hexdigest()[:32]
        return fallback


def _legacy_hardware_fingerprint() -> str:
    """Legacy macOS hardware fingerprinting implementation."""
    try:
        components = []

        # 1. Serial number (najspoľahlivejší)
        serial = get_system_serial()
        if serial:
            components.append(f"serial:{serial}")

        # 2. Hardware UUID
        hardware_uuid = get_hardware_uuid()
        if hardware_uuid:
            components.append(f"uuid:{hardware_uuid}")
        
        # 3. MAC adresa (ethernet/wifi)
        mac_address = get_primary_mac_address()
        if mac_address:
            components.append(f"mac:{mac_address}")
        
        # 4. Motherboard info
        motherboard = get_motherboard_info()
        if motherboard:
            components.append(f"board:{motherboard}")
        
        # 5. CPU info
        cpu_info = get_cpu_info()
        if cpu_info:
            components.append(f"cpu:{cpu_info}")
        
        # Ak nemáme žiadne komponenty, použij fallback
        if not components:
            components.append(f"fallback:{platform.node()}")
        
        # Vytvor hash z kombinovaných komponentov
        combined = "|".join(sorted(components))
        fingerprint = hashlib.sha256(combined.encode('utf-8')).hexdigest()[:32]
        
        logging.info(f"🔐 Hardware fingerprint vytvorený: {fingerprint[:8]}...")
        return fingerprint
        
    except Exception as e:
        logging.error(f"❌ Chyba pri vytváraní fingerprint: {e}")
        # Fallback fingerprint
        fallback = hashlib.sha256(platform.node().encode('utf-8')).hexdigest()[:32]
        return fallback


def get_system_serial() -> Optional[str]:
    """Získa sériové číslo systému (macOS)."""
    try:
        result = subprocess.run(
            ["system_profiler", "SPHardwareDataType"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            for line in result.stdout.split('\n'):
                if 'Serial Number' in line:
                    serial = line.split(':')[-1].strip()
                    if serial and serial != "(system)":
                        return serial
        
        # Alternatívny spôsob
        result = subprocess.run(
            ["ioreg", "-l"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            for line in result.stdout.split('\n'):
                if '"IOPlatformSerialNumber"' in line:
                    parts = line.split('=')
                    if len(parts) > 1:
                        serial = parts[1].strip().strip('"')
                        if serial:
                            return serial
                            
    except Exception as e:
        logging.debug(f"Chyba pri získavaní serial number: {e}")
    
    return None


def get_hardware_uuid() -> Optional[str]:
    """Získa hardware UUID (macOS)."""
    try:
        result = subprocess.run(
            ["system_profiler", "SPHardwareDataType"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            for line in result.stdout.split('\n'):
                if 'Hardware UUID' in line:
                    uuid = line.split(':')[-1].strip()
                    if uuid:
                        return uuid
        
        # Alternatívny spôsob cez ioreg
        result = subprocess.run(
            ["ioreg", "-rd1", "-c", "IOPlatformExpertDevice"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            for line in result.stdout.split('\n'):
                if '"IOPlatformUUID"' in line:
                    parts = line.split('=')
                    if len(parts) > 1:
                        uuid = parts[1].strip().strip('"')
                        if uuid:
                            return uuid
                            
    except Exception as e:
        logging.debug(f"Chyba pri získavaní hardware UUID: {e}")
    
    return None


def get_primary_mac_address() -> Optional[str]:
    """Získa primárnu MAC adresu."""
    try:
        # Skús získať MAC adresu cez networksetup
        result = subprocess.run(
            ["networksetup", "-listallhardwareports"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            lines = result.stdout.split('\n')
            for i, line in enumerate(lines):
                if 'Wi-Fi' in line or 'Ethernet' in line:
                    # Hľadaj MAC adresu v nasledujúcich riadkoch
                    for j in range(i+1, min(i+4, len(lines))):
                        if 'Ethernet Address' in lines[j]:
                            mac = lines[j].split(':')[-1].strip()
                            if mac and mac != "N/A":
                                return mac.replace(':', '').lower()
        
        # Fallback cez ifconfig
        result = subprocess.run(
            ["ifconfig"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            for line in result.stdout.split('\n'):
                if 'ether' in line and 'en0' in result.stdout:
                    parts = line.split()
                    if len(parts) >= 2:
                        mac = parts[1].replace(':', '').lower()
                        if mac and mac != "000000000000":
                            return mac
                            
    except Exception as e:
        logging.debug(f"Chyba pri získavaní MAC adresy: {e}")
    
    return None


def get_motherboard_info() -> Optional[str]:
    """Získa informácie o motherboard."""
    try:
        result = subprocess.run(
            ["system_profiler", "SPHardwareDataType"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            model_info = []
            for line in result.stdout.split('\n'):
                if 'Model Name' in line:
                    model = line.split(':')[-1].strip()
                    if model:
                        model_info.append(model)
                elif 'Model Identifier' in line:
                    identifier = line.split(':')[-1].strip()
                    if identifier:
                        model_info.append(identifier)
            
            if model_info:
                return "_".join(model_info)
                
    except Exception as e:
        logging.debug(f"Chyba pri získavaní motherboard info: {e}")
    
    return None


def get_cpu_info() -> Optional[str]:
    """Získa informácie o CPU."""
    try:
        result = subprocess.run(
            ["sysctl", "-n", "machdep.cpu.brand_string"],
            capture_output=True,
            text=True,
            timeout=5
        )
        
        if result.returncode == 0:
            cpu_brand = result.stdout.strip()
            if cpu_brand:
                # Skráti a normalizuj CPU string
                cpu_normalized = cpu_brand.replace(' ', '_').replace('@', '').replace('(', '').replace(')', '')
                return cpu_normalized[:50]  # Obmedz dĺžku
                
    except Exception as e:
        logging.debug(f"Chyba pri získavaní CPU info: {e}")
    
    return None


def verify_hardware_fingerprint(stored_fingerprint: str) -> bool:
    """
    Overí, či sa aktuálny hardware fingerprint zhoduje s uloženým.
    Umožňuje malé zmeny (napr. výmena RAM, disk).
    """
    try:
        current_fingerprint = get_hardware_fingerprint()
        
        # Presná zhoda
        if current_fingerprint == stored_fingerprint:
            return True
        
        # Fuzzy matching - povolí malé zmeny
        # Získaj komponenty pre oba fingerprints
        current_components = get_fingerprint_components()
        
        # Pre jednoduchosť, ak sa zhoduje aspoň 70% komponentov, považuj za valid
        # V produkčnom prostredí by ste mohli implementovať sofistikovanejšiu logiku
        
        logging.info(f"🔍 Fingerprint check: current={current_fingerprint[:8]}..., stored={stored_fingerprint[:8]}...")
        
        # Pre teraz použijeme presnú zhodu
        return current_fingerprint == stored_fingerprint
        
    except Exception as e:
        logging.error(f"❌ Chyba pri overovaní fingerprint: {e}")
        return False


def get_fingerprint_components() -> dict:
    """Vráti jednotlivé komponenty fingerprint pre analýzu."""
    return {
        "serial": get_system_serial(),
        "uuid": get_hardware_uuid(),
        "mac": get_primary_mac_address(),
        "motherboard": get_motherboard_info(),
        "cpu": get_cpu_info()
    }


# Test funkcia
if __name__ == "__main__":
    logging.basicConfig(level=logging.DEBUG)
    
    print("🔐 Hardware Fingerprint Test")
    print("=" * 40)
    
    components = get_fingerprint_components()
    for key, value in components.items():
        print(f"{key:12}: {value}")
    
    print("=" * 40)
    fingerprint = get_hardware_fingerprint()
    print(f"Fingerprint: {fingerprint}")
