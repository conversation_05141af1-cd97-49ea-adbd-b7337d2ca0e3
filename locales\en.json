{"app_title": "Subtitle Reader", "main_tab": "Main Control", "debug_tab": "Debug Parameters", "detection_tab": "Subtitle Detection", "start_reading": "Start Reading", "stop_reading": "Stop Reading", "hotkey_hint": "or press Cmd+Alt+Ctrl+V", "mode_group": "Reading Mode", "mode_full_auto": "Fully Automatic", "mode_static": "Static", "mode_dynamic": "Dynamic", "ocr_group": "OCR", "ocr_threshold": "OCR Threshold:", "language_group": "Language", "app_language": "Application Language:", "reading_language": "Subtitle Reading Language:", "performance_mode": "Performance Mode:", "custom_settings": "Settings", "clear_cache": "<PERSON>ache", "optimize_memory": "Optimize Memory", "speed_test": "Speed Test", "tts_group": "Speech (TTS)", "speech_rate": "Speech Rate:", "speech_volume": "Speech Volume:", "allow_uppercase": "Allow reading uppercase text", "perf_speed": "Speed (beam=1)", "perf_balanced": "Balanced (beam=4)", "perf_quality": "Quality (beam=5)", "perf_custom": "Custom", "tooltip_optimize_memory": "Force memory optimization", "tts_detecting_subtitles": "Detecting subtitle type.", "tts_starting_continuous": "Starting continuous subtitle detection.", "tts_starting_reading": "Starting to read subtitles for you.", "tts_ending_reading": "Ending reading.", "tts_detected_dynamic": "Detected dynamic subtitles.", "tts_detected_static": "Detected static subtitles.", "tts_switched_dynamic": "Switched to dynamic processing.", "tts_switched_static": "Switched to static processing.", "tts_switched_to_static": "Switched to static subtitles.", "demo_expired_message": "20 minutes demo time limit expired for today. Either purchase the full version or wait until tomorrow.", "demo_time_remaining": "🔒 Demo version, you have {time} left for today", "purchase_button": "Purchase for 1 year", "full_version_status": "🔓 Full version, valid until {expires_at} ({days_remaining} days)", "demo_expired_title": "Demo time expired", "demo_expired_dialog": "Your daily demo time has expired.\n\nTo continue using the application, please purchase the full version for one year.\n\nDemo time will be renewed tomorrow.", "demo_test_button": "Demo version", "full_test_button": "Full version", "tts_test_button": "🔊 Test TTS", "toolbar_actions": "Actions", "reset_completed_title": "Reset completed", "reset_completed_message": "All settings have been restored to default values according to system language.", "start_reading_section": "START READING", "translator_stats": "Statistics", "translator_stats_legacy": "Statistics: Legacy system", "translator_stats_unavailable": "Statistics: Multi-translator unavailable", "translator_stats_error": "Statistics: Initialization error", "reading_allowed": "allowed", "reading_forbidden": "forbidden", "uppercase_reading_status": "Reading uppercase letters is", "keyboard_image_not_found": "Keyboard image not found (keyboard_MAC_200.jpg)", "keyboard_image_error": "Error loading image:", "performance_speed": "🚀 Speed (beam=1)", "performance_balanced": "⚖️ Balanced (beam=4)", "performance_quality": "🎯 Quality (beam=5)", "performance_custom": "⚙️ Custom", "quality_fast": "⚡ Fast", "quality_balanced": "⚖️ Balanced", "quality_quality": "🏆 Quality", "quality_custom": "⚙️ Custom", "no_voices_available": "No voices available", "statistics_not_available": "Statistics: Not available", "statistics_legacy_system": "Statistics: Legacy system", "statistics_initialization_error": "Statistics: Initialization error", "connection_status_testing": "Status: 🔄 Testing connection...", "connection_status_invalid_credentials": "Status: ❌ Invalid credentials", "connection_status_success": "Status: ✅ Connection successful", "connection_status_failed": "Status: ❌ Connection test failed", "connection_status_error": "Status: ❌ Error:", "connection_status_configured": "Status: ✅ Credentials configured", "connection_status_not_configured": "Status: Not configured", "filtering_enabled": "ENABLED", "filtering_disabled": "DISABLED", "filtering_active": "Active (X={x}px)", "filtering_detecting": "Detecting ({samples}/5 samples)", "filtering_waiting": "Waiting for detection", "filtering_all_text": "Taking all text without filtering", "device_cpu": "CPU", "device_mps": "MPS (Apple Silicon)", "device_cuda": "CUDA (NVIDIA)", "model_status_not_loaded": "Model not loaded", "model_status_loaded": "Model loaded", "model_status_loading": "Loading model...", "memory_usage": "Memory: {memory} MB", "voice_for_reading": "🎤 Voice for reading:", "voice_for_reading_tooltip": "Select voice for reading subtitles and system messages", "available_languages_info": "Available: {count} languages (OCR: {ocr}, TTS: {tts})", "custom_settings_button": "⚙️ Settings", "similarity_thresholds": "⚙️ Basic similarity thresholds", "tts_duplicate_threshold": "TTS duplicate threshold", "stability_similarity": "Line stability (similarity)", "two_frame_stability": "Two frame stability", "static_similarity": "Static similarity", "static_stability_threshold": "Static stability threshold", "static_replacement_threshold": "Static replacement threshold", "cycling_stability": "🔄 Cycling stability", "min_cycles_first_line": "Min cycles 1st line", "min_cycles_second_line": "Min cycles 2nd line", "min_cycles_second_growth": "Min cycles 2nd line growth", "single_text_cycles": "Single text stability cycles", "different_texts_count": "Number of different texts for switch", "dynamic_growth": "📈 Dynamic growth", "dynamic_growth_similarity": "Dynamic growth similarity", "dynamic_min_word_length": "Min added word length", "dynamic_max_words": "Max words added at once", "full_auto_thresholds": "🤖 Fully automatic thresholds", "fa_initial_static": "FA: initial static threshold", "fa_initial_dynamic": "FA: initial dynamic threshold", "fa_stable_static": "FA: stable static threshold", "fa_stable_dynamic": "FA: stable dynamic threshold", "fa_min_samples": "FA: min stable samples", "fa_replace_threshold": "FA: replacement threshold", "ocr_analysis": "🔍 OCR and analysis", "ocr_threshold_param": "OCR threshold", "dynamic_ocr_threshold": "Dynamic OCR threshold", "min_text_length": "Min text length for analysis", "tests_tab": "Tests", "tolerance_group": "Subtitle position tolerances", "ocr_interval_group": "OCR interval", "filtering_group": "Left side filtering", "actions_group": "Actions", "y_threshold_label": "Y subtitle threshold (%):", "reference_logging": "Reference logging for detection analysis", "current_logging_to": "Current logging to:", "standard_logging": "app.log (standard logging)", "static_logging": "static.log (for static subtitles)", "dynamic_logging": "dynamic.log (for dynamic subtitles)", "clear_test_logs": "Clear all test logs", "open_logs_folder": "Open logs folder", "dialog_success": "Success", "dialog_error": "Error", "dialog_warning": "Warning", "dialog_confirmation": "Confirmation", "dialog_critical_error": "Critical error", "device_mps_gpu": "🚀 MPS GPU (Apple Silicon)", "device_cuda_gpu": "🚀 CUDA GPU", "device_cpu_only": "💻 CPU", "status_initialized": "✅ Initialized", "status_not_initialized": "❌ Not initialized", "memory_usage_model": "~2-3 GB (600M model)", "memory_usage_none": "0 GB", "test_connection": "🧪 Test connection", "test_connection_button": "🧪 Test connection", "test_connection_tooltip": "Tests API connection with current credentials", "cache_settings_group": "🗄️ Cache settings", "enable_cache": "Enable cache", "max_cache_size": "Max cache size:", "api_settings_group": "⚙️ API settings", "timeout_label": "Timeout:", "timeout_suffix": " seconds", "timeout_tooltip": "Timeout for API requests", "rate_limit_label": "Rate limit:", "rate_limit_suffix": " seconds", "rate_limit_tooltip": "Minimum interval between API requests", "batch_size_label": "Batch size:", "batch_size_tooltip": "Number of texts sent in one API request", "beam_search_group": "🔹 Beam Search", "penalties_group": "⚖️ Penalties", "no_repeat_ngram": "No repeat ngram size:", "no_repeat_ngram_tooltip": "Blocks repetition of n-grams (e.g. 'the the the')\n0 = disabled, 2-4 = typical values", "repetition_penalty": "Repetition penalty:", "repetition_penalty_tooltip": "Penalizes repeated words\n>1.0 = less repetition, 1.1-1.3 = recommended", "length_penalty": "Length penalty:", "length_limits_group": "🔹 Length limits", "max_length_label": "Max length:", "optimizations_group": "🚀 Optimizations", "fast_mode": "Enable fast mode:", "fast_mode_tooltip": "Enables fast optimizations", "memory_optimization": "Memory optimization:", "memory_optimization_tooltip": "Enables memory optimizations", "info_group": "ℹ️ Information", "reset_to_default": "🔄 Reset to defaults", "ok_button": "OK", "cancel_button": "❌ Cancel", "generation_group": "🎯 Text generation", "max_length_tokens": "Max length:", "tokens_suffix": " tokens", "min_length_tokens": "Min length:", "num_beams": "Number of beams:", "temperature": "Temperature:", "top_k": "Top-k:", "top_p": "Top-p:", "use_cache": "Use cache", "early_stopping": "Early stopping", "sampling": "Sampling", "source_language_label": "Source language:", "target_language_label": "Target language:", "mode_off": "Off", "mode_manual": "Manual", "mode_auto": "Automatic", "mode_smart": "Smart", "quality_profile_label": "Quality profile:", "profile_fast": "⚡ Fast", "profile_balanced": "⚖️ Balanced", "profile_quality": "🏆 Quality", "profile_custom": "⚙️ Custom", "active_status": " (active)", "not_exists": "does not exist", "error_loading_stats": "Error loading statistics: {error}", "reading_uppercase_enabled": "Reading uppercase letters is ENABLED", "reading_uppercase_disabled": "Reading uppercase letters is DISABLED", "speech_rate_set": "Speech rate set to {rate}", "speech_volume_set": "Speech volume set to {volume}", "ocr_threshold_set": "OCR threshold set to {threshold}", "thread_safe_status_update": "Thread‑safe status update.", "start_stop_action": "Start/Stop", "test_text_placeholder": "Enter text for test:", "swap_languages_button": "🔄 Swap languages", "confirmation_dialog": "Confirmation", "success_dialog": "Success", "error_dialog": "Error", "warning_dialog": "Warning", "critical_error_dialog": "Critical Error", "max_retries_param": "Max retries:", "no_repeat_ngram_label": "No repeat ngram size:", "repetition_penalty_label": "Repetition penalty:", "length_penalty_label": "Length penalty:", "max_length_not_supported_label": "Max length (not supported):", "fast_mode_label": "Enable fast mode:", "memory_opt_label": "Memory optimization:", "reset_to_defaults": "🔄 Reset to defaults", "ocr_interval_label": "OCR interval (s):", "temperature_label": "Temperature:", "top_k_label": "Top-k:", "top_p_label": "Top-p:", "no_repeat_ngram_size_label": "No repeat ngram size:", "early_stopping_label": "Early stopping", "sampling_label": "Sampling", "reset_to_default_button": "🔄 Reset to defaults", "ocr_value_initial": "0%", "rate_value_initial": "0", "volume_value_initial": "0%", "ocr_interval_initial": "0.00s", "status_credentials_configured": "Status: ✅ Credentials configured", "status_not_configured": "Status: Not configured", "switched_to_static": "switched to static mode", "all_modes_enabled": "all modes enabled", "reset_left_detection": "Reset left detection", "report_anomaly": "Report anomaly", "dialog_ok": "OK", "dialog_cancel": "Cancel", "seconds_suffix": " seconds", "timeout_param": "Timeout:", "rate_limit_param": "Rate limit:", "batch_size_param": "Batch size:", "memory_opt_tooltip": "Enables memory optimizations", "max_length_param": "Max length:", "min_length_param": "Min length:", "num_beams_param": "Number of beams:", "use_cache_param": "Use cache"}