import logging
import re

import common_config as config
import common_utils
from platform_loader import get_tts_function

def speak_text(*args, **kwargs):
    return get_tts_function('speak_text')(*args, **kwargs)
import csv_logger
from full_automatic_mode.full_automatic_logic import log_text_change_to_csv
import subtitle_area_detector

def validate_text_for_reading(text):
    """
    Validuje, či je text vhodný na čítanie.
    Filtruje OCR chyby a nezmyselné texty.
    """
    if not text or len(text.strip()) < 3:
        return False

    text = text.strip()

    # Filtre pre nevalidné texty
    invalid_patterns = [
        r'^[0-9\s\-\|]+$',  # Len čísla a špeciálne znaky
        r'^[^\w\s]+$',  # Len špeciálne znaky
        r'[0-9]{5,}',  # Dlhé sekvencie čísel
        r'[\|\-\+]{3,}',  # Opakuj<PERSON>ce sa špeciálne znaky
    ]

    # Pridaj filtre pre veľké písmená len ak nie sú povolené
    if not getattr(config, 'ALLOW_UPPERCASE_TEXT', False):
        invalid_patterns.extend([
            r'^[A-Z]{3,}$',  # Len veľké písmená (napr. "WAYMO", "VUZ", "HIC")
            r'[A-Z]{5,}',  # Dlhé sekvencie veľkých písmen
        ])

    # Kontrola nevalidných vzorov
    for pattern in invalid_patterns:
        if re.search(pattern, text):
            logging.debug(f"[VALIDATION] Text '{text}' neprešiel validáciou - vzor: {pattern}")
            return False

    # Kontrola pomeru písmen k špeciálnym znakom
    letters = len(re.findall(r'[a-zA-ZáčďéěíňóřšťúůýžÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ]', text))
    total_chars = len(text.replace(' ', ''))

    if total_chars > 0 and letters / total_chars < 0.6:  # Menej ako 60% písmen
        logging.debug(f"[VALIDATION] Text '{text}' má príliš málo písmen ({letters}/{total_chars})")
        return False

    # Kontrola rozumnej dĺžky
    if len(text) > 200:  # Príliš dlhý text
        logging.debug(f"[VALIDATION] Text '{text}' je príliš dlhý ({len(text)} znakov)")
        return False

    logging.debug(f"[VALIDATION] Text '{text}' prešiel validáciou")
    return True

# Funkcia process_dynamic_speech_queue bola odstránená - text sa teraz spracováva centrálne

def handle_single_line_dynamic_reading(current_text, cycle_id):
    """
    Logika čítania jednoriadkových textov v dynamickom režime.

    Kedy sa číta:
    1. Text zmizne → Prečítaj posledný zaznamenaný text
    2. Text sa zmení na úplne nový → Prečítaj starý text, začni s novým

    Kedy sa NEČÍTA:
    - Keď sa text len doplňuje (vysoká podobnosť)
    """

    # Získanie predchádzajúceho jednoriadkového textu
    previous_single_line = getattr(config, 'previous_single_line_text', "")

    # Kontrola minimálnej dĺžky textu
    min_length = getattr(config, 'MIN_DYNAMIC_TEXT_LENGTH', 5)

    if not current_text:  # Text zmizol
        if previous_single_line and len(previous_single_line) >= min_length:
            # Kontrola, či nie je duplicitný
            if common_utils.is_new_subtitle(previous_single_line, config.TTS_HISTORY_SIMILARITY_THRESHOLD):
                logging.info(f"[DYNAMIC] CycleID={cycle_id}: Jednoriadkový text zmizol, čítam posledný: '{previous_single_line}'")
                speak_text(previous_single_line, cycle_id)
                csv_logger.log_tts_event(cycle_id, previous_single_line, "TTS_SENT", "Dynamic mode - jednoriadkový text zmizol")
                config.tts_history.append(previous_single_line)
                config.last_processed_dynamic_text = previous_single_line
            else:
                logging.debug(f"[DYNAMIC] CycleID={cycle_id}: Jednoriadkový text zmizol, ale je duplicitný: '{previous_single_line}'")

        # Reset
        config.previous_single_line_text = ""
        config.candidate_line_buffer = ""

    elif previous_single_line:  # Máme predchádzajúci text
        # Kontrola podobnosti - je to doplňovanie alebo nový text?
        similarity = common_utils.calculate_similarity(previous_single_line, current_text)

        if similarity >= 0.8:  # Vysoká podobnosť = doplňovanie
            logging.debug(f"[DYNAMIC] CycleID={cycle_id}: Text sa doplňuje (podobnosť: {similarity:.3f}): '{previous_single_line}' → '{current_text}'")
            # Aktualizuj kandidátsky text, ale nečítaj
            config.candidate_line_buffer = current_text
            config.previous_single_line_text = current_text

        else:  # Nízka podobnosť = možno nový text
            # KONTROLA: Je to doplňovanie textu (nový text obsahuje starý)?
            if previous_single_line in current_text and len(current_text) > len(previous_single_line):
                # Toto je doplňovanie, nie nový text
                logging.debug(f"[DYNAMIC] CycleID={cycle_id}: Text sa doplňuje (obsahuje starý): '{previous_single_line}' → '{current_text}'")
                config.candidate_line_buffer = current_text
                config.previous_single_line_text = current_text
            else:
                # Skutočne nový text
                if len(previous_single_line) >= min_length:
                    # Kontrola, či nie je duplicitný
                    if common_utils.is_new_subtitle(previous_single_line, config.TTS_HISTORY_SIMILARITY_THRESHOLD):
                        logging.info(f"[DYNAMIC] CycleID={cycle_id}: Nový jednoriadkový text, čítam starý: '{previous_single_line}'")
                        speak_text(previous_single_line, cycle_id)
                        csv_logger.log_tts_event(cycle_id, previous_single_line, "TTS_SENT", "Dynamic mode - nový jednoriadkový text")
                        config.tts_history.append(previous_single_line)
                        config.last_processed_dynamic_text = previous_single_line
                    else:
                        logging.debug(f"[DYNAMIC] CycleID={cycle_id}: Starý jednoriadkový text je duplicitný: '{previous_single_line}'")

                # Začni sledovať nový text
                config.candidate_line_buffer = current_text
                config.previous_single_line_text = current_text
                logging.debug(f"[DYNAMIC] CycleID={cycle_id}: Začínam sledovať nový jednoriadkový text: '{current_text}'")

    else:  # Prvý text
        config.candidate_line_buffer = current_text
        config.previous_single_line_text = current_text
        logging.debug(f"[DYNAMIC] CycleID={cycle_id}: Prvý jednoriadkový text: '{current_text}'")


def handle_text_comparison_dynamic(new_text, cycle_id: int):
    logging.info(f"[DYNAMIC] *** HANDLE_TEXT_COMPARISON_DYNAMIC VOLANÁ *** text='{new_text}', cycle_id={cycle_id}")
    logging.debug(f"handle_text_comparison_dynamic: Vstupný text: '{new_text}', CycleID={cycle_id}")
    """
    Logika pre dynamický režim, ktorá overuje stabilitu textu a zabezpečuje čítanie aj prechodných stavov.
    """

    # Spustenie detekcie ľavej strany dynamických titulkov pri prvom volaní
    if not hasattr(config, '_dynamic_mode_initialized') or not config._dynamic_mode_initialized:
        logging.info("[DYNAMIC] Spúšťam detekciu ľavej strany dynamických titulkov")
        subtitle_area_detector.start_dynamic_subtitle_left_detection()
        config._dynamic_mode_initialized = True

    cleaned_lines = common_utils.clean_text(new_text)
    logging.debug(f"handle_text_comparison_dynamic: Vyčistené riadky: {cleaned_lines}")

    # Filtrovanie riadkov - berieme len validné titulkové riadky
    valid_lines = []
    for line in cleaned_lines:
        if validate_text_for_reading(line.strip()):
            valid_lines.append(line.strip())
        else:
            logging.debug(f"[DYNAMIC] Ignorujem nevalidný riadok: '{line.strip()}'")

    logging.debug(f"[DYNAMIC] Validné riadky po filtrovaní: {valid_lines}")

    current_first_line = ""
    current_second_line = ""

    if valid_lines:
        current_first_line = " ".join(valid_lines[0].split())
        if len(valid_lines) > 1:
            current_second_line = " ".join(valid_lines[1].split())

    # --- Logika pre prípad, keď nie je detekovaný žiadny validný text (titulky zmizli) ---
    if not valid_lines:
        # Ak bol v second_line_buffer text, prečítaj ho a vymaz buffer
        if config.second_line_buffer:
            is_valid_last_fragment = validate_text_for_reading(config.second_line_buffer)

            if is_valid_last_fragment and common_utils.is_new_subtitle(config.second_line_buffer, config.TTS_HISTORY_SIMILARITY_THRESHOLD):
                logging.info(f"[DYNAMIC] CycleID={cycle_id}: Oba riadky zmiznuli, čítam posledný fragment druhého riadku: '{config.second_line_buffer}'")
                speak_text(config.second_line_buffer, cycle_id)
                csv_logger.log_tts_event(cycle_id, config.second_line_buffer, "TTS_SENT", "Dynamic mode - posledný fragment druhého riadku po zmiznutí")
                config.tts_history.append(config.second_line_buffer)
                config.last_processed_dynamic_text = config.second_line_buffer
                # Nastav flag, aby sa tento text nečítal znovu ako prvý riadok
                config.recently_read_from_buffer = config.second_line_buffer
            else:
                if not is_valid_last_fragment:
                    logging.debug(f"[DYNAMIC] CycleID={cycle_id}: Posledný fragment druhého riadku nie je validný text: '{config.second_line_buffer}'")
                    csv_logger.log_tts_event(cycle_id, config.second_line_buffer, "TTS_SKIPPED", "Dynamic mode - nevalidný posledný fragment")
                else:
                    logging.debug(f"[DYNAMIC] CycleID={cycle_id}: Posledný fragment druhého riadku už bol prečítaný: '{config.second_line_buffer}'")
                    csv_logger.log_tts_event(cycle_id, config.second_line_buffer, "TTS_SKIPPED", "Dynamic mode - duplicitný posledný fragment")
            config.second_line_buffer = ""

        # Reset všetkých premenných stavu
        config.candidate_line_buffer = ""
        config.previous_first_line = ""
        config.previous_second_line = ""
        config.first_line_stable_count = 0 # Reset stability count
        return

    # --- Logika pre spracovanie jedného validného riadku ---
    if len(valid_lines) == 1:
        # KONTROLA: Ak sa predchádzajúci stav bol dvojriadkový, rozhodni o čítaní druhého riadku
        if config.previous_second_line and config.second_line_buffer:
            # NOVÁ LOGIKA: Kontrola, či jednoriadkový text nie je časť buffera
            current_first_line = valid_lines[0] if valid_lines else ""
            similarity_with_buffer = common_utils.calculate_similarity(current_first_line, config.second_line_buffer) if current_first_line else 0.0

            # Ak je jednoriadkový text podobný buffera (je to jeho časť), NEČÍTAJ z buffera
            if similarity_with_buffer >= 0.7 or (current_first_line and current_first_line in config.second_line_buffer):
                logging.debug(f"[DYNAMIC] CycleID={cycle_id}: Jednoriadkový text '{current_first_line}' je časť buffera '{config.second_line_buffer}' (podobnosť: {similarity_with_buffer:.3f})")
                logging.debug(f"[DYNAMIC] CycleID={cycle_id}: NEČÍTAM z buffera - nechávam dvojriadkovú logiku na neskôr")
                # NEMAZAŤ buffer - nechaj ho pre dvojriadkovú logiku
            else:
                # Prechod z dvojriadkového na jednoriadkový → prečítaj posledný druhý riadok
                is_valid_second = validate_text_for_reading(config.second_line_buffer)
                min_length = getattr(config, 'MIN_DYNAMIC_TEXT_LENGTH', 5)

                if is_valid_second and len(config.second_line_buffer) >= min_length:
                    if common_utils.is_new_subtitle(config.second_line_buffer, config.TTS_HISTORY_SIMILARITY_THRESHOLD):
                        logging.info(f"[DYNAMIC] CycleID={cycle_id}: Prechod na jednoriadkový, čítam posledný druhý riadok: '{config.second_line_buffer}'")
                        speak_text(config.second_line_buffer, cycle_id)
                        csv_logger.log_tts_event(cycle_id, config.second_line_buffer, "TTS_SENT", "Dynamic mode - prechod na jednoriadkový")
                        config.tts_history.append(config.second_line_buffer)
                        config.last_processed_dynamic_text = config.second_line_buffer
                        # Nastav flag, aby sa tento text nečítal znovu ako prvý riadok
                        config.recently_read_from_buffer = config.second_line_buffer
                    else:
                        logging.debug(f"[DYNAMIC] CycleID={cycle_id}: Posledný druhý riadok je duplicitný: '{config.second_line_buffer}'")
                else:
                    logging.debug(f"[DYNAMIC] CycleID={cycle_id}: Posledný druhý riadok nie je validný alebo je príliš krátky: '{config.second_line_buffer}'")

                # Vyčisti buffer
                config.second_line_buffer = ""

            logging.debug(f"[DYNAMIC] CycleID={cycle_id}: Prechod z dvojriadkového na jednoriadkový, preskakujem jednoriadkovú logiku")
        else:
            # NOVÁ LOGIKA: Čítanie jednoriadkových textov v dynamickom režime
            handle_single_line_dynamic_reading(current_first_line, cycle_id)

        # Aktualizácia previous_second_line pre jednoriadkový stav
        config.previous_second_line = ""
        
    # --- Logika pre spracovanie dvoch alebo viacerých validných riadkov ---
    elif len(valid_lines) >= 2:
        # Ak sme mali kandidáta, stal sa z neho prvý riadok, tak ho vymažeme z bufferu
        if config.candidate_line_buffer:
            logging.debug(f"[DYNAMIC] CycleID={cycle_id}: Kandidátsky text sa stal prvým riadkom, vyprázdňujem buffer.")
            config.candidate_line_buffer = ""

        # Logika pre prípad, keď sa druhý riadok presunie na pozíciu prvého riadku
        # Pôvodný second_line_buffer sa NEČÍTA, stáva sa novým current_first_line
        similarity_move = common_utils.calculate_similarity(current_first_line, config.previous_second_line) if config.previous_second_line else 0.0
        line_moved_up = False

        if config.previous_second_line and similarity_move >= config.STABILITY_SIMILARITY_THRESHOLD:
            logging.debug(f"[DYNAMIC] CycleID={cycle_id}: Druhý riadok sa posunul na prvý, ponechávam stability count pre čítanie.")
            csv_logger.log_text_comparison(cycle_id, config.previous_second_line, current_first_line, similarity_move, "INCREMENTAL", "Dynamic mode - riadok sa posunul")
            # NEMAZAŤ second_line_buffer - nech sa nečíta z buffera
            # NERESETTOVAŤ first_line_stable_count - nech sa číta ako stabilný prvý riadok
            line_moved_up = True

        # Kontrola, či aktuálny prvý riadok nie je fragment druhého riadku
        # Zabránime čítaniu fragmentov druhého riadku počas jeho doplňovania
        is_fragment_of_second_line = False

        if config.previous_second_line and not line_moved_up:
            # Ak je aktuálny prvý riadok súčasťou predchádzajúceho druhého riadku
            if config.previous_second_line.startswith(current_first_line) and len(current_first_line) < len(config.previous_second_line):
                is_fragment_of_second_line = True
                logging.debug(f"[DYNAMIC] CycleID={cycle_id}: Prvý riadok '{current_first_line}' je fragment druhého riadku, nečítam")
            # Alebo ak je veľmi podobný ale kratší
            elif similarity_move > 0.8 and len(current_first_line) < len(config.previous_second_line) * 0.8:
                is_fragment_of_second_line = True
                logging.debug(f"[DYNAMIC] CycleID={cycle_id}: Prvý riadok '{current_first_line}' je pravdepodobne fragment druhého riadku (podobnosť: {similarity_move:.3f}), nečítam")
            # Kontrola aj s aktuálnym second_line_buffer
            elif config.second_line_buffer and config.second_line_buffer.startswith(current_first_line) and len(current_first_line) < len(config.second_line_buffer):
                is_fragment_of_second_line = True
                logging.debug(f"[DYNAMIC] CycleID={cycle_id}: Prvý riadok '{current_first_line}' je fragment aktuálneho druhého riadku '{config.second_line_buffer}', nečítam")

        # Spracovanie prvého riadku (overenie stability)
        if current_first_line == config.previous_first_line:
            config.first_line_stable_count += 1
            logging.debug(f"[DYNAMIC] CycleID={cycle_id}: Prvý riadok stabilný, count={config.first_line_stable_count}, text: '{current_first_line}'")
            csv_logger.log_text_comparison(cycle_id, config.previous_first_line, current_first_line, 1.0, "STABLE", f"Dynamic mode - prvý riadok stabilný (count={config.first_line_stable_count})")
        else:
            # Kontrola, či je zmena významná (nie len drobná zmena kvôli OCR chybám)
            similarity = common_utils.calculate_similarity(config.previous_first_line, current_first_line) if config.previous_first_line else 0.0

            if similarity >= 0.8:  # Ak je text veľmi podobný (len drobné OCR chyby), zachovaj stabilitu
                config.first_line_stable_count += 1  # OPRAVA: Zvýš stabilitu pri drobnej zmene
                logging.debug(f"[DYNAMIC] CycleID={cycle_id}: Drobná zmena textu (podobnosť: {similarity:.3f}), zachovávam stabilitu, count={config.first_line_stable_count}")
            else:
                config.first_line_stable_count = 0  # Reset len pri významnej zmene
                logging.debug(f"[DYNAMIC] CycleID={cycle_id}: Prvý riadok zmenený, reset stability count. Starý: '{config.previous_first_line}' → Nový: '{current_first_line}'")
            similarity_first = common_utils.calculate_similarity(config.previous_first_line, current_first_line) if config.previous_first_line else 0.0
            csv_logger.log_text_comparison(cycle_id, config.previous_first_line, current_first_line, similarity_first, "REPLACE", f"Dynamic mode - prvý riadok zmenený")
            config.previous_first_line = current_first_line

            # Ak sa prvý riadok úplne zmenil (nový titulok), reset second_line_buffer
            if similarity_first < 0.3:  # Úplne nový text
                if config.second_line_buffer:
                    logging.debug(f"[DYNAMIC] CycleID={cycle_id}: Nový titulok detekovaný, reset second_line_buffer")
                config.second_line_buffer = ""

        # Validácia textu pred čítaním
        is_valid_text = validate_text_for_reading(current_first_line)

        # Stabilita prvého riadku - vždy 2 cykly podľa požiadavky používateľa
        required_stability = config.DYNAMIC_FIRST_LINE_STABILITY_THRESHOLD

        # DEBUG: Kontrola podmienok pre čítanie
        is_new_subtitle = common_utils.is_new_subtitle(current_first_line, config.TTS_HISTORY_SIMILARITY_THRESHOLD) if current_first_line else False
        logging.debug(f"[DYNAMIC] CycleID={cycle_id}: DEBUG podmienky - is_fragment_of_second_line={is_fragment_of_second_line}, first_line_stable_count={config.first_line_stable_count}, required_stability={required_stability}, current_first_line='{current_first_line}', is_valid_text={is_valid_text}, is_new_subtitle={is_new_subtitle}")

        # Čítanie len ak nie je fragment druhého riadku
        if not is_fragment_of_second_line and config.first_line_stable_count >= required_stability:
            # KONTROLA: Ak sa text už prečítal z second_line_buffer, nečítaj ho znovu
            recently_read_from_buffer = hasattr(config, 'recently_read_from_buffer') and config.recently_read_from_buffer
            if recently_read_from_buffer and current_first_line in config.recently_read_from_buffer:
                logging.debug(f"[DYNAMIC] CycleID={cycle_id}: Text už bol prečítaný z buffera, preskakujem: '{current_first_line}'")
                # Vyčisti flag po použití
                config.recently_read_from_buffer = ""
            elif current_first_line and is_valid_text and is_new_subtitle:
                logging.info(f"[DYNAMIC] CycleID={cycle_id}: Prvý riadok stabilný (req={required_stability}), posielam do TTS: '{current_first_line}'")
                speak_text(current_first_line, cycle_id)
                csv_logger.log_tts_event(cycle_id, current_first_line, "TTS_SENT", f"Dynamic mode - stabilný prvý riadok (req={required_stability})")
                config.tts_history.append(current_first_line)
                config.last_processed_dynamic_text = current_first_line

                # Ak sa riadok posunul z druhého na prvý, vyčisti buffer po prečítaní
                if line_moved_up:
                    config.second_line_buffer = ""
                    logging.debug(f"[DYNAMIC] CycleID={cycle_id}: Riadok bol prečítaný ako prvý, vyprázdňujem second_line_buffer")
            else:
                if not current_first_line:
                    logging.debug(f"[DYNAMIC] CycleID={cycle_id}: Prvý riadok je prázdny, nečítam")
                    csv_logger.log_tts_event(cycle_id, "", "TTS_SKIPPED", "Dynamic mode - prázdny prvý riadok")
                elif not is_valid_text:
                    logging.debug(f"[DYNAMIC] CycleID={cycle_id}: Prvý riadok nie je validný text: '{current_first_line}'")
                    csv_logger.log_tts_event(cycle_id, current_first_line, "TTS_SKIPPED", "Dynamic mode - nevalidný text")
                else:
                    # Detailnejšie logovanie pre debugging duplicitných textov
                    similarity_to_last = common_utils.calculate_similarity(current_first_line, config.last_processed_dynamic_text) if config.last_processed_dynamic_text else 0.0
                    logging.debug(f"[DYNAMIC] CycleID={cycle_id}: Prvý riadok už bol prečítaný: '{current_first_line}' (podobnosť s posledným: {similarity_to_last:.3f})")
                    csv_logger.log_tts_event(cycle_id, current_first_line, "TTS_SKIPPED", f"Dynamic mode - duplicitný prvý riadok (podobnosť: {similarity_to_last:.3f})")
            config.first_line_stable_count = 0 # Reset po prečítaní
        elif is_fragment_of_second_line:
            logging.debug(f"[DYNAMIC] CycleID={cycle_id}: Prvý riadok je fragment druhého riadku, nečítam")
            csv_logger.log_tts_event(cycle_id, current_first_line, "TTS_SKIPPED", "Dynamic mode - fragment druhého riadku")
        else:
            logging.debug(f"[DYNAMIC] CycleID={cycle_id}: Prvý riadok nie je stabilný, nečítam (count={config.first_line_stable_count}, req={required_stability})")
            csv_logger.log_tts_event(cycle_id, current_first_line, "TTS_SKIPPED", f"Dynamic mode - prvý riadok nestabilný (count={config.first_line_stable_count}, req={required_stability})")
            config.current_ocr_interval = config.READING_OCR_INTERVAL # Udržíme rýchly interval

        # Spracovanie druhého riadku (bufferovanie) - NIKDY NEČÍTAŤ, LEN UKLADAŤ
        # PRAVIDLO: Druhý riadok sa NIKDY nečíta keď sú titulky v dvoj- alebo jednoriadkovom stave
        # Číta sa LEN keď: 1) oba riadky zmiznú, 2) prechod na jednoriadkový text

        # Inteligentné aktualizovanie druhého riadku
        if not config.second_line_buffer:
            # Prvý druhý riadok
            config.second_line_buffer = current_second_line
            logging.debug(f"[DYNAMIC] CycleID={cycle_id}: Prvý druhý riadok uložený: '{config.second_line_buffer}'")
        else:
            # Kontrola, či sa text doplňuje alebo mení
            similarity = common_utils.calculate_similarity(config.second_line_buffer, current_second_line)

            if similarity >= 0.8:
                # Vysoká podobnosť - pravdepodobne doplňovanie
                if len(current_second_line) > len(config.second_line_buffer):
                    # Text sa doplňuje
                    config.second_line_buffer = current_second_line
                    logging.debug(f"[DYNAMIC] CycleID={cycle_id}: Druhý riadok sa doplňuje: '{config.second_line_buffer}'")
                else:
                    # Text sa skracuje alebo je rovnaký - ponechaj starší
                    logging.debug(f"[DYNAMIC] CycleID={cycle_id}: Druhý riadok sa nezmenil alebo skrátil, ponechávam: '{config.second_line_buffer}'")
            else:
                # Nízka podobnosť - nový text
                config.second_line_buffer = current_second_line
                logging.debug(f"[DYNAMIC] CycleID={cycle_id}: Nový druhý riadok: '{config.second_line_buffer}'")

        # POZNÁMKA: Logika pre čítanie posledného fragmentu druhého riadku
        # je implementovaná na začiatku funkcie (riadky 89-107) pre prípad zmiznutia všetkých riadkov

        config.current_ocr_interval = config.READING_OCR_INTERVAL # Vrátime sa k normálnemu intervalu

    # --- Aktualizácia predchádzajúcich riadkov pre ďalší cyklus ---
    config.previous_first_line = current_first_line
    config.previous_second_line = current_second_line

    # --- Aktualizácia globálnych premenných pre detekciu ---
    config.current_first_line = current_first_line
    config.current_second_line = current_second_line

def start_dynamic_mode_processing():
    """Reset premenných pre dynamický režim - spracovanie textu je teraz centrálne."""
    # Vyčistenie stavu pri štarte
    config.candidate_line_buffer = ""
    config.second_line_buffer = ""
    config.previous_first_line = ""
    config.previous_second_line = ""
    config.previous_single_line_text = ""
    config.last_processed_dynamic_text = ""
    config.recently_read_from_buffer = ""
    config.first_line_stable_count = 0
    config.current_first_line = ""
    config.current_second_line = ""
    logging.info("Dynamic mode variables reset.")

def stop_dynamic_mode_processing():
    """Reset premenných pre dynamický režim - spracovanie textu je teraz centrálne."""
    # Vyčistenie stavu dynamického režimu
    config.candidate_line_buffer = ""
    config.second_line_buffer = ""
    config.previous_first_line = ""
    config.previous_second_line = ""
    config.previous_single_line_text = ""
    config.last_processed_dynamic_text = ""
    config.recently_read_from_buffer = ""
    config.first_line_stable_count = 0
    config.current_first_line = ""
    config.current_second_line = ""

    logging.info("Dynamic mode variables reset.")
