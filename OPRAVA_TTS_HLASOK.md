# 🔧 OPRAVA TTS HLÁŠOK - PREPÍNANIE JAZYKOV

## 🎯 Problém
Používateľ nahlásil, že napriek nastaveniu slovenčiny pre všetko (aplikácia, čítanie, TTS), sa úvodné a záverečné TTS hlášky čítajú v angličtine namiesto slovenčiny.

## 🔍 Analýza problému

### 1. **Nesprávna inicializácia TTS hlášok**
- `TTSMessageManager` sa inicializoval s predvoleným jazykom skôr, ako sa načítali uložené nastavenia
- Výsledok: TTS hlášky zostali v angličtine aj po načítaní slovenských nastavení

### 2. **Chýbajúca aktualizácia pri zmene jazyka aplikácie**
- Pri zmene jazyka aplikácie sa aktualizovali len GUI texty
- TTS hlášky sa neaktualizovali → zostali v starom jazyku

### 3. **Nesprávna logika jazyka TTS hlášok**
- TTS hlášky sa inicializovali s jazykom čítania (`READING_LANGUAGE`)
- Správne by mali byť v jazyku aplikácie (`APP_LANGUAGE`)

### 4. **Hardcoded texty v kóde**
- V `automatic_mode/automatic_logic.py` boli hardcoded slovenské texty namiesto preložených hlášok

## ✅ Implementované opravy

### 1. **Oprava inicializácie v `main_qt.py`**
```python
# Aktualizuj TTS hlášky na správny jazyk aplikácie po načítaní nastavení
from i18n_manager import update_tts_language
import common_config as config
update_tts_language(getattr(config, 'APP_LANGUAGE', 'cs'))
logging.info(f"🔊 TTS hlášky aktualizované na jazyk: {getattr(config, 'APP_LANGUAGE', 'cs')}")
```

### 2. **Oprava prepínania jazyka aplikácie v `qt_gui.py`**
```python
def _on_app_language_changed(self):
    # ... existujúci kód ...
    
    # OPRAVA: Aktualizuj TTS hlášky na nový jazyk aplikácie
    from i18n_manager import update_tts_language
    update_tts_language(selected_data)
    logging.info(f"🔊 TTS hlášky aktualizované na jazyk aplikácie: {selected_data}")
```

### 3. **Oprava logiky TTS hlášok v `i18n_manager.py`**
```python
class TTSMessageManager:
    def __init__(self):
        # TTS hlášky by mali byť v jazyku aplikácie, nie čítania
        self.current_language = getattr(config, "APP_LANGUAGE", "cs")
        
    def _load_tts_translator(self):
        """Načíta translator pre TTS hlášky v jazyku aplikácie."""
```

### 4. **Odstránenie aktualizácie TTS pri zmene jazyka čítania**
```python
# TTS hlášky sa neaktualizujú pri zmene jazyka čítania
# TTS hlášky sú v jazyku aplikácie, nie čítania
```

### 5. **Oprava hardcoded textov v `automatic_mode/automatic_logic.py`**
```python
# Namiesto: speak_text("Čtud statické titulky.")
tts_msg = get_tts_message("tts_switched_to_static")
speak_text(tts_msg)

# Namiesto: speak_text("Čtud dinamické titulky.")
tts_msg = get_tts_message("tts_switched_dynamic")
speak_text(tts_msg)
```

## 🧪 Testovanie

### Test súbory:
- `test_tts_messages.py` - Test TTS hlášok pre rôzne jazyky
- `test_tts_fix.py` - Test opravy TTS hlášok
- `test_language_switching.py` - Test prepínania jazykov

### Spustenie testov:
```bash
python3 test_tts_messages.py
python3 test_tts_fix.py
python3 test_language_switching.py
```

## 📋 Logika TTS hlášok po oprave

### **Jazyk TTS hlášok = Jazyk aplikácie**
- ✅ Ak je jazyk aplikácie slovenčina → TTS hlášky v slovenčine
- ✅ Ak je jazyk aplikácie čeština → TTS hlášky v češtine
- ✅ Ak je jazyk aplikácie angličtina → TTS hlášky v angličtine

### **Nezávislé od jazyka čítania**
- 📖 Jazyk čítania ovplyvňuje len OCR a čítanie titulkov
- 🔊 TTS hlášky zostávajú v jazyku aplikácie

### **Aktualizácia TTS hlášok**
- 🔄 Pri zmene jazyka aplikácie → TTS hlášky sa aktualizujú
- 📖 Pri zmene jazyka čítania → TTS hlášky sa neaktualizujú
- 🚀 Pri spustení aplikácie → TTS hlášky sa nastavia na jazyk aplikácie

## 🎯 Výsledok

Po implementácii opráv:

1. **✅ TTS hlášky sa inicializujú správne** - v jazyku aplikácie po načítaní nastavení
2. **✅ TTS hlášky sa aktualizujú pri zmene jazyka aplikácie** - okamžite po zmene
3. **✅ Žiadne hardcoded texty** - všetky hlášky cez `get_tts_message()`
4. **✅ Konzistentná logika** - TTS hlášky vždy v jazyku aplikácie

### Používateľský scenár:
- Nastavenie: Slovenčina pre aplikáciu + slovenčina pre čítanie
- Výsledok: **Všetky TTS hlášky v slovenčine** ✅

## 🔄 Inštrukcie pre používateľa

1. **Reštartujte aplikáciu** - aby sa načítali opravy
2. **Skontrolujte nastavenia** - jazyk aplikácie aj čítania
3. **Otestujte čítanie** - úvodné a záverečné hlášky by mali byť v správnom jazyku
4. **Otestujte prepínanie** - zmena jazyka aplikácie by mala okamžite aktualizovať TTS hlášky

## 📝 Poznámky

- Oprava je spätne kompatibilná
- Neovplyvňuje existujúce nastavenia
- Zlepšuje používateľskú skúsenosť
- Odstraňuje nekonzistentnosti v jazykoch
