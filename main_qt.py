# Sp<PERSON>šťač pre Qt PoC verziu GUI

import sys
import os
import logging
import signal
import platform
import time
from PyQt6 import QtWidgets

# 🔧 OPRAVA: Nastavenie správneho working directory pre py2app bundle
if getattr(sys, 'frozen', False):
    # Aplik<PERSON>cia beží ako py2app bundle
    bundle_dir = os.path.dirname(sys.executable)
    resources_dir = os.path.join(bundle_dir, '..', 'Resources')
    resources_dir = os.path.abspath(resources_dir)  # Absolútna cesta

    if os.path.exists(resources_dir):
        os.chdir(resources_dir)
        print(f"🔧 Working directory changed to: {os.getcwd()}")

        # Pridaj Python path pre moduly
        python_lib_dir = os.path.join(resources_dir, 'lib', 'python3.10')
        if os.path.exists(python_lib_dir) and python_lib_dir not in sys.path:
            sys.path.insert(0, python_lib_dir)
            print(f"🔧 Added to Python path: {python_lib_dir}")

        # Pridaj resources_dir do Python path
        if resources_dir not in sys.path:
            sys.path.insert(0, resources_dir)
            print(f"🔧 Added to Python path: {resources_dir}")

        # Pridaj všetky potrebné moduly do Python path
        for module_dir in ['full_automatic_mode', 'automatic_mode', 'dynamic_mode', 'static_mode']:
            module_path = os.path.join(python_lib_dir, module_dir)
            if os.path.exists(module_path) and module_path not in sys.path:
                sys.path.insert(0, module_path)
                print(f"🔧 Added module to Python path: {module_path}")
    else:
        print(f"❌ Resources directory not found: {resources_dir}")
else:
    print(f"🔧 Running in development mode, working directory: {os.getcwd()}")

import common_config as config
from qt_gui import SubtitleReaderQtGUI
from platform_loader import get_tts_manager
import csv_logger
from license_manager import get_license_manager

# Súbor pre uloženie PID
PID_FILE = os.path.join(os.path.dirname(os.path.abspath(__file__)), "ocr_reader.pid")


def setup_logging():
    # jednoduché logovanie do app.log (rešpektuje common_config nastavenia súboru)
    import os

    # Získanie aktuálneho log súboru z konfigurácie
    log_file = config.current_test_log_file

    # 🔧 OPRAVA: Vymazanie starého log súboru pri novom spustení aplikácie (len ak je to app.log)
    if log_file == 'app.log' and os.path.exists(log_file):
        try:
            os.remove(log_file)
            print(f"Starý {log_file} súbor vymazaný.")
        except Exception as e:
            print(f"Chyba pri mazaní starého {log_file}: {e}")

    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.DEBUG)

    # súbor - s UTF-8 encoding pre Windows
    fh = logging.FileHandler(config.current_test_log_file, encoding='utf-8')
    fh.setLevel(logging.DEBUG)
    fh.setFormatter(formatter)
    root_logger.addHandler(fh)

    # konzola - s UTF-8 encoding pre Windows
    import sys
    ch = logging.StreamHandler()
    ch.setLevel(logging.INFO)
    ch.setFormatter(formatter)

    # Pre Windows nastavíme UTF-8 encoding pre StreamHandler
    if sys.platform == "win32":
        try:
            import codecs
            ch.stream = codecs.getwriter('utf-8')(ch.stream.buffer)
        except (AttributeError, OSError):
            # Fallback ak sa nepodarí nastaviť UTF-8
            pass

    root_logger.addHandler(ch)

    logging.info("=== APLIKÁCIA SPUSTENÁ (Qt PoC) ===")


def is_running(pid):
    """Kontroluje, či proces s daným PID beží."""
    try:
        if platform.system() == "Windows":
            # Windows implementácia
            import ctypes
            PROCESS_QUERY_INFORMATION = 0x0400
            handle = ctypes.windll.kernel32.OpenProcess(PROCESS_QUERY_INFORMATION, False, pid)
            if handle:
                ctypes.windll.kernel32.CloseHandle(handle)
                return True
            return False
        else:
            # Unix/Linux/macOS implementácia
            os.kill(pid, 0)  # len otestuje, či proces beží
            return True
    except (OSError, Exception):
        return False


def terminate_process(pid):
    """Ukončí proces s daným PID."""
    try:
        if platform.system() == "Windows":
            # Windows implementácia
            import ctypes
            PROCESS_TERMINATE = 1
            handle = ctypes.windll.kernel32.OpenProcess(PROCESS_TERMINATE, False, pid)
            if handle:
                result = ctypes.windll.kernel32.TerminateProcess(handle, -1)
                ctypes.windll.kernel32.CloseHandle(handle)
                return result != 0
            return False
        else:
            # Unix/Linux/macOS implementácia
            os.kill(pid, signal.SIGTERM)
            return True
    except (OSError, Exception) as e:
        logging.warning(f"⚠️ Nepodarilo sa ukončiť proces {pid}: {e}")
        return False


def ensure_single_instance():
    """Zabezpečí, že beží len jedna inštancia aplikácie."""
    if os.path.exists(PID_FILE):
        try:
            with open(PID_FILE, "r") as f:
                old_pid = int(f.read().strip())

            if is_running(old_pid):
                logging.info(f"🔄 Ukončujem starú inštanciu aplikácie (PID {old_pid})...")
                if terminate_process(old_pid):
                    logging.info(f"✅ Stará inštancia úspešne ukončená")
                    # Počkaj chvíľu, aby sa proces stihol ukončiť
                    time.sleep(1)
                else:
                    logging.warning(f"⚠️ Nepodarilo sa ukončiť starú inštanciu")
            else:
                logging.info("🔍 Starý PID súbor existuje, ale proces už nebeží")
        except (ValueError, IOError) as e:
            logging.warning(f"⚠️ Chyba pri čítaní PID súboru: {e}")

    # Zapíš aktuálny PID
    try:
        with open(PID_FILE, "w") as f:
            f.write(str(os.getpid()))
        logging.info(f"📝 Nová inštancia zaregistrovaná (PID {os.getpid()})")
    except IOError as e:
        logging.error(f"❌ Nepodarilo sa zapísať PID súbor: {e}")


def cleanup_pid_file():
    """Vymaže PID súbor pri ukončení aplikácie."""
    try:
        if os.path.exists(PID_FILE):
            os.remove(PID_FILE)
            logging.info("🗑️ PID súbor vymazaný")
    except OSError as e:
        logging.warning(f"⚠️ Nepodarilo sa vymazať PID súbor: {e}")


def setup_signal_handlers():
    """Nastavenie signal handlerov pre graceful shutdown."""
    def signal_handler(signum, frame):
        logging.info(f"📡 Prijatý signál {signum}, ukončujem aplikáciu...")
        cleanup_pid_file()
        sys.exit(0)

    if platform.system() != "Windows":
        # Unix/Linux/macOS signály
        signal.signal(signal.SIGTERM, signal_handler)
        signal.signal(signal.SIGINT, signal_handler)


def main():
    setup_logging()

    # 🔧 NOVÁ FUNKCIONALITA: Zabezpečenie single instance
    ensure_single_instance()

    # Nastavenie signal handlerov
    setup_signal_handlers()
    setup_logging()

    # Inicializácia licenčného systému
    try:
        license_manager = get_license_manager()
        logging.info("🔐 Licenčný systém inicializovaný")
    except Exception as e:
        logging.error(f"❌ Chyba pri inicializácii licenčného systému: {e}")

    # Načítanie uložených nastavení
    try:
        import settings_manager
        settings_manager.load_settings()
        logging.info("📖 Uložené nastavenia načítané")

        # Aktualizuj translator na správny jazyk aplikácie po načítaní nastavení
        from i18n_manager import update_tts_language, set_app_language
        import common_config as config
        app_lang = getattr(config, 'APP_LANGUAGE', 'cs')

        # Aktualizuj globálny translator na správny jazyk
        set_app_language(app_lang)
        logging.info(f"🌍 Globálny translator aktualizovaný na jazyk: {app_lang}")

        # 🔧 OPRAVA: TTS hlášky sa aktualizujú len na jazyk čítania, nie aplikácie
        reading_lang = getattr(config, 'READING_LANGUAGE', 'cs')
        update_tts_language(reading_lang)
        logging.info(f"🔊 TTS hlášky aktualizované na jazyk čítania: {reading_lang}")
    except Exception as e:
        logging.warning(f"⚠️ Chyba pri načítavaní nastavení: {e}")

    # CSV logging
    try:
        csv_logger.init_csv_logging()
        csv_logger.log_custom_event("APP_START", "Aplikácia spustená (Qt PoC)")
    except Exception as e:
        logging.warning(f"CSV logging init zlyhal: {e}")

    # TTS worker
    try:
        tts_manager = get_tts_manager()
        if tts_manager and hasattr(tts_manager, 'init_tts_worker'):
            tts_manager.init_tts_worker()
        else:
            logging.error("❌ TTS manager not available")
    except Exception as e:
        logging.error(f"Chyba pri spúšťaní TTS workera: {e}")



    # Qt aplikácia a GUI
    app = QtWidgets.QApplication(sys.argv)

    # Cleanup pri ukončení Qt aplikácie
    app.aboutToQuit.connect(cleanup_pid_file)

    def on_closing():
        # kompatibilita s existujúcim on_closing z app_logic – necháme tu len minimum
        try:
            # Cleanup prekladačov
            logging.info("Čistenie prekladačov...")
        except Exception as e:
            logging.error(f"Chyba pri cleanup prekladačov: {e}")

        try:
            from app_logic import on_closing as backend_on_closing
            backend_on_closing()
        except SystemExit:
            pass
        except Exception as e:
            logging.error(f"Chyba pri ukončovaní backendu: {e}")

        # Vyčisti PID súbor pri ukončení
        cleanup_pid_file()

    def toggle_reading():
        try:
            from app_logic import toggle_reading as backend_toggle
            backend_toggle()
        except Exception as e:
            logging.error(f"Chyba v toggle_reading: {e}")

    def set_reading_mode(mode: str):
        try:
            from app_logic import set_reading_mode as backend_set_mode
            backend_set_mode(mode)
        except Exception as e:
            logging.error(f"Chyba v set_reading_mode: {e}")

    gui = SubtitleReaderQtGUI(on_closing, toggle_reading, set_reading_mode)
    config.gui_instance = gui

    # Inicializuj počiatočné UI hodnoty z configu
    gui.update_all_sliders_and_labels()
    gui.update_mode_buttons(getattr(config, "reading_mode", "full_automatic"))

    hotkeys_initialized = False
    # Pokus o nastavenie globálnych hotkeys (pynput) – po vytvorení GUI, aby sa predišlo crashom
    try:
        from platform_loader import get_hotkey_manager
        hotkey_manager = get_hotkey_manager()
        if hotkey_manager:
            setup_pynput_hotkeys = hotkey_manager.setup_pynput_hotkeys
        setup_pynput_hotkeys(root=None)
        logging.info("Globálne klávesové skratky úspešne nastavené pomocou pynput.")
        hotkeys_initialized = True
    except Exception as e:
        logging.warning(f"Pynput hotkeys setup zlyhal: {e}")
        logging.warning("Aktivujem fallback na Qt QShortcut (funguje, keď je okno aktívne)")

    if not hotkeys_initialized:
        try:
            gui._install_qshortcuts()
            logging.info("Qt klávesové skratky sú pripravené – fungujú, keď je aplikácia v popredí.")
        except Exception as e:
            logging.error(f"[Main] _install_qshortcuts zlyhalo: {e}")

    # update status na štart
    gui.update_status(getattr(config, "status_message", "Aplikácia pripravená."))

    try:
        # Spustenie aplikácie
        result = app.exec()
        cleanup_pid_file()
        sys.exit(result)
    except KeyboardInterrupt:
        logging.info("🛑 Aplikácia ukončená používateľom")
        cleanup_pid_file()
        sys.exit(0)



if __name__ == "__main__":
    main()
