#!/usr/bin/env python3
"""
Test script pre overenie funkcionality Subtitle Reader na Windows x86/x64
"""

import sys
import os
import platform
import subprocess
import logging

def setup_logging():
    """Nastavenie logovania"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('test_x86.log', encoding='utf-8')
        ]
    )

def test_system_info():
    """Test systémových informácií"""
    print("🖥️ SYSTÉMOVÉ INFORMÁCIE:")
    print("=" * 50)
    print(f"   Systém: {platform.system()}")
    print(f"   Verzia: {platform.version()}")
    print(f"   Architektúra: {platform.machine()}")
    print(f"   Procesor: {platform.processor()}")
    print(f"   Python: {sys.version}")
    print()
    
    # Kontrola architektúry
    arch = platform.machine().lower()
    if 'amd64' in arch or 'x86_64' in arch:
        print("✅ x86_64 architektúra detekovaná")
        return True
    elif 'x86' in arch or 'i386' in arch or 'i686' in arch:
        print("✅ x86 architektúra detekovaná")
        return True
    else:
        print(f"⚠️ Neočakávaná architektúra: {arch}")
        return False

def test_python_dependencies():
    """Test Python závislostí"""
    print("📦 PYTHON ZÁVISLOSTI:")
    print("=" * 50)
    
    dependencies = [
        ('PyQt6', 'PyQt6'),
        ('pytesseract', 'pytesseract'),
        ('Pillow', 'PIL'),
        ('pyttsx3', 'pyttsx3'),
        ('pywin32', 'win32api'),
        ('WMI', 'wmi'),
        ('pynput', 'pynput'),
        ('mss', 'mss'),
        ('RapidFuzz', 'rapidfuzz'),
        ('requests', 'requests'),
        ('pywebview', 'webview')
    ]
    
    success_count = 0
    for name, import_name in dependencies:
        try:
            __import__(import_name)
            print(f"   ✅ {name}")
            success_count += 1
        except ImportError as e:
            print(f"   ❌ {name} - {e}")
    
    print(f"\n📊 Úspešnosť: {success_count}/{len(dependencies)} ({success_count/len(dependencies)*100:.1f}%)")
    return success_count == len(dependencies)

def test_winrt_dependencies():
    """Test Windows Runtime závislostí"""
    print("\n🎤 WINDOWS RUNTIME (OneCore TTS):")
    print("=" * 50)
    
    winrt_modules = [
        'winrt.windows.foundation',
        'winrt.windows.media.speechsynthesis',
        'winrt.windows.storage.streams'
    ]
    
    success_count = 0
    for module in winrt_modules:
        try:
            __import__(module)
            print(f"   ✅ {module}")
            success_count += 1
        except ImportError as e:
            print(f"   ❌ {module} - {e}")
    
    print(f"\n📊 WinRT úspešnosť: {success_count}/{len(winrt_modules)} ({success_count/len(winrt_modules)*100:.1f}%)")
    return success_count > 0  # Aspoň jeden WinRT modul

def test_tesseract():
    """Test Tesseract OCR"""
    print("\n🔍 TESSERACT OCR:")
    print("=" * 50)
    
    try:
        # Test Tesseract executable
        result = subprocess.run(['tesseract', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            version_line = result.stdout.split('\n')[0]
            print(f"   ✅ Tesseract: {version_line}")
            
            # Test dostupných jazykov
            result = subprocess.run(['tesseract', '--list-langs'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                languages = result.stdout.strip().split('\n')[1:]  # Skip header
                print(f"   📋 Dostupné jazyky: {', '.join(languages)}")
                
                # Kontrola českého jazyka
                if 'ces' in languages:
                    print("   ✅ Český jazyk (ces) dostupný")
                else:
                    print("   ⚠️ Český jazyk (ces) nie je dostupný")
                
                return True
            else:
                print("   ⚠️ Nepodarilo sa získať zoznam jazykov")
                return True  # Tesseract funguje, len jazyky sa nepodarilo získať
        else:
            print(f"   ❌ Tesseract chyba: {result.stderr}")
            return False
            
    except FileNotFoundError:
        print("   ❌ Tesseract nie je nainštalovaný alebo nie je v PATH")
        return False
    except subprocess.TimeoutExpired:
        print("   ❌ Tesseract timeout")
        return False
    except Exception as e:
        print(f"   ❌ Tesseract chyba: {e}")
        return False

def test_tts():
    """Test TTS funkcionality"""
    print("\n🔊 TEXT-TO-SPEECH:")
    print("=" * 50)
    
    # Test pyttsx3
    try:
        import pyttsx3
        engine = pyttsx3.init()
        voices = engine.getProperty('voices')
        print(f"   ✅ pyttsx3: {len(voices)} hlasov dostupných")
        
        # Test OneCore TTS
        try:
            import asyncio
            from winrt.windows.media.speechsynthesis import SpeechSynthesizer
            
            async def test_onecore():
                synthesizer = SpeechSynthesizer()
                voices = synthesizer.all_voices
                return len(voices)
            
            # Spusti async test
            if sys.platform == 'win32':
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                onecore_count = loop.run_until_complete(test_onecore())
                loop.close()
                print(f"   ✅ OneCore TTS: {onecore_count} hlasov dostupných")
            else:
                print("   ⚠️ OneCore TTS: Nie je Windows systém")
                
        except Exception as e:
            print(f"   ⚠️ OneCore TTS: {e}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ TTS chyba: {e}")
        return False

def test_gui():
    """Test GUI frameworku"""
    print("\n🖥️ GUI FRAMEWORK:")
    print("=" * 50)
    
    try:
        from PyQt6 import QtWidgets, QtCore, QtGui
        print("   ✅ PyQt6 importy úspešné")
        
        # Test vytvorenia aplikácie (bez zobrazenia)
        app = QtWidgets.QApplication.instance()
        if app is None:
            app = QtWidgets.QApplication([])
        
        # Test vytvorenia okna
        window = QtWidgets.QWidget()
        window.setWindowTitle("Test")
        print("   ✅ Test okno vytvorené")
        
        # Vyčistenie
        window.close()
        
        return True
        
    except Exception as e:
        print(f"   ❌ GUI chyba: {e}")
        return False

def test_application_files():
    """Test prítomnosti súborov aplikácie"""
    print("\n📁 SÚBORY APLIKÁCIE:")
    print("=" * 50)
    
    required_files = [
        'main_qt.py',
        'common_config.py',
        'platform_windows.py',
        'onecore_tts_provider.py',
        'language_detector.py',
        'i18n_manager.py'
    ]
    
    success_count = 0
    for file in required_files:
        if os.path.exists(file):
            print(f"   ✅ {file}")
            success_count += 1
        else:
            print(f"   ❌ {file} - CHÝBA")
    
    print(f"\n📊 Súbory: {success_count}/{len(required_files)} ({success_count/len(required_files)*100:.1f}%)")
    return success_count == len(required_files)

def main():
    """Hlavná funkcia"""
    setup_logging()
    
    print("=" * 60)
    print("  Subtitle Reader - x86 Installation Test")
    print("=" * 60)
    print()
    
    # Spusti všetky testy
    tests = [
        ("Systémové informácie", test_system_info),
        ("Python závislosti", test_python_dependencies),
        ("WinRT závislosti", test_winrt_dependencies),
        ("Tesseract OCR", test_tesseract),
        ("Text-to-Speech", test_tts),
        ("GUI Framework", test_gui),
        ("Súbory aplikácie", test_application_files)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"   ❌ {test_name} - CHYBA: {e}")
            results.append((test_name, False))
    
    # Súhrn výsledkov
    print("\n" + "=" * 60)
    print("📊 SÚHRN TESTOV:")
    print("=" * 60)
    
    success_count = 0
    for test_name, result in results:
        status = "✅ ÚSPECH" if result else "❌ CHYBA"
        print(f"   {status}: {test_name}")
        if result:
            success_count += 1
    
    print(f"\n🎯 Celková úspešnosť: {success_count}/{len(results)} ({success_count/len(results)*100:.1f}%)")
    
    if success_count >= len(results) * 0.8:  # 80% úspešnosť
        print("\n🎉 INŠTALÁCIA JE FUNKČNÁ!")
        print("💡 Môžete spustiť aplikáciu pomocou: run_app_x86.bat")
    else:
        print("\n⚠️ INŠTALÁCIA MÁ PROBLÉMY")
        print("🔧 Prosím opravte chyby uvedené vyššie")
    
    print("=" * 60)
    
    # Ulož výsledky do súboru
    with open('test_results_x86.txt', 'w', encoding='utf-8') as f:
        f.write(f"Subtitle Reader x86 Test Results\n")
        f.write(f"================================\n\n")
        f.write(f"System: {platform.system()} {platform.version()}\n")
        f.write(f"Architecture: {platform.machine()}\n")
        f.write(f"Python: {sys.version}\n\n")
        
        for test_name, result in results:
            f.write(f"{'✅' if result else '❌'} {test_name}\n")
        
        f.write(f"\nOverall Success: {success_count}/{len(results)} ({success_count/len(results)*100:.1f}%)\n")

if __name__ == "__main__":
    main()
    input("\nStlačte Enter pre ukončenie...")
